import json
import logging
from pysqlitecipher import sqlitewrapper


class RecordDb(object):

    def __init__(self, dbName, password, tableName='Test_Records'):
        self.dbName = dbName
        self.password = password
        self.tableName = tableName
        self.dbHandle = None
        self.tableDesc = [["DEV_ID", "TEXT"], ["NAME", "TEXT"], ["FREQ_L", "TEXT"], ["FREQ_R", "TEXT"],
                          ["THD_L", "TEXT"], ["THD_R", "TEXT"], ["MIC_L", "TEXT"], ["MIC_R", "TEXT"],
                          ["Ring_Dev", "TEXT"], ["MESSAGE", "TEXT"], ["TOTAL_RESULT", "TEXT"], ["DATE", "TEXT"],
                          ["UPLOAD", "TEXT"]]
        self.colList = [k[0] for k in self.tableDesc]

    def initDatabase(self):
        try:
            self.dbHandle = sqlitewrapper.SqliteCipher(dataBasePath=self.dbName, checkSameThread=False, password=self.password)
            tableName, encTableName, secured = self.dbHandle.checkIfTableIsSecured(self.tableName, raiseError=False)
            # print(tableName, encTableName, secured)
            if not encTableName:
                self.deleteTable(self.tableName)
                self.dbHandle.createTable(self.tableName, self.tableDesc, makeSecure=True, commit=True)
                tableName, encTableName, secured = self.dbHandle.checkIfTableIsSecured(self.tableName, raiseError=False)

            if tableName != self.tableName or not encTableName or not secured:
                return False

            colNames = self.dbHandle.describeTable(self.tableName)
            colNames.pop(0)
            for col in colNames:
                col.pop()
            if colNames != self.tableDesc:
                logging.info('Record database cols not valid!')
                self.deleteTable(encTableName)
                self.dbHandle.createTable(self.tableName, self.tableDesc, makeSecure=True, commit=True)
                tableName, encTableName, secured = self.dbHandle.checkIfTableIsSecured(self.tableName, raiseError=False)
                if tableName != self.tableName or not encTableName or not secured:
                    logging.info('Record database table not valid!')
                    return False
            else:
                logging.info('Record database Valid check passed!')

            logging.info('Record database Init successfully!')
            return True
        except Exception as e:
            logging.exception(e)
            return False

    def deleteTable(self, tableName):
        if not self.dbHandle:
            return

        logging.info('Delete table: %s', tableName)
        stringToExe = """DROP TABLE IF EXISTS '{}';""".format(tableName)
        self.dbHandle.sqlObj.execute(stringToExe)
        stringToExe = """DELETE from tableNames where tableName='{}';""".format(tableName)
        self.dbHandle.sqlObj.execute(stringToExe)
        self.dbHandle.sqlObj.commit()

    def addRecordData(self, recordDict):
        if not self.dbHandle:
            return False

        record = [recordDict.get(k[0], '') for k in self.tableDesc]
        self.dbHandle.insertIntoTable(self.tableName, record, commit=True)
        return True

    def getAllRecords(self, colIdx=None, colValue=None, omitID=True):
        if not self.dbHandle:
            return []
        cols, records = self.dbHandle.getDataFromTable(self.tableName, raiseConversionError=False , omitID=omitID)
        if colIdx is not None and colValue is not None:
            return [d for d in records if d[colIdx] == colValue]
        else:
            return records

    def clearRecords(self):
        if not self.dbHandle:
            return False
        logging.info('Clear Record table.')
        tableName, encTableName, secured = self.dbHandle.checkIfTableIsSecured(self.tableName, raiseError=False)
        if not encTableName:
            return False
        stringToExe = """DELETE from '{}';""".format(encTableName)
        self.dbHandle.sqlObj.execute(stringToExe)
        self.dbHandle.sqlObj.commit()
        return True

    def updateUploadState(self, recordId, state):
        if not self.dbHandle:
            return False

        try:
            self.dbHandle.updateInTable(self.tableName, recordId, 'UPLOAD', str(state))
            return True
        except Exception as e:
            return False

    '''
    def getDataWithCondition(self, colName, colValue, omitID=True):

        tableName, encTableName, secured = self.dbHandle.checkIfTableIsSecured(self.tableName, raiseError=False)
        tableDescription = self.dbHandle.describeTable(tableName)

        print(tableDescription)
        colFound = False
        colData = None
        for i in tableDescription:
            if i[0] == colName:
                colFound = True
                colData = i[1]
                colName = i[2]
                break
        if not colFound:
            logging.error('Can not found col: %s in local DB', colName)
            return []
        colValue = str(colValue)
        if secured:
            colValue = self.dbHandle.encryptor(colValue)

        usedName = encTableName if secured else tableName

        stringToExe = """SELECT * FROM '{}' where "{}"='{}';""".format(encTableName, colName, colValue)
        print(stringToExe)
        result = self.dbHandle.sqlObj.execute(stringToExe)
        print(result)

        valueList = []
        for row in result:
            tempList = []
            for i, j in zip(row, tableDescription):
                if secured:
                    i = self.dbHandle.decryptor(i)
                if j[1] == "INT":
                    try:
                        i = int(i)
                    except TypeError:
                        i = str(i)
                elif j[1] == "REAL":
                    try:
                        i = float(i)
                    except TypeError:
                        i = str(i)
                elif j[1] == "TEXT":
                    i = str(i)
                tempList.append(i)
            valueList.append(tempList)

        if omitID:
            newValueList = []
            for i in valueList:
                newValueList.append(i[1:])
            valueList = newValueList

        return valueList
    '''


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    '''
    tableName = 'Test_Records'
    colList = [["rollno", "INT"], ["name", "TEXT"], ]
    obj = sqlitewrapper.SqliteCipher(dataBasePath="record.db", checkSameThread=False, password='yinkman2015')
    if not obj.checkTableExist(tableName):
        obj.createTable(tableName, colList, makeSecure=False, commit=True)
    '''

    dbName = r'C:\Users\<USER>\AppData\Local\Ls900TestTool\Records\Records.db'
    passwd = 'ls900_records'
    db = RecordDb(dbName, passwd)
    if not db.intDatabase():
        logging.error('Init reocord db error!')
        exit(0)

    # db.clearRecords()

    # record = ['1231', '', '', '', 'False']
    # db.addReocrdData(record)
    records = db.getAllRecords()
    print(records)
    header = [k[0] for k in db.tableDesc]
    print(header)
    from CsvFileOps import CsvFileOps
    fileName = 'export_data.csv'
    CsvFileOps.saveDataToCsvFile(records, header, fileName)
