import sys
import logging
from PySide6 import QtGui, QtWidgets
from ui_LoginDialog import Ui_UserLoginDialog
from BaseSubMainWindow import BaseSubDialog
from ServerRequest import ServerRequest
from ConfigParser import ConfigParser


class UserLogIn(BaseSubDialog):

    ancTest = None
    srvHandle = None
    cancel = False
    user = ''

    def __init__(self, config=None, parent=None):
        super(UserLogIn, self).__init__()
        self.ui = Ui_UserLoginDialog()
        self.ui.setupUi(self)
        self.bindEvent()

        self.ui.lineEditPort.setValidator(QtGui.QIntValidator())

        self.userConfig = config
        if self.userConfig is not None:
            self.ui.lineEditUserName.setText(self.userConfig.get('Server', 'user'))
            self.ui.lineEditPassword.setText(self.userConfig.get('Server', 'passwd'))
            self.ui.lineEditServer.setText(self.userConfig.get('Server', 'ip'))
            self.ui.lineEditPort.setText(self.userConfig.get('Server', 'port'))

    def bindEvent(self):
        self.ui.btnLogin.clicked.connect(self.userLogin)
        self.ui.btnClose.clicked.connect(self.closeWindows)

    def userLogin(self):
        self.user = self.ui.lineEditUserName.text()
        passwd = self.ui.lineEditPassword.text()
        server = self.ui.lineEditServer.text()
        port = self.ui.lineEditPort.text()

        self.cancel = False

        logging.info('Login: user = %s, passwd = %s, server = %s:%s', self.user, passwd, server, port)
        self.updateConfig(self.user, passwd, server, port)

        self.srvHandle = ServerRequest(server, port)
        ret, msg = self.srvHandle.userLogin(self.user, passwd)
        if not ret:
            logging.error('Failed to Login to %s:%s, msg: %s', server, port, msg)
            self.srvHandle = None
            self.showMessage('Login Failed！')
            return None

        logging.info('Login to %s:%s', server, port)
        # self.showMessage('Login Success！', msgType=QtWidgets.QMessageBox.Information)
        self.close()
        return self.srvHandle

    def closeWindows(self):
        logging.info('Close Login Window.')
        self.close()
        self.cancel = True

    def closeEvent(self, event):
        logging.info('Login window Close Event.')

    def updateConfig(self, user, passwd, server, port):
        if self.userConfig is not None:
            self.userConfig.set('Server', 'user', user)
            self.userConfig.set('Server', 'passwd', passwd)
            self.userConfig.set('Server', 'server', server)
            self.userConfig.set('Server', 'port', port)
            ConfigParser.saveConfigFile(self.userConfig, 'Config.ini')


if __name__ == "__main__":
    app = QtWidgets.QApplication(sys.argv)
    loginWin = UserLogIn()
    loginWin.show()
    sys.exit(app.exec_())

