import time
import logging
import pymysql
from datetime import datetime


class BaseMysqlDb(object):

    def __init__(self, dbName, host, user, passwd):
        self.dbName = dbName
        self.password = passwd
        self.dbHost = host
        self.dbUser = user
        self.dbHandle = None
        self.dbCursor = None

    def __del__(self):
        if self.dbCursor:
            self.dbCursor.close()
        if self.dbHandle:
            self.dbHandle.close()

    def initialize(self):
        try:
            self.dbHandle = pymysql.connect(host=self.dbHost, user=self.dbUser, password=self.password,
                                            database=self.dbName)
            self.dbCursor = self.dbHandle.cursor(cursor=pymysql.cursors.DictCursor)

            if not self.dbHandle or not self.dbCursor:
                return False

            return True
        except Exception as e:
            # logging.exception(e)
            return False

    def getVersion(self):
        self.dbCursor.execute("SELECT VERSION()")
        return self.getOneData()['VERSION()']

    def getOneData(self):
        data = self.dbCursor.fetchone()
        return data

    def showTables(self):
        tables = self.executeSql("show tables;")
        for table in tables:
            logging.info('Table Name: %s, Cols: %s', table['Tables_in_ls900_production'], self.describeTable(table['Tables_in_ls900_production']))

    def createTable(self, tableName, attrdict, constraint="PRIMARY KEY(`id`)"):
        if self.isExistTable(tableName):
            logging.warning("%s is exit" % tableName)
            return False
        sql = ''
        sql_mid = '`id` int NOT NULL AUTO_INCREMENT,'
        for attr, value in attrdict.items():
            sql_mid = sql_mid + '`' + attr + '`' + ' ' + value + ','
        sql = sql + 'CREATE TABLE IF NOT EXISTS %s (' % tableName
        sql = sql + sql_mid
        sql = sql + constraint
        sql = sql + ') ENGINE=InnoDB DEFAULT CHARSET=utf8'
        logging.info('CreateTable:' + sql)
        return self.executeCommit(sql)

    def executeSql(self, sql=''):
        try:
            logging.debug('SQL exec: %s', sql)
            self.dbCursor.execute(sql)
            records = self.dbCursor.fetchall()
            return records
        except pymysql.Error as e:
            error = 'SQL Failed (%s): %s' % (e.args[0], e.args[1])
            logging.error(error)
            return None

    def executeCommit(self, sql=''):
        try:
            self.dbCursor.execute(sql)
            self.dbHandle.commit()
            return True
        except pymysql.Error as e:
            self.dbHandle.rollback()
            error = 'SQL Failed (%s): %s' % (e.args[0], e.args[1])
            logging.error("error: %s", error)
            return False

    def insert(self, tableName, params):
        key = []
        value = []
        for k, v in params.items():
            key.append('`' + k + '`')
            value.append(v)
        sql = 'insert into %s' % tableName
        # sql = sql + attrs_sql + values_sql
        sql = sql + '(' + ','.join(key) + ')' + ' values ' + str(tuple(value))
        logging.debug('_insert: %s', sql)
        return self.executeCommit(sql)

    def select(self, tableName, cond_dict=None, order='', fields='*'):
        if cond_dict is None:
            cond_dict = {}
        consql = ' '
        if cond_dict != {}:
            for k, v in cond_dict.items():
                consql = consql + '`' + k + '`' + '=' + '"' + v + '"' + ' and'
        consql = consql + ' 1=1 '
        if fields == "*":
            sql = 'select * from %s where ' % tableName
        else:
            if isinstance(fields, list):
                fields = ",".join(fields)
                sql = 'select %s from %s where ' % (fields, tableName)
            else:
                logging.error("fields input error, please input list fields.")
                return False
        sql = sql + consql + order
        logging.debug('select: %s', sql)
        return self.executeSql(sql)

    def insertMany(self, table, attrs, values):
        values_sql = ['%s' for v in attrs]
        attrs_sql = '(' + ','.join(attrs) + ')'
        values_sql = ' values(' + ','.join(values_sql) + ')'
        sql = 'insert into %s' % table
        sql = sql + attrs_sql + values_sql
        logging.debug('insertMany: %s', sql)
        try:
            for i in range(0, len(values), 20000):
                self.dbCursor.executemany(sql, values[i:i + 20000])
                self.dbHandle.commit()
                return True
        except pymysql.Error as e:
            self.dbHandle.rollback()
            error = 'InsertMany failed (%s): %s' % (e.args[0], e.args[1])
            logging.error(error)
            return False

    def delete(self, tableName, cond_dict):
        consql = ' '
        if cond_dict != '':
            for k, v in cond_dict.items():
                if isinstance(v, str):
                    v = "\'" + v + "\'"
                consql = consql + tableName + "." + k + '=' + v + ' and '
        consql = consql + ' 1=1 '
        sql = "DELETE FROM %s where%s" % (tableName, consql)
        logging.debug(sql)
        return self.executeCommit(sql)

    def update(self, tableName, attrs_dict, cond_dict):
        attrs_list = []
        consql = ' '
        for tmpkey, tmpvalue in attrs_dict.items():
            attrs_list.append("`" + tmpkey + "`" + "=" + "\'" + tmpvalue + "\'")
        attrs_sql = ",".join(attrs_list)
        logging.debug("attrs_sql: %s", attrs_sql)
        if cond_dict != '':
            for k, v in cond_dict.items():
                if isinstance(v, str):
                    v = "\'" + v + "\'"
                consql = consql + "`" + tableName + "`." + "`" + k + "`" + '=' + v + ' and '
        consql = consql + ' 1=1 '
        sql = "UPDATE %s SET %s where%s" % (tableName, attrs_sql, consql)
        logging.debug(sql)
        return self.executeCommit(sql)

    def dropTable(self, tableName):
        sql = "DROP TABLE  %s" % tableName
        return self.executeCommit(sql)

    def deleteTable(self, tableName):
        sql = "DELETE FROM %s" % tableName
        return self.executeCommit(sql)

    def isExistTable(self, tableName):
        sql = "select * from %s" % tableName
        return self.executeCommit(sql)

    def describeTable(self, tableName):
        sql = "desc %s;" % tableName
        result = self.executeSql(sql)
        if not result:
            return []
        colList = [description['Field'] for description in result]
        return colList


class DevTestMysqlDb(BaseMysqlDb):
    recordTableCols = {"DEV_ID": "varchar(64)", "NAME": "varchar(256)", "FREQ_L": "varchar(64)",
                       "FREQ_R": "varchar(64)", "THD_L": "varchar(64)", "THD_R": "varchar(64)", "MIC_L": "varchar(64)",
                       "MIC_R": "varchar(64)", "Ring_Dev": "varchar(64)", "MESSAGE": "varchar(1024)",
                       "TOTAL_RESULT": "varchar(64)", "DATE": "datetime"}

    def __init__(self, dbName='YM_Production', tableName='Test_Records', host='************', user='ls900', passwd='ls9002024', tbDesc=None):
        super().__init__(dbName, host, user, passwd)
        if tbDesc:
            self.recordTableCols = tbDesc
        self.colList = list(self.recordTableCols.keys())
        self.recordTableName = tableName

    def checkTableValid(self, tableName, tableCols):
        validColList = list(tableCols.keys())
        if self.isExistTable(tableName):
            curCols = self.describeTable(tableName)
            if set(validColList).issubset(set(curCols)):
                return True
            else:
                logging.warning('%s: Invalid Tables desc, re-create it.', tableName)
                self.dropTable(tableName)

        return self.createTable(tableName, tableCols)

    def checkConnection(self):
        try:
            self.dbHandle.ping()
            return True
        except Exception as e:
            # logging.exception(e)
            return False

    def initDatabase(self):
        if not self.initialize():
            logging.error('Ls900 DB Init Failed.')
            return False
        return self.checkTableValid(self.recordTableName, self.recordTableCols)

    def addRecordData(self, record):
        if not self.dbHandle:
            return False

        return self.insert(self.recordTableName, record)

    def checkAndUploadRecordData(self, record):
        if not self.dbHandle:
            return False

        conDict = {key: value for key, value in record.items() if key == 'DATE'}
        validColList = list(self.recordTableCols.keys())
        records = self.select(self.recordTableName, cond_dict=conDict, fields=validColList)
        # logging.info('cnt: %d, %s', len(records), records)

        if len(records) == 0:
            return self.insert(self.recordTableName, record)

        for data in records:
            data['DATE'] = str(data['DATE'])
            non_empty_dict1 = {k: v for k, v in record.items() if v}
            non_empty_dict2 = {k: v for k, v in data.items() if v}
            if non_empty_dict1 == non_empty_dict2:
                # logging.info('Record already in remote server.')
                return True

        return self.insert(self.recordTableName, record)

    def getAllRecords(self, cols=None):
        if not self.dbHandle:
            return []
        if cols:
            validColList = cols
        else:
            validColList = list(self.recordTableCols.keys())
        # validColList[-1] = "DATE_FORMAT(date, '%Y-%m-%d %H:%i:%s')"
        records = self.select(self.recordTableName, fields=validColList)
        recordList = []
        for record in records:
            record['DATE'] = str(record['DATE'])
            recordList.append(list(record.values()))
        return recordList

    def clearRecords(self):
        if not self.dbHandle:
            return False
        logging.info('Clear Record table.')
        return self.deleteTable(self.recordTableName)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    '''
    remoteHost = '************'
    dbUser = 'ls900'
    dbPasswd = 'ls9002024'
    dataBaseName = 'ls900_production'
    '''
    db = DevTestMysqlDb()
    if db.initDatabase():
        logging.info('DB init OK!')
    else:
        logging.error('DB Init failed!')
        exit(0)

    version = db.getVersion()
    logging.info(version)
    db.showTables()

    # db.dropTable('Ls900_Test_Records')
    # db.dropTable('Test_Records')

    '''
    tmpTableName = 'Test_Records'

    db.createTable(tmpTableName, db.recordTableCols)
    db.showTables()

    if db.checkTableValid(tmpTableName, db.recordTableCols):
        logging.info('Check Record table passed.')
    else:
        logging.error('Check Record table failed.')
        exit(0)
    '''

    allRecords = db.getAllRecords()
    logging.info(allRecords)

    hpID = 'QM0110240500001'

    if db.checkHpIsPacked(hpID):
        logging.info('HP: %s already packed.', hpID)
        exit(0)

    if db.updatePackState(hpID):
        logging.info('Update HP: %s pack state.', hpID)
    else:
        logging.error('Update HP: %s pack state error.', hpID)
        exit(0)

    # db.clearRecords()
