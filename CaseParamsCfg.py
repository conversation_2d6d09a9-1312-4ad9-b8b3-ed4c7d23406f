import ctypes
import logging


# Define the C structures in Python
class WdrcBandCfg(ctypes.Structure):
    _fields_ = [
        ("frequency", ctypes.c_int32),
        ("attack_time", ctypes.c_int32),
        ("release_time", ctypes.c_int32),
        ("limit_threshold", ctypes.c_int32),
        ("limit_knee", ctypes.c_int32),
        ("small_spl", ctypes.c_int32),
        ("small_spl_gain", ctypes.c_int32),
        ("medium_spl", ctypes.c_int32),
        ("medium_spl_gain", ctypes.c_int32),
        ("large_spl", ctypes.c_int32),
        ("large_spl_gain", ctypes.c_int32)
    ]


class WdrcCfg(ctypes.Structure):
    _fields_ = [
        ("wdrc_state", ctypes.c_int32),
        ("wdrc_all_band_gain", ctypes.c_int32),
        ("band_cfg", WdrcBandCfg * 20)  # Array of 20 WdrcBandCfg structures
    ]


class CaseParams(ctypes.Structure):
    _fields_ = [
        ("ym_algo_state", ctypes.c_int32),
        ("in_gain", ctypes.c_int32),
        ("out_gain", ctypes.c_int32),
        ("ns_state", ctypes.c_int32),
        ("ns_mode", ctypes.c_int32),
        ("beam_state", ctypes.c_int32),
        ("beam_v_angle", ctypes.c_int32),
        ("beam_h_angle", ctypes.c_int32),
        ("beam_mode", ctypes.c_int32),
        ("beam_out_id", ctypes.c_int32),
        ("ssl_state", ctypes.c_int32),
        ("limit_state", ctypes.c_int32),
        ("limit_threshold", ctypes.c_int32),
        ("limit_knee", ctypes.c_int32),
        ("lea_format", ctypes.c_int32),
        ("wdrc_cfg", WdrcCfg)
    ]


wdrc_band_cfg_size = ctypes.sizeof(WdrcBandCfg)
wdrc_cfg_size = ctypes.sizeof(WdrcCfg)
AidsParamsSize = ctypes.sizeof(CaseParams)


# Function to convert the structure to a Python dictionary
def structure_to_dict(struct):
    result = {}
    for field_name, _ in struct._fields_:
        value = getattr(struct, field_name)

        # Handle nested structures and arrays
        if isinstance(value, ctypes.Structure):
            result[field_name] = structure_to_dict(value)
        elif isinstance(value, ctypes.Array):
            result[field_name] = [structure_to_dict(item) if isinstance(item, ctypes.Structure)
                                  else item for item in value]
        else:
            result[field_name] = value
    return result

def json_to_aids_params(json_data):
    # 创建空的 AidsParams 结构体实例
    aids_params = CaseParams()
    
    # 填充顶层字段
    for field_name, _ in CaseParams._fields_:
        if field_name == 'wdrc_cfg':
            continue  # 跳过复杂结构，后面单独处理
        if field_name in json_data:
            setattr(aids_params, field_name, json_data[field_name])
    
    # 填充 WDRC 配置
    if 'wdrc_cfg' in json_data:
        wdrc_dict = json_data['wdrc_cfg']
        aids_params.wdrc_cfg.wdrc_state = wdrc_dict.get('wdrc_state', 0)
        aids_params.wdrc_cfg.wdrc_all_band_gain = wdrc_dict.get('wdrc_all_band_gain', 0)
        
        # 填充每个频带配置
        if 'band_cfg' in wdrc_dict:
            band_list = wdrc_dict['band_cfg']
            
            for i, band_dict in enumerate(band_list):
                if i >= 20:  # 确保不超过数组大小
                    break
                    
                # 填充频带结构体
                band = aids_params.wdrc_cfg.band_cfg[i]
                for field_name, _ in WdrcBandCfg._fields_:
                    if field_name in band_dict:
                        setattr(band, field_name, band_dict[field_name])
    
    return aids_params


def showAidsParams(aidsParams):
    # 打印顶层字段
    print("YmCfg Structure:")
    print(f"  ym_algo_state: {aidsParams.ym_algo_state}")
    print(f"  in_gain: {aidsParams.in_gain}")
    print(f"  out_gain: {aidsParams.out_gain}")
    print(f"  ns_state: {aidsParams.ns_state}")
    print(f"  ns_mode: {aidsParams.ns_mode}")
    print(f"  beam_state: {aidsParams.beam_state}")
    print(f"  beam_angle: {aidsParams.beam_angle}")
    print(f"  freqshift_state: {aidsParams.freqshift_state}")
    print(f"  freqshift_level: {aidsParams.freqshift_level}")
    print(f"  afc_state: {aidsParams.afc_state}")
    print(f"  afc_mode: {aidsParams.afc_mode}")
    print(f"  afc_speech_level: {aidsParams.afc_speech_level}")
    print(f"  afc_delay: {aidsParams.afc_delay}")
    print(f"  afc_filter_len: {aidsParams.afc_filter_len}")
    print(f"  limit_state: {aidsParams.limit_state}")
    print(f"  limit_threshold: {aidsParams.limit_threshold}")
    print(f"  limit_knee: {aidsParams.limit_knee}")

    # 打印 WDRC 配置
    print("\nWDRC Configuration:")
    print(f"  wdrc_state: {aidsParams.wdrc_cfg.wdrc_state}")
    print(f"  wdrc_all_band_gain: {aidsParams.wdrc_cfg.wdrc_all_band_gain}")

    # 打印每个频带配置
    print("\nWDRC Band Configurations:")
    for i in range(20):
        band = aidsParams.wdrc_cfg.band_cfg[i]
        print(f"\nBand {i}:")
        print(f"  frequency: {band.frequency}")
        print(f"  attack_time: {band.attack_time}")
        print(f"  release_time: {band.release_time}")
        print(f"  limit_threshold: {band.limit_threshold}")
        print(f"  limit_knee: {band.limit_knee}")
        print(f"  small_spl: {band.small_spl}")
        print(f"  small_spl_gain: {band.small_spl_gain}")
        print(f"  medium_spl: {band.medium_spl}")
        print(f"  medium_spl_gain: {band.medium_spl_gain}")
        print(f"  large_spl: {band.large_spl}")
        print(f"  large_spl_gain: {band.large_spl_gain}")
