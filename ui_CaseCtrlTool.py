# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'CaseCtrlTool.ui'
##
## Created by: Qt User Interface Compiler version 6.6.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QAction, QBrush, QColor, QConicalGradient,
    QCursor, QFont, QFontDatabase, QGradient,
    QIcon, QImage, QKeySequence, QLinearGradient,
    QPainter, QPalette, QPixmap, QRadialGradient,
    QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QDoubleSpinBox,
    QFrame, QGridLayout, QGroupBox, QHBoxLayout,
    QLabel, QLayout, QLineEdit, QMainWindow,
    QMenu, QMenuBar, QPushButton, QScrollArea,
    QSizePolicy, QSpinBox, QStatusBar, QTabWidget,
    QTextBrowser, QVBoxLayout, QWidget)

from DelayedSlider import DelayedSlider

class Ui_CaseCtrlTool(object):
    def setupUi(self, CaseCtrlTool):
        if not CaseCtrlTool.objectName():
            CaseCtrlTool.setObjectName(u"CaseCtrlTool")
        CaseCtrlTool.setWindowModality(Qt.WindowModal)
        CaseCtrlTool.setEnabled(True)
        CaseCtrlTool.resize(998, 731)
        CaseCtrlTool.setMinimumSize(QSize(0, 0))
        CaseCtrlTool.setMaximumSize(QSize(16777215, 16777215))
        font = QFont()
        font.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        CaseCtrlTool.setFont(font)
        CaseCtrlTool.setTabShape(QTabWidget.Rounded)
        CaseCtrlTool.setDockOptions(QMainWindow.AllowTabbedDocks|QMainWindow.AnimatedDocks)
        self.actionMenu = QAction(CaseCtrlTool)
        self.actionMenu.setObjectName(u"actionMenu")
        font1 = QFont()
        font1.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        font1.setPointSize(10)
        self.actionMenu.setFont(font1)
        self.actionAbout = QAction(CaseCtrlTool)
        self.actionAbout.setObjectName(u"actionAbout")
        self.actionAbout.setFont(font1)
        self.actionUploadRecords = QAction(CaseCtrlTool)
        self.actionUploadRecords.setObjectName(u"actionUploadRecords")
        self.actionUploadRecords.setFont(font1)
        self.actionClearLocalDb = QAction(CaseCtrlTool)
        self.actionClearLocalDb.setObjectName(u"actionClearLocalDb")
        self.actionExportRecords = QAction(CaseCtrlTool)
        self.actionExportRecords.setObjectName(u"actionExportRecords")
        self.actionStyleAuto = QAction(CaseCtrlTool)
        self.actionStyleAuto.setObjectName(u"actionStyleAuto")
        self.actionStyleDark = QAction(CaseCtrlTool)
        self.actionStyleDark.setObjectName(u"actionStyleDark")
        self.actionStyleLight = QAction(CaseCtrlTool)
        self.actionStyleLight.setObjectName(u"actionStyleLight")
        self.actionStyleLight.setFont(font1)
        self.actionDebugTool = QAction(CaseCtrlTool)
        self.actionDebugTool.setObjectName(u"actionDebugTool")
        self.actionExport = QAction(CaseCtrlTool)
        self.actionExport.setObjectName(u"actionExport")
        self.actionExport.setFont(font1)
        self.actionImport = QAction(CaseCtrlTool)
        self.actionImport.setObjectName(u"actionImport")
        self.actionImport.setFont(font1)
        self.centralwidget = QWidget(CaseCtrlTool)
        self.centralwidget.setObjectName(u"centralwidget")
        self.horizontalLayout_263 = QHBoxLayout(self.centralwidget)
        self.horizontalLayout_263.setObjectName(u"horizontalLayout_263")
        self.tabWidgetCtrl = QTabWidget(self.centralwidget)
        self.tabWidgetCtrl.setObjectName(u"tabWidgetCtrl")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tabWidgetCtrl.sizePolicy().hasHeightForWidth())
        self.tabWidgetCtrl.setSizePolicy(sizePolicy)
        self.tabControl = QWidget()
        self.tabControl.setObjectName(u"tabControl")
        self.verticalLayout_24 = QVBoxLayout(self.tabControl)
        self.verticalLayout_24.setObjectName(u"verticalLayout_24")
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.horizontalLayout_2.setSizeConstraint(QLayout.SetDefaultConstraint)
        self.horizontalLayout_2.setContentsMargins(6, -1, 6, -1)
        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.label = QLabel(self.tabControl)
        self.label.setObjectName(u"label")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy1)
        self.label.setMinimumSize(QSize(80, 0))
        self.label.setMaximumSize(QSize(16777215, 16777215))
        self.label.setFont(font1)

        self.horizontalLayout.addWidget(self.label)

        self.comboBoxDevList = QComboBox(self.tabControl)
        self.comboBoxDevList.setObjectName(u"comboBoxDevList")

        self.horizontalLayout.addWidget(self.comboBoxDevList)


        self.horizontalLayout_2.addLayout(self.horizontalLayout)

        self.btnScan = QPushButton(self.tabControl)
        self.btnScan.setObjectName(u"btnScan")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.btnScan.sizePolicy().hasHeightForWidth())
        self.btnScan.setSizePolicy(sizePolicy2)
        self.btnScan.setMinimumSize(QSize(80, 0))
        self.btnScan.setFont(font1)

        self.horizontalLayout_2.addWidget(self.btnScan)

        self.btnConnect = QPushButton(self.tabControl)
        self.btnConnect.setObjectName(u"btnConnect")
        sizePolicy2.setHeightForWidth(self.btnConnect.sizePolicy().hasHeightForWidth())
        self.btnConnect.setSizePolicy(sizePolicy2)
        self.btnConnect.setMinimumSize(QSize(80, 0))
        self.btnConnect.setFont(font1)

        self.horizontalLayout_2.addWidget(self.btnConnect)

        self.btnSync = QPushButton(self.tabControl)
        self.btnSync.setObjectName(u"btnSync")
        sizePolicy2.setHeightForWidth(self.btnSync.sizePolicy().hasHeightForWidth())
        self.btnSync.setSizePolicy(sizePolicy2)
        self.btnSync.setMinimumSize(QSize(80, 0))
        self.btnSync.setFont(font1)

        self.horizontalLayout_2.addWidget(self.btnSync)

        self.btnSave = QPushButton(self.tabControl)
        self.btnSave.setObjectName(u"btnSave")
        sizePolicy2.setHeightForWidth(self.btnSave.sizePolicy().hasHeightForWidth())
        self.btnSave.setSizePolicy(sizePolicy2)
        self.btnSave.setMinimumSize(QSize(80, 0))
        self.btnSave.setFont(font1)

        self.horizontalLayout_2.addWidget(self.btnSave)


        self.verticalLayout_24.addLayout(self.horizontalLayout_2)

        self.line_5 = QFrame(self.tabControl)
        self.line_5.setObjectName(u"line_5")
        self.line_5.setLineWidth(1)
        self.line_5.setFrameShape(QFrame.HLine)
        self.line_5.setFrameShadow(QFrame.Sunken)

        self.verticalLayout_24.addWidget(self.line_5)

        self.horizontalLayout_89 = QHBoxLayout()
        self.horizontalLayout_89.setObjectName(u"horizontalLayout_89")
        self.horizontalLayout_89.setContentsMargins(-1, 3, -1, 3)
        self.horizontalLayout_77 = QHBoxLayout()
        self.horizontalLayout_77.setObjectName(u"horizontalLayout_77")
        self.horizontalLayout_77.setSizeConstraint(QLayout.SetMaximumSize)
        self.horizontalLayout_77.setContentsMargins(6, -1, 6, -1)
        self.label_26 = QLabel(self.tabControl)
        self.label_26.setObjectName(u"label_26")
        sizePolicy1.setHeightForWidth(self.label_26.sizePolicy().hasHeightForWidth())
        self.label_26.setSizePolicy(sizePolicy1)
        self.label_26.setMinimumSize(QSize(80, 0))
        self.label_26.setMaximumSize(QSize(16777215, 16777215))
        self.label_26.setFont(font1)

        self.horizontalLayout_77.addWidget(self.label_26)

        self.leditVersionInfo = QLineEdit(self.tabControl)
        self.leditVersionInfo.setObjectName(u"leditVersionInfo")
        sizePolicy3 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.leditVersionInfo.sizePolicy().hasHeightForWidth())
        self.leditVersionInfo.setSizePolicy(sizePolicy3)
        self.leditVersionInfo.setMinimumSize(QSize(0, 0))
        self.leditVersionInfo.setMaximumSize(QSize(16777215, 16777215))
        self.leditVersionInfo.setFont(font1)
        self.leditVersionInfo.setReadOnly(True)

        self.horizontalLayout_77.addWidget(self.leditVersionInfo)

        self.horizontalLayout_248 = QHBoxLayout()
        self.horizontalLayout_248.setSpacing(0)
        self.horizontalLayout_248.setObjectName(u"horizontalLayout_248")
        self.label_56 = QLabel(self.tabControl)
        self.label_56.setObjectName(u"label_56")
        sizePolicy4 = QSizePolicy(QSizePolicy.Policy.Maximum, QSizePolicy.Policy.Preferred)
        sizePolicy4.setHorizontalStretch(0)
        sizePolicy4.setVerticalStretch(0)
        sizePolicy4.setHeightForWidth(self.label_56.sizePolicy().hasHeightForWidth())
        self.label_56.setSizePolicy(sizePolicy4)
        self.label_56.setMinimumSize(QSize(0, 0))
        self.label_56.setMaximumSize(QSize(16777215, 16777215))
        self.label_56.setFont(font1)

        self.horizontalLayout_248.addWidget(self.label_56)

        self.comboBoxPsapSwCtrl = QComboBox(self.tabControl)
        self.comboBoxPsapSwCtrl.addItem("")
        self.comboBoxPsapSwCtrl.addItem("")
        self.comboBoxPsapSwCtrl.setObjectName(u"comboBoxPsapSwCtrl")
        sizePolicy5 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy5.setHorizontalStretch(0)
        sizePolicy5.setVerticalStretch(0)
        sizePolicy5.setHeightForWidth(self.comboBoxPsapSwCtrl.sizePolicy().hasHeightForWidth())
        self.comboBoxPsapSwCtrl.setSizePolicy(sizePolicy5)
        self.comboBoxPsapSwCtrl.setMinimumSize(QSize(100, 0))
        self.comboBoxPsapSwCtrl.setFont(font1)

        self.horizontalLayout_248.addWidget(self.comboBoxPsapSwCtrl)


        self.horizontalLayout_77.addLayout(self.horizontalLayout_248)


        self.horizontalLayout_89.addLayout(self.horizontalLayout_77)


        self.verticalLayout_24.addLayout(self.horizontalLayout_89)

        self.line = QFrame(self.tabControl)
        self.line.setObjectName(u"line")
        self.line.setLineWidth(1)
        self.line.setFrameShape(QFrame.HLine)
        self.line.setFrameShadow(QFrame.Sunken)

        self.verticalLayout_24.addWidget(self.line)

        self.groupBoxYmAlg = QGroupBox(self.tabControl)
        self.groupBoxYmAlg.setObjectName(u"groupBoxYmAlg")
        sizePolicy6 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Maximum)
        sizePolicy6.setHorizontalStretch(0)
        sizePolicy6.setVerticalStretch(0)
        sizePolicy6.setHeightForWidth(self.groupBoxYmAlg.sizePolicy().hasHeightForWidth())
        self.groupBoxYmAlg.setSizePolicy(sizePolicy6)
        self.groupBoxYmAlg.setFont(font1)
        self.groupBoxYmAlg.setCheckable(True)
        self.verticalLayout_2 = QVBoxLayout(self.groupBoxYmAlg)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.groupBoxGain = QGroupBox(self.groupBoxYmAlg)
        self.groupBoxGain.setObjectName(u"groupBoxGain")
        self.horizontalLayout_55 = QHBoxLayout(self.groupBoxGain)
        self.horizontalLayout_55.setSpacing(50)
        self.horizontalLayout_55.setObjectName(u"horizontalLayout_55")
        self.horizontalLayout_55.setContentsMargins(6, -1, 6, -1)
        self.horizontalLayout_28 = QHBoxLayout()
        self.horizontalLayout_28.setObjectName(u"horizontalLayout_28")
        self.label_10 = QLabel(self.groupBoxGain)
        self.label_10.setObjectName(u"label_10")
        sizePolicy1.setHeightForWidth(self.label_10.sizePolicy().hasHeightForWidth())
        self.label_10.setSizePolicy(sizePolicy1)
        self.label_10.setMinimumSize(QSize(100, 0))
        self.label_10.setMaximumSize(QSize(16777215, 16777215))
        self.label_10.setFont(font1)

        self.horizontalLayout_28.addWidget(self.label_10)

        self.doubleSpinBoxInputGain = QDoubleSpinBox(self.groupBoxGain)
        self.doubleSpinBoxInputGain.setObjectName(u"doubleSpinBoxInputGain")
        self.doubleSpinBoxInputGain.setDecimals(1)
        self.doubleSpinBoxInputGain.setMinimum(-120.000000000000000)
        self.doubleSpinBoxInputGain.setMaximum(120.000000000000000)

        self.horizontalLayout_28.addWidget(self.doubleSpinBoxInputGain)


        self.horizontalLayout_55.addLayout(self.horizontalLayout_28)

        self.horizontalLayout_45 = QHBoxLayout()
        self.horizontalLayout_45.setObjectName(u"horizontalLayout_45")
        self.label_17 = QLabel(self.groupBoxGain)
        self.label_17.setObjectName(u"label_17")
        sizePolicy1.setHeightForWidth(self.label_17.sizePolicy().hasHeightForWidth())
        self.label_17.setSizePolicy(sizePolicy1)
        self.label_17.setMinimumSize(QSize(100, 0))
        self.label_17.setMaximumSize(QSize(16777215, 16777215))
        self.label_17.setFont(font1)

        self.horizontalLayout_45.addWidget(self.label_17)

        self.doubleSpinBoxOutputGain = QDoubleSpinBox(self.groupBoxGain)
        self.doubleSpinBoxOutputGain.setObjectName(u"doubleSpinBoxOutputGain")
        self.doubleSpinBoxOutputGain.setDecimals(1)
        self.doubleSpinBoxOutputGain.setMinimum(-120.000000000000000)
        self.doubleSpinBoxOutputGain.setMaximum(120.000000000000000)

        self.horizontalLayout_45.addWidget(self.doubleSpinBoxOutputGain)


        self.horizontalLayout_55.addLayout(self.horizontalLayout_45)


        self.verticalLayout_2.addWidget(self.groupBoxGain)

        self.horizontalLayout_15 = QHBoxLayout()
        self.horizontalLayout_15.setSpacing(20)
        self.horizontalLayout_15.setObjectName(u"horizontalLayout_15")
        self.groupBoxNs = QGroupBox(self.groupBoxYmAlg)
        self.groupBoxNs.setObjectName(u"groupBoxNs")
        self.groupBoxNs.setCheckable(True)
        self.horizontalLayout_4 = QHBoxLayout(self.groupBoxNs)
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.horizontalLayout_4.setContentsMargins(6, -1, 6, -1)
        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.label_3 = QLabel(self.groupBoxNs)
        self.label_3.setObjectName(u"label_3")
        sizePolicy1.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy1)
        self.label_3.setMinimumSize(QSize(0, 0))
        self.label_3.setMaximumSize(QSize(16777215, 16777215))
        self.label_3.setFont(font1)

        self.horizontalLayout_5.addWidget(self.label_3)

        self.comboBoxNsMode = QComboBox(self.groupBoxNs)
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.addItem("")
        self.comboBoxNsMode.setObjectName(u"comboBoxNsMode")

        self.horizontalLayout_5.addWidget(self.comboBoxNsMode)


        self.horizontalLayout_4.addLayout(self.horizontalLayout_5)


        self.horizontalLayout_15.addWidget(self.groupBoxNs)

        self.groupBoxSweepFreq = QGroupBox(self.groupBoxYmAlg)
        self.groupBoxSweepFreq.setObjectName(u"groupBoxSweepFreq")
        self.groupBoxSweepFreq.setCheckable(True)
        self.horizontalLayout_65 = QHBoxLayout(self.groupBoxSweepFreq)
        self.horizontalLayout_65.setObjectName(u"horizontalLayout_65")
        self.horizontalLayout_65.setContentsMargins(6, -1, 6, -1)
        self.horizontalLayout_252 = QHBoxLayout()
        self.horizontalLayout_252.setObjectName(u"horizontalLayout_252")
        self.label_79 = QLabel(self.groupBoxSweepFreq)
        self.label_79.setObjectName(u"label_79")
        sizePolicy1.setHeightForWidth(self.label_79.sizePolicy().hasHeightForWidth())
        self.label_79.setSizePolicy(sizePolicy1)
        self.label_79.setMinimumSize(QSize(0, 0))
        self.label_79.setMaximumSize(QSize(16777215, 16777215))
        self.label_79.setFont(font1)

        self.horizontalLayout_252.addWidget(self.label_79)

        self.comboBoxSweepType = QComboBox(self.groupBoxSweepFreq)
        self.comboBoxSweepType.addItem("")
        self.comboBoxSweepType.addItem("")
        self.comboBoxSweepType.addItem("")
        self.comboBoxSweepType.addItem("")
        self.comboBoxSweepType.setObjectName(u"comboBoxSweepType")

        self.horizontalLayout_252.addWidget(self.comboBoxSweepType)


        self.horizontalLayout_65.addLayout(self.horizontalLayout_252)

        self.horizontalLayout_251 = QHBoxLayout()
        self.horizontalLayout_251.setObjectName(u"horizontalLayout_251")
        self.label_19 = QLabel(self.groupBoxSweepFreq)
        self.label_19.setObjectName(u"label_19")
        sizePolicy1.setHeightForWidth(self.label_19.sizePolicy().hasHeightForWidth())
        self.label_19.setSizePolicy(sizePolicy1)
        self.label_19.setMinimumSize(QSize(0, 0))
        self.label_19.setMaximumSize(QSize(16777215, 16777215))
        self.label_19.setFont(font1)

        self.horizontalLayout_251.addWidget(self.label_19)

        self.spinBoxSweepFreq = QSpinBox(self.groupBoxSweepFreq)
        self.spinBoxSweepFreq.setObjectName(u"spinBoxSweepFreq")
        self.spinBoxSweepFreq.setMaximum(8000)
        self.spinBoxSweepFreq.setValue(1000)

        self.horizontalLayout_251.addWidget(self.spinBoxSweepFreq)


        self.horizontalLayout_65.addLayout(self.horizontalLayout_251)


        self.horizontalLayout_15.addWidget(self.groupBoxSweepFreq)


        self.verticalLayout_2.addLayout(self.horizontalLayout_15)

        self.horizontalLayout_26 = QHBoxLayout()
        self.horizontalLayout_26.setSpacing(20)
        self.horizontalLayout_26.setObjectName(u"horizontalLayout_26")
        self.verticalLayout_26 = QVBoxLayout()
        self.verticalLayout_26.setObjectName(u"verticalLayout_26")
        self.groupBoxLimit = QGroupBox(self.groupBoxYmAlg)
        self.groupBoxLimit.setObjectName(u"groupBoxLimit")
        self.groupBoxLimit.setCheckable(True)
        self.horizontalLayout_29 = QHBoxLayout(self.groupBoxLimit)
        self.horizontalLayout_29.setSpacing(20)
        self.horizontalLayout_29.setObjectName(u"horizontalLayout_29")
        self.horizontalLayout_29.setContentsMargins(6, -1, 6, -1)
        self.horizontalLayout_253 = QHBoxLayout()
        self.horizontalLayout_253.setSpacing(0)
        self.horizontalLayout_253.setObjectName(u"horizontalLayout_253")
        self.label_222 = QLabel(self.groupBoxLimit)
        self.label_222.setObjectName(u"label_222")
        sizePolicy1.setHeightForWidth(self.label_222.sizePolicy().hasHeightForWidth())
        self.label_222.setSizePolicy(sizePolicy1)
        self.label_222.setMinimumSize(QSize(0, 0))
        self.label_222.setMaximumSize(QSize(16777215, 16777215))
        self.label_222.setFont(font1)

        self.horizontalLayout_253.addWidget(self.label_222)

        self.dSBoxLimitThreshold = QDoubleSpinBox(self.groupBoxLimit)
        self.dSBoxLimitThreshold.setObjectName(u"dSBoxLimitThreshold")
        sizePolicy3.setHeightForWidth(self.dSBoxLimitThreshold.sizePolicy().hasHeightForWidth())
        self.dSBoxLimitThreshold.setSizePolicy(sizePolicy3)
        self.dSBoxLimitThreshold.setDecimals(1)
        self.dSBoxLimitThreshold.setMinimum(-6553.500000000000000)
        self.dSBoxLimitThreshold.setMaximum(0.000000000000000)

        self.horizontalLayout_253.addWidget(self.dSBoxLimitThreshold)


        self.horizontalLayout_29.addLayout(self.horizontalLayout_253)

        self.horizontalLayout_254 = QHBoxLayout()
        self.horizontalLayout_254.setSpacing(0)
        self.horizontalLayout_254.setObjectName(u"horizontalLayout_254")
        self.label_223 = QLabel(self.groupBoxLimit)
        self.label_223.setObjectName(u"label_223")
        sizePolicy1.setHeightForWidth(self.label_223.sizePolicy().hasHeightForWidth())
        self.label_223.setSizePolicy(sizePolicy1)
        self.label_223.setMinimumSize(QSize(0, 0))
        self.label_223.setMaximumSize(QSize(16777215, 16777215))
        self.label_223.setFont(font1)

        self.horizontalLayout_254.addWidget(self.label_223)

        self.dSBoxLimitKnee = QDoubleSpinBox(self.groupBoxLimit)
        self.dSBoxLimitKnee.setObjectName(u"dSBoxLimitKnee")
        sizePolicy3.setHeightForWidth(self.dSBoxLimitKnee.sizePolicy().hasHeightForWidth())
        self.dSBoxLimitKnee.setSizePolicy(sizePolicy3)
        self.dSBoxLimitKnee.setDecimals(1)
        self.dSBoxLimitKnee.setMinimum(0.000000000000000)
        self.dSBoxLimitKnee.setMaximum(30.000000000000000)

        self.horizontalLayout_254.addWidget(self.dSBoxLimitKnee)


        self.horizontalLayout_29.addLayout(self.horizontalLayout_254)


        self.verticalLayout_26.addWidget(self.groupBoxLimit)

        self.groupBoxFrameRms = QGroupBox(self.groupBoxYmAlg)
        self.groupBoxFrameRms.setObjectName(u"groupBoxFrameRms")
        self.groupBoxFrameRms.setCheckable(True)
        self.groupBoxFrameRms.setChecked(False)
        self.horizontalLayout_255 = QHBoxLayout(self.groupBoxFrameRms)
        self.horizontalLayout_255.setSpacing(20)
        self.horizontalLayout_255.setObjectName(u"horizontalLayout_255")
        self.horizontalLayout_255.setContentsMargins(6, -1, 6, -1)
        self.horizontalLayout_256 = QHBoxLayout()
        self.horizontalLayout_256.setSpacing(0)
        self.horizontalLayout_256.setObjectName(u"horizontalLayout_256")
        self.label_224 = QLabel(self.groupBoxFrameRms)
        self.label_224.setObjectName(u"label_224")
        sizePolicy7 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        sizePolicy7.setHorizontalStretch(0)
        sizePolicy7.setVerticalStretch(0)
        sizePolicy7.setHeightForWidth(self.label_224.sizePolicy().hasHeightForWidth())
        self.label_224.setSizePolicy(sizePolicy7)
        self.label_224.setMinimumSize(QSize(0, 0))
        self.label_224.setMaximumSize(QSize(16777215, 16777215))
        self.label_224.setFont(font1)

        self.horizontalLayout_256.addWidget(self.label_224)

        self.leditRMSVal = QLineEdit(self.groupBoxFrameRms)
        self.leditRMSVal.setObjectName(u"leditRMSVal")
        sizePolicy3.setHeightForWidth(self.leditRMSVal.sizePolicy().hasHeightForWidth())
        self.leditRMSVal.setSizePolicy(sizePolicy3)
        self.leditRMSVal.setMinimumSize(QSize(0, 0))
        self.leditRMSVal.setMaximumSize(QSize(16777215, 16777215))
        self.leditRMSVal.setFont(font1)
        self.leditRMSVal.setReadOnly(True)

        self.horizontalLayout_256.addWidget(self.leditRMSVal)


        self.horizontalLayout_255.addLayout(self.horizontalLayout_256)

        self.horizontalLayout_257 = QHBoxLayout()
        self.horizontalLayout_257.setSpacing(0)
        self.horizontalLayout_257.setObjectName(u"horizontalLayout_257")
        self.label_225 = QLabel(self.groupBoxFrameRms)
        self.label_225.setObjectName(u"label_225")
        sizePolicy7.setHeightForWidth(self.label_225.sizePolicy().hasHeightForWidth())
        self.label_225.setSizePolicy(sizePolicy7)
        self.label_225.setMinimumSize(QSize(0, 0))
        self.label_225.setMaximumSize(QSize(16777215, 16777215))
        self.label_225.setFont(font1)

        self.horizontalLayout_257.addWidget(self.label_225)

        self.leditRMSVal_LF = QLineEdit(self.groupBoxFrameRms)
        self.leditRMSVal_LF.setObjectName(u"leditRMSVal_LF")
        sizePolicy3.setHeightForWidth(self.leditRMSVal_LF.sizePolicy().hasHeightForWidth())
        self.leditRMSVal_LF.setSizePolicy(sizePolicy3)
        self.leditRMSVal_LF.setMinimumSize(QSize(0, 0))
        self.leditRMSVal_LF.setMaximumSize(QSize(16777215, 16777215))
        self.leditRMSVal_LF.setFont(font1)
        self.leditRMSVal_LF.setReadOnly(True)

        self.horizontalLayout_257.addWidget(self.leditRMSVal_LF)


        self.horizontalLayout_255.addLayout(self.horizontalLayout_257)


        self.verticalLayout_26.addWidget(self.groupBoxFrameRms)


        self.horizontalLayout_26.addLayout(self.verticalLayout_26)

        self.groupBoxSsl = QGroupBox(self.groupBoxYmAlg)
        self.groupBoxSsl.setObjectName(u"groupBoxSsl")
        self.groupBoxSsl.setCheckable(True)
        self.groupBoxSsl.setChecked(True)
        self.gridLayout = QGridLayout(self.groupBoxSsl)
        self.gridLayout.setObjectName(u"gridLayout")
        self.horizontalLayout_258 = QHBoxLayout()
        self.horizontalLayout_258.setSpacing(0)
        self.horizontalLayout_258.setObjectName(u"horizontalLayout_258")
        self.checkBoxSslAngle = QCheckBox(self.groupBoxSsl)
        self.checkBoxSslAngle.setObjectName(u"checkBoxSslAngle")
        self.checkBoxSslAngle.setFont(font1)

        self.horizontalLayout_258.addWidget(self.checkBoxSslAngle)

        self.leditSslAngle = QLineEdit(self.groupBoxSsl)
        self.leditSslAngle.setObjectName(u"leditSslAngle")
        sizePolicy3.setHeightForWidth(self.leditSslAngle.sizePolicy().hasHeightForWidth())
        self.leditSslAngle.setSizePolicy(sizePolicy3)
        self.leditSslAngle.setMinimumSize(QSize(0, 0))
        self.leditSslAngle.setMaximumSize(QSize(16777215, 16777215))
        self.leditSslAngle.setFont(font1)
        self.leditSslAngle.setReadOnly(True)

        self.horizontalLayout_258.addWidget(self.leditSslAngle)


        self.gridLayout.addLayout(self.horizontalLayout_258, 0, 0, 1, 2)

        self.horizontalLayout_259 = QHBoxLayout()
        self.horizontalLayout_259.setObjectName(u"horizontalLayout_259")
        self.label_228 = QLabel(self.groupBoxSsl)
        self.label_228.setObjectName(u"label_228")
        sizePolicy1.setHeightForWidth(self.label_228.sizePolicy().hasHeightForWidth())
        self.label_228.setSizePolicy(sizePolicy1)
        self.label_228.setMinimumSize(QSize(65, 0))
        self.label_228.setMaximumSize(QSize(16777215, 16777215))
        self.label_228.setFont(font1)

        self.horizontalLayout_259.addWidget(self.label_228)

        self.comboBoxSSLAreaID = QComboBox(self.groupBoxSsl)
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.addItem("")
        self.comboBoxSSLAreaID.setObjectName(u"comboBoxSSLAreaID")
        sizePolicy5.setHeightForWidth(self.comboBoxSSLAreaID.sizePolicy().hasHeightForWidth())
        self.comboBoxSSLAreaID.setSizePolicy(sizePolicy5)

        self.horizontalLayout_259.addWidget(self.comboBoxSSLAreaID)


        self.gridLayout.addLayout(self.horizontalLayout_259, 1, 0, 1, 1)

        self.horizontalLayout_260 = QHBoxLayout()
        self.horizontalLayout_260.setObjectName(u"horizontalLayout_260")
        self.label_229 = QLabel(self.groupBoxSsl)
        self.label_229.setObjectName(u"label_229")
        sizePolicy1.setHeightForWidth(self.label_229.sizePolicy().hasHeightForWidth())
        self.label_229.setSizePolicy(sizePolicy1)
        self.label_229.setMinimumSize(QSize(65, 0))
        self.label_229.setMaximumSize(QSize(16777215, 16777215))
        self.label_229.setFont(font1)

        self.horizontalLayout_260.addWidget(self.label_229)

        self.comboBoxSSLAreaEnable = QComboBox(self.groupBoxSsl)
        self.comboBoxSSLAreaEnable.addItem("")
        self.comboBoxSSLAreaEnable.addItem("")
        self.comboBoxSSLAreaEnable.setObjectName(u"comboBoxSSLAreaEnable")
        sizePolicy5.setHeightForWidth(self.comboBoxSSLAreaEnable.sizePolicy().hasHeightForWidth())
        self.comboBoxSSLAreaEnable.setSizePolicy(sizePolicy5)

        self.horizontalLayout_260.addWidget(self.comboBoxSSLAreaEnable)


        self.gridLayout.addLayout(self.horizontalLayout_260, 1, 1, 1, 1)

        self.horizontalLayout_12 = QHBoxLayout()
        self.horizontalLayout_12.setObjectName(u"horizontalLayout_12")
        self.label_6 = QLabel(self.groupBoxSsl)
        self.label_6.setObjectName(u"label_6")
        sizePolicy1.setHeightForWidth(self.label_6.sizePolicy().hasHeightForWidth())
        self.label_6.setSizePolicy(sizePolicy1)
        self.label_6.setMinimumSize(QSize(65, 0))
        self.label_6.setMaximumSize(QSize(16777215, 16777215))
        self.label_6.setFont(font1)

        self.horizontalLayout_12.addWidget(self.label_6)

        self.spinBoxSslVAngleMin = QSpinBox(self.groupBoxSsl)
        self.spinBoxSslVAngleMin.setObjectName(u"spinBoxSslVAngleMin")
        self.spinBoxSslVAngleMin.setMinimumSize(QSize(80, 0))
        self.spinBoxSslVAngleMin.setMaximum(90)

        self.horizontalLayout_12.addWidget(self.spinBoxSslVAngleMin)


        self.gridLayout.addLayout(self.horizontalLayout_12, 2, 0, 1, 1)

        self.horizontalLayout_13 = QHBoxLayout()
        self.horizontalLayout_13.setObjectName(u"horizontalLayout_13")
        self.label_7 = QLabel(self.groupBoxSsl)
        self.label_7.setObjectName(u"label_7")
        sizePolicy1.setHeightForWidth(self.label_7.sizePolicy().hasHeightForWidth())
        self.label_7.setSizePolicy(sizePolicy1)
        self.label_7.setMinimumSize(QSize(65, 0))
        self.label_7.setMaximumSize(QSize(16777215, 16777215))
        self.label_7.setFont(font1)

        self.horizontalLayout_13.addWidget(self.label_7)

        self.spinBoxSslVAngleMax = QSpinBox(self.groupBoxSsl)
        self.spinBoxSslVAngleMax.setObjectName(u"spinBoxSslVAngleMax")
        self.spinBoxSslVAngleMax.setMinimumSize(QSize(80, 0))
        self.spinBoxSslVAngleMax.setMaximum(90)

        self.horizontalLayout_13.addWidget(self.spinBoxSslVAngleMax)


        self.gridLayout.addLayout(self.horizontalLayout_13, 2, 1, 1, 1)

        self.horizontalLayout_14 = QHBoxLayout()
        self.horizontalLayout_14.setObjectName(u"horizontalLayout_14")
        self.label_8 = QLabel(self.groupBoxSsl)
        self.label_8.setObjectName(u"label_8")
        sizePolicy1.setHeightForWidth(self.label_8.sizePolicy().hasHeightForWidth())
        self.label_8.setSizePolicy(sizePolicy1)
        self.label_8.setMinimumSize(QSize(65, 0))
        self.label_8.setMaximumSize(QSize(16777215, 16777215))
        self.label_8.setFont(font1)

        self.horizontalLayout_14.addWidget(self.label_8)

        self.spinBoxSslHAngleMin = QSpinBox(self.groupBoxSsl)
        self.spinBoxSslHAngleMin.setObjectName(u"spinBoxSslHAngleMin")
        self.spinBoxSslHAngleMin.setMinimumSize(QSize(80, 0))
        self.spinBoxSslHAngleMin.setMaximum(360)

        self.horizontalLayout_14.addWidget(self.spinBoxSslHAngleMin)


        self.gridLayout.addLayout(self.horizontalLayout_14, 3, 0, 1, 1)

        self.horizontalLayout_261 = QHBoxLayout()
        self.horizontalLayout_261.setObjectName(u"horizontalLayout_261")
        self.label_9 = QLabel(self.groupBoxSsl)
        self.label_9.setObjectName(u"label_9")
        sizePolicy1.setHeightForWidth(self.label_9.sizePolicy().hasHeightForWidth())
        self.label_9.setSizePolicy(sizePolicy1)
        self.label_9.setMinimumSize(QSize(65, 0))
        self.label_9.setMaximumSize(QSize(16777215, 16777215))
        self.label_9.setFont(font1)

        self.horizontalLayout_261.addWidget(self.label_9)

        self.spinBoxSslHAngleMax = QSpinBox(self.groupBoxSsl)
        self.spinBoxSslHAngleMax.setObjectName(u"spinBoxSslHAngleMax")
        self.spinBoxSslHAngleMax.setMinimumSize(QSize(80, 0))
        self.spinBoxSslHAngleMax.setMaximum(360)

        self.horizontalLayout_261.addWidget(self.spinBoxSslHAngleMax)


        self.gridLayout.addLayout(self.horizontalLayout_261, 3, 1, 1, 1)


        self.horizontalLayout_26.addWidget(self.groupBoxSsl)


        self.verticalLayout_2.addLayout(self.horizontalLayout_26)

        self.horizontalLayout_11 = QHBoxLayout()
        self.horizontalLayout_11.setSpacing(20)
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.groupBoxBeam = QGroupBox(self.groupBoxYmAlg)
        self.groupBoxBeam.setObjectName(u"groupBoxBeam")
        self.groupBoxBeam.setCheckable(True)
        self.gridLayout_44 = QGridLayout(self.groupBoxBeam)
        self.gridLayout_44.setObjectName(u"gridLayout_44")
        self.gridLayout_44.setHorizontalSpacing(15)
        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.label_5 = QLabel(self.groupBoxBeam)
        self.label_5.setObjectName(u"label_5")
        sizePolicy1.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy1)
        self.label_5.setMinimumSize(QSize(70, 0))
        self.label_5.setMaximumSize(QSize(16777215, 16777215))
        self.label_5.setFont(font1)

        self.horizontalLayout_9.addWidget(self.label_5)

        self.spinBoxBeamHAngle = QSpinBox(self.groupBoxBeam)
        self.spinBoxBeamHAngle.setObjectName(u"spinBoxBeamHAngle")
        self.spinBoxBeamHAngle.setMinimumSize(QSize(80, 0))
        self.spinBoxBeamHAngle.setMaximum(360)

        self.horizontalLayout_9.addWidget(self.spinBoxBeamHAngle)


        self.gridLayout_44.addLayout(self.horizontalLayout_9, 0, 1, 1, 1)

        self.horizontalLayout_250 = QHBoxLayout()
        self.horizontalLayout_250.setObjectName(u"horizontalLayout_250")
        self.label_90 = QLabel(self.groupBoxBeam)
        self.label_90.setObjectName(u"label_90")
        sizePolicy1.setHeightForWidth(self.label_90.sizePolicy().hasHeightForWidth())
        self.label_90.setSizePolicy(sizePolicy1)
        self.label_90.setMinimumSize(QSize(70, 0))
        self.label_90.setMaximumSize(QSize(16777215, 16777215))
        self.label_90.setFont(font1)

        self.horizontalLayout_250.addWidget(self.label_90)

        self.comboBoxBeamOutID = QComboBox(self.groupBoxBeam)
        self.comboBoxBeamOutID.addItem("")
        self.comboBoxBeamOutID.addItem("")
        self.comboBoxBeamOutID.addItem("")
        self.comboBoxBeamOutID.addItem("")
        self.comboBoxBeamOutID.addItem("")
        self.comboBoxBeamOutID.setObjectName(u"comboBoxBeamOutID")
        sizePolicy5.setHeightForWidth(self.comboBoxBeamOutID.sizePolicy().hasHeightForWidth())
        self.comboBoxBeamOutID.setSizePolicy(sizePolicy5)

        self.horizontalLayout_250.addWidget(self.comboBoxBeamOutID)


        self.gridLayout_44.addLayout(self.horizontalLayout_250, 1, 1, 1, 1)

        self.horizontalLayout_249 = QHBoxLayout()
        self.horizontalLayout_249.setObjectName(u"horizontalLayout_249")
        self.label_68 = QLabel(self.groupBoxBeam)
        self.label_68.setObjectName(u"label_68")
        sizePolicy1.setHeightForWidth(self.label_68.sizePolicy().hasHeightForWidth())
        self.label_68.setSizePolicy(sizePolicy1)
        self.label_68.setMinimumSize(QSize(70, 0))
        self.label_68.setMaximumSize(QSize(16777215, 16777215))
        self.label_68.setFont(font1)

        self.horizontalLayout_249.addWidget(self.label_68)

        self.comboBoxBeamMode = QComboBox(self.groupBoxBeam)
        self.comboBoxBeamMode.addItem("")
        self.comboBoxBeamMode.addItem("")
        self.comboBoxBeamMode.setObjectName(u"comboBoxBeamMode")
        sizePolicy5.setHeightForWidth(self.comboBoxBeamMode.sizePolicy().hasHeightForWidth())
        self.comboBoxBeamMode.setSizePolicy(sizePolicy5)

        self.horizontalLayout_249.addWidget(self.comboBoxBeamMode)


        self.gridLayout_44.addLayout(self.horizontalLayout_249, 1, 0, 1, 1)

        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.label_4 = QLabel(self.groupBoxBeam)
        self.label_4.setObjectName(u"label_4")
        sizePolicy1.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy1)
        self.label_4.setMinimumSize(QSize(70, 0))
        self.label_4.setMaximumSize(QSize(16777215, 16777215))
        self.label_4.setFont(font1)

        self.horizontalLayout_8.addWidget(self.label_4)

        self.spinBoxBeamVAngle = QSpinBox(self.groupBoxBeam)
        self.spinBoxBeamVAngle.setObjectName(u"spinBoxBeamVAngle")
        self.spinBoxBeamVAngle.setMinimumSize(QSize(80, 0))
        self.spinBoxBeamVAngle.setMaximum(90)

        self.horizontalLayout_8.addWidget(self.spinBoxBeamVAngle)


        self.gridLayout_44.addLayout(self.horizontalLayout_8, 0, 0, 1, 1)

        self.horizontalLayout_262 = QHBoxLayout()
        self.horizontalLayout_262.setObjectName(u"horizontalLayout_262")
        self.label_227 = QLabel(self.groupBoxBeam)
        self.label_227.setObjectName(u"label_227")
        sizePolicy1.setHeightForWidth(self.label_227.sizePolicy().hasHeightForWidth())
        self.label_227.setSizePolicy(sizePolicy1)
        self.label_227.setMinimumSize(QSize(70, 0))
        self.label_227.setMaximumSize(QSize(16777215, 16777215))
        self.label_227.setFont(font1)

        self.horizontalLayout_262.addWidget(self.label_227)

        self.comboBoxBeamWidth = QComboBox(self.groupBoxBeam)
        self.comboBoxBeamWidth.addItem("")
        self.comboBoxBeamWidth.addItem("")
        self.comboBoxBeamWidth.addItem("")
        self.comboBoxBeamWidth.addItem("")
        self.comboBoxBeamWidth.setObjectName(u"comboBoxBeamWidth")
        sizePolicy5.setHeightForWidth(self.comboBoxBeamWidth.sizePolicy().hasHeightForWidth())
        self.comboBoxBeamWidth.setSizePolicy(sizePolicy5)

        self.horizontalLayout_262.addWidget(self.comboBoxBeamWidth)


        self.gridLayout_44.addLayout(self.horizontalLayout_262, 2, 0, 1, 1)


        self.horizontalLayout_11.addWidget(self.groupBoxBeam)

        self.groupBoxLeAudio = QGroupBox(self.groupBoxYmAlg)
        self.groupBoxLeAudio.setObjectName(u"groupBoxLeAudio")
        self.verticalLayout_25 = QVBoxLayout(self.groupBoxLeAudio)
        self.verticalLayout_25.setObjectName(u"verticalLayout_25")
        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.label_221 = QLabel(self.groupBoxLeAudio)
        self.label_221.setObjectName(u"label_221")
        sizePolicy1.setHeightForWidth(self.label_221.sizePolicy().hasHeightForWidth())
        self.label_221.setSizePolicy(sizePolicy1)
        self.label_221.setMinimumSize(QSize(50, 0))
        self.label_221.setMaximumSize(QSize(16777215, 16777215))
        self.label_221.setFont(font1)

        self.horizontalLayout_6.addWidget(self.label_221)

        self.comboBoxLeAudioFmtL = QComboBox(self.groupBoxLeAudio)
        self.comboBoxLeAudioFmtL.addItem("")
        self.comboBoxLeAudioFmtL.addItem("")
        self.comboBoxLeAudioFmtL.addItem("")
        self.comboBoxLeAudioFmtL.addItem("")
        self.comboBoxLeAudioFmtL.addItem("")
        self.comboBoxLeAudioFmtL.setObjectName(u"comboBoxLeAudioFmtL")
        self.comboBoxLeAudioFmtL.setFont(font1)

        self.horizontalLayout_6.addWidget(self.comboBoxLeAudioFmtL)


        self.verticalLayout_25.addLayout(self.horizontalLayout_6)

        self.horizontalLayout_10 = QHBoxLayout()
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.label_226 = QLabel(self.groupBoxLeAudio)
        self.label_226.setObjectName(u"label_226")
        sizePolicy1.setHeightForWidth(self.label_226.sizePolicy().hasHeightForWidth())
        self.label_226.setSizePolicy(sizePolicy1)
        self.label_226.setMinimumSize(QSize(50, 0))
        self.label_226.setMaximumSize(QSize(16777215, 16777215))
        self.label_226.setFont(font1)

        self.horizontalLayout_10.addWidget(self.label_226)

        self.comboBoxLeAudioFmtR = QComboBox(self.groupBoxLeAudio)
        self.comboBoxLeAudioFmtR.addItem("")
        self.comboBoxLeAudioFmtR.addItem("")
        self.comboBoxLeAudioFmtR.addItem("")
        self.comboBoxLeAudioFmtR.addItem("")
        self.comboBoxLeAudioFmtR.addItem("")
        self.comboBoxLeAudioFmtR.setObjectName(u"comboBoxLeAudioFmtR")
        self.comboBoxLeAudioFmtR.setFont(font1)

        self.horizontalLayout_10.addWidget(self.comboBoxLeAudioFmtR)


        self.verticalLayout_25.addLayout(self.horizontalLayout_10)


        self.horizontalLayout_11.addWidget(self.groupBoxLeAudio)


        self.verticalLayout_2.addLayout(self.horizontalLayout_11)


        self.verticalLayout_24.addWidget(self.groupBoxYmAlg)

        self.line_3 = QFrame(self.tabControl)
        self.line_3.setObjectName(u"line_3")
        self.line_3.setLineWidth(1)
        self.line_3.setFrameShape(QFrame.HLine)
        self.line_3.setFrameShadow(QFrame.Sunken)

        self.verticalLayout_24.addWidget(self.line_3)

        self.horizontalLayout_246 = QHBoxLayout()
        self.horizontalLayout_246.setSpacing(50)
        self.horizontalLayout_246.setObjectName(u"horizontalLayout_246")
        self.horizontalLayout_246.setContentsMargins(6, -1, 6, -1)
        self.horizontalLayout_102 = QHBoxLayout()
        self.horizontalLayout_102.setObjectName(u"horizontalLayout_102")
        self.label_36 = QLabel(self.tabControl)
        self.label_36.setObjectName(u"label_36")
        sizePolicy1.setHeightForWidth(self.label_36.sizePolicy().hasHeightForWidth())
        self.label_36.setSizePolicy(sizePolicy1)
        self.label_36.setMinimumSize(QSize(0, 0))
        self.label_36.setMaximumSize(QSize(16777215, 16777215))
        self.label_36.setFont(font1)

        self.horizontalLayout_102.addWidget(self.label_36)

        self.comboBoxDebugLevel = QComboBox(self.tabControl)
        self.comboBoxDebugLevel.addItem("")
        self.comboBoxDebugLevel.addItem("")
        self.comboBoxDebugLevel.addItem("")
        self.comboBoxDebugLevel.addItem("")
        self.comboBoxDebugLevel.addItem("")
        self.comboBoxDebugLevel.addItem("")
        self.comboBoxDebugLevel.addItem("")
        self.comboBoxDebugLevel.setObjectName(u"comboBoxDebugLevel")
        self.comboBoxDebugLevel.setFont(font1)

        self.horizontalLayout_102.addWidget(self.comboBoxDebugLevel)


        self.horizontalLayout_246.addLayout(self.horizontalLayout_102)

        self.horizontalLayout_247 = QHBoxLayout()
        self.horizontalLayout_247.setSpacing(5)
        self.horizontalLayout_247.setObjectName(u"horizontalLayout_247")
        self.label_46 = QLabel(self.tabControl)
        self.label_46.setObjectName(u"label_46")
        sizePolicy1.setHeightForWidth(self.label_46.sizePolicy().hasHeightForWidth())
        self.label_46.setSizePolicy(sizePolicy1)
        self.label_46.setMinimumSize(QSize(0, 0))
        self.label_46.setMaximumSize(QSize(16777215, 16777215))
        self.label_46.setFont(font1)

        self.horizontalLayout_247.addWidget(self.label_46)

        self.comboBoxAudioDumpFormat = QComboBox(self.tabControl)
        self.comboBoxAudioDumpFormat.addItem("")
        self.comboBoxAudioDumpFormat.addItem("")
        self.comboBoxAudioDumpFormat.addItem("")
        self.comboBoxAudioDumpFormat.setObjectName(u"comboBoxAudioDumpFormat")
        self.comboBoxAudioDumpFormat.setFont(font1)

        self.horizontalLayout_247.addWidget(self.comboBoxAudioDumpFormat)

        self.comboBoxAudioDumpBits = QComboBox(self.tabControl)
        self.comboBoxAudioDumpBits.addItem("")
        self.comboBoxAudioDumpBits.addItem("")
        self.comboBoxAudioDumpBits.setObjectName(u"comboBoxAudioDumpBits")
        self.comboBoxAudioDumpBits.setMinimumSize(QSize(70, 0))
        self.comboBoxAudioDumpBits.setFont(font1)

        self.horizontalLayout_247.addWidget(self.comboBoxAudioDumpBits)


        self.horizontalLayout_246.addLayout(self.horizontalLayout_247)


        self.verticalLayout_24.addLayout(self.horizontalLayout_246)

        self.line_4 = QFrame(self.tabControl)
        self.line_4.setObjectName(u"line_4")
        self.line_4.setLineWidth(1)
        self.line_4.setFrameShape(QFrame.HLine)
        self.line_4.setFrameShadow(QFrame.Sunken)

        self.verticalLayout_24.addWidget(self.line_4)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setSizeConstraint(QLayout.SetMaximumSize)
        self.horizontalLayout_3.setContentsMargins(6, -1, 6, -1)
        self.label_2 = QLabel(self.tabControl)
        self.label_2.setObjectName(u"label_2")
        sizePolicy1.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy1)
        self.label_2.setMinimumSize(QSize(80, 0))
        self.label_2.setMaximumSize(QSize(16777215, 16777215))
        self.label_2.setFont(font1)

        self.horizontalLayout_3.addWidget(self.label_2)

        self.leditAcivateState = QLineEdit(self.tabControl)
        self.leditAcivateState.setObjectName(u"leditAcivateState")
        sizePolicy3.setHeightForWidth(self.leditAcivateState.sizePolicy().hasHeightForWidth())
        self.leditAcivateState.setSizePolicy(sizePolicy3)
        self.leditAcivateState.setMinimumSize(QSize(0, 0))
        self.leditAcivateState.setMaximumSize(QSize(16777215, 16777215))
        self.leditAcivateState.setFont(font1)
        self.leditAcivateState.setReadOnly(True)

        self.horizontalLayout_3.addWidget(self.leditAcivateState)

        self.btnAlgActivate = QPushButton(self.tabControl)
        self.btnAlgActivate.setObjectName(u"btnAlgActivate")
        sizePolicy2.setHeightForWidth(self.btnAlgActivate.sizePolicy().hasHeightForWidth())
        self.btnAlgActivate.setSizePolicy(sizePolicy2)
        self.btnAlgActivate.setMinimumSize(QSize(80, 0))
        self.btnAlgActivate.setFont(font1)
        self.btnAlgActivate.setAutoFillBackground(False)
        self.btnAlgActivate.setStyleSheet(u"QPushButton:checked\n"
"{background-color: rgb(0, 255, 0);font-size: 10pt;border-style:solid;border-width: 4px;}")
        self.btnAlgActivate.setCheckable(False)

        self.horizontalLayout_3.addWidget(self.btnAlgActivate)


        self.verticalLayout_24.addLayout(self.horizontalLayout_3)

        self.tabWidgetCtrl.addTab(self.tabControl, "")
        self.tabWDRC = QWidget()
        self.tabWDRC.setObjectName(u"tabWDRC")
        self.gridLayout_11 = QGridLayout(self.tabWDRC)
        self.gridLayout_11.setObjectName(u"gridLayout_11")
        self.groupBoxWDRC = QGroupBox(self.tabWDRC)
        self.groupBoxWDRC.setObjectName(u"groupBoxWDRC")
        self.groupBoxWDRC.setCheckable(True)
        self.verticalLayout_3 = QVBoxLayout(self.groupBoxWDRC)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.groupBox_7 = QGroupBox(self.groupBoxWDRC)
        self.groupBox_7.setObjectName(u"groupBox_7")
        self.groupBox_7.setFont(font1)
        self.horizontalLayout_93 = QHBoxLayout(self.groupBox_7)
        self.horizontalLayout_93.setObjectName(u"horizontalLayout_93")
        self.horizontalLayout_93.setContentsMargins(-1, 1, -1, 1)
        self.label_59 = QLabel(self.groupBox_7)
        self.label_59.setObjectName(u"label_59")
        sizePolicy1.setHeightForWidth(self.label_59.sizePolicy().hasHeightForWidth())
        self.label_59.setSizePolicy(sizePolicy1)
        self.label_59.setMinimumSize(QSize(70, 0))
        self.label_59.setMaximumSize(QSize(16777215, 20))

        self.horizontalLayout_93.addWidget(self.label_59)

        self.sliderAfcAllGain = DelayedSlider(self.groupBox_7)
        self.sliderAfcAllGain.setObjectName(u"sliderAfcAllGain")
        self.sliderAfcAllGain.setMinimum(-1200)
        self.sliderAfcAllGain.setMaximum(1200)
        self.sliderAfcAllGain.setValue(0)
        self.sliderAfcAllGain.setOrientation(Qt.Horizontal)

        self.horizontalLayout_93.addWidget(self.sliderAfcAllGain)

        self.spinAfcAllGain = QDoubleSpinBox(self.groupBox_7)
        self.spinAfcAllGain.setObjectName(u"spinAfcAllGain")
        self.spinAfcAllGain.setMinimumSize(QSize(0, 0))
        self.spinAfcAllGain.setDecimals(1)
        self.spinAfcAllGain.setMinimum(-120.000000000000000)
        self.spinAfcAllGain.setMaximum(120.000000000000000)
        self.spinAfcAllGain.setSingleStep(1.000000000000000)

        self.horizontalLayout_93.addWidget(self.spinAfcAllGain)


        self.verticalLayout_3.addWidget(self.groupBox_7)

        self.scrollArea = QScrollArea(self.groupBoxWDRC)
        self.scrollArea.setObjectName(u"scrollArea")
        self.scrollArea.setWidgetResizable(True)
        self.scrollAreaWidgetContents = QWidget()
        self.scrollAreaWidgetContents.setObjectName(u"scrollAreaWidgetContents")
        self.scrollAreaWidgetContents.setGeometry(QRect(0, 0, 611, 2452))
        self.gridLayout_15 = QGridLayout(self.scrollAreaWidgetContents)
        self.gridLayout_15.setObjectName(u"gridLayout_15")
        self.gpBand_1 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_1.setObjectName(u"gpBand_1")
        sizePolicy8 = QSizePolicy(QSizePolicy.Policy.Maximum, QSizePolicy.Policy.Maximum)
        sizePolicy8.setHorizontalStretch(0)
        sizePolicy8.setVerticalStretch(0)
        sizePolicy8.setHeightForWidth(self.gpBand_1.sizePolicy().hasHeightForWidth())
        self.gpBand_1.setSizePolicy(sizePolicy8)
        self.gpBand_1.setMinimumSize(QSize(0, 0))
        self.gpBand_1.setFont(font1)
        self.verticalLayout_5 = QVBoxLayout(self.gpBand_1)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.horizontalLayout_18 = QHBoxLayout()
        self.horizontalLayout_18.setObjectName(u"horizontalLayout_18")
        self.horizontalLayout_18.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_36 = QHBoxLayout()
        self.horizontalLayout_36.setObjectName(u"horizontalLayout_36")
        self.label_27 = QLabel(self.gpBand_1)
        self.label_27.setObjectName(u"label_27")
        sizePolicy1.setHeightForWidth(self.label_27.sizePolicy().hasHeightForWidth())
        self.label_27.setSizePolicy(sizePolicy1)
        self.label_27.setMinimumSize(QSize(0, 0))
        self.label_27.setMaximumSize(QSize(16777215, 16777215))
        self.label_27.setFont(font1)

        self.horizontalLayout_36.addWidget(self.label_27)

        self.spinBoxAttackBand_1 = QSpinBox(self.gpBand_1)
        self.spinBoxAttackBand_1.setObjectName(u"spinBoxAttackBand_1")
        self.spinBoxAttackBand_1.setMaximum(65535)

        self.horizontalLayout_36.addWidget(self.spinBoxAttackBand_1)


        self.horizontalLayout_18.addLayout(self.horizontalLayout_36)

        self.horizontalLayout_37 = QHBoxLayout()
        self.horizontalLayout_37.setObjectName(u"horizontalLayout_37")
        self.label_28 = QLabel(self.gpBand_1)
        self.label_28.setObjectName(u"label_28")
        sizePolicy1.setHeightForWidth(self.label_28.sizePolicy().hasHeightForWidth())
        self.label_28.setSizePolicy(sizePolicy1)
        self.label_28.setMinimumSize(QSize(0, 0))
        self.label_28.setMaximumSize(QSize(16777215, 16777215))
        self.label_28.setFont(font1)

        self.horizontalLayout_37.addWidget(self.label_28)

        self.spinBoxReleaseBand_1 = QSpinBox(self.gpBand_1)
        self.spinBoxReleaseBand_1.setObjectName(u"spinBoxReleaseBand_1")
        self.spinBoxReleaseBand_1.setMaximum(65535)

        self.horizontalLayout_37.addWidget(self.spinBoxReleaseBand_1)


        self.horizontalLayout_18.addLayout(self.horizontalLayout_37)


        self.verticalLayout_5.addLayout(self.horizontalLayout_18)

        self.gpLimitBand_1 = QGroupBox(self.gpBand_1)
        self.gpLimitBand_1.setObjectName(u"gpLimitBand_1")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_1.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_1.setSizePolicy(sizePolicy6)
        self.gpLimitBand_1.setFont(font1)
        self.gridLayout_4 = QGridLayout(self.gpLimitBand_1)
        self.gridLayout_4.setObjectName(u"gridLayout_4")
        self.gridLayout_4.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_19 = QHBoxLayout()
        self.horizontalLayout_19.setObjectName(u"horizontalLayout_19")
        self.label_12 = QLabel(self.gpLimitBand_1)
        self.label_12.setObjectName(u"label_12")
        sizePolicy1.setHeightForWidth(self.label_12.sizePolicy().hasHeightForWidth())
        self.label_12.setSizePolicy(sizePolicy1)
        self.label_12.setMinimumSize(QSize(0, 0))
        self.label_12.setMaximumSize(QSize(16777215, 16777215))
        self.label_12.setFont(font1)

        self.horizontalLayout_19.addWidget(self.label_12)

        self.dSBoxLimitThresholdBand_1 = QDoubleSpinBox(self.gpLimitBand_1)
        self.dSBoxLimitThresholdBand_1.setObjectName(u"dSBoxLimitThresholdBand_1")
        self.dSBoxLimitThresholdBand_1.setDecimals(1)
        self.dSBoxLimitThresholdBand_1.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_1.setMaximum(65535.500000000000000)

        self.horizontalLayout_19.addWidget(self.dSBoxLimitThresholdBand_1)


        self.gridLayout_4.addLayout(self.horizontalLayout_19, 0, 0, 1, 1)

        self.horizontalLayout_38 = QHBoxLayout()
        self.horizontalLayout_38.setObjectName(u"horizontalLayout_38")
        self.label_29 = QLabel(self.gpLimitBand_1)
        self.label_29.setObjectName(u"label_29")
        sizePolicy1.setHeightForWidth(self.label_29.sizePolicy().hasHeightForWidth())
        self.label_29.setSizePolicy(sizePolicy1)
        self.label_29.setMinimumSize(QSize(0, 0))
        self.label_29.setMaximumSize(QSize(16777215, 16777215))
        self.label_29.setFont(font1)

        self.horizontalLayout_38.addWidget(self.label_29)

        self.dSBoxLimitKneeBand_1 = QDoubleSpinBox(self.gpLimitBand_1)
        self.dSBoxLimitKneeBand_1.setObjectName(u"dSBoxLimitKneeBand_1")
        self.dSBoxLimitKneeBand_1.setDecimals(1)
        self.dSBoxLimitKneeBand_1.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_1.setMaximum(30.000000000000000)

        self.horizontalLayout_38.addWidget(self.dSBoxLimitKneeBand_1)


        self.gridLayout_4.addLayout(self.horizontalLayout_38, 0, 1, 1, 1)


        self.verticalLayout_5.addWidget(self.gpLimitBand_1)

        self.groupBox_9 = QGroupBox(self.gpBand_1)
        self.groupBox_9.setObjectName(u"groupBox_9")
        sizePolicy6.setHeightForWidth(self.groupBox_9.sizePolicy().hasHeightForWidth())
        self.groupBox_9.setSizePolicy(sizePolicy6)
        self.gridLayout_5 = QGridLayout(self.groupBox_9)
        self.gridLayout_5.setObjectName(u"gridLayout_5")
        self.gridLayout_5.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_39 = QHBoxLayout()
        self.horizontalLayout_39.setObjectName(u"horizontalLayout_39")
        self.label_30 = QLabel(self.groupBox_9)
        self.label_30.setObjectName(u"label_30")
        sizePolicy1.setHeightForWidth(self.label_30.sizePolicy().hasHeightForWidth())
        self.label_30.setSizePolicy(sizePolicy1)
        self.label_30.setMinimumSize(QSize(30, 0))
        self.label_30.setMaximumSize(QSize(16777215, 16777215))
        self.label_30.setFont(font1)

        self.horizontalLayout_39.addWidget(self.label_30)

        self.dSBoxSmallSPLBand_1 = QDoubleSpinBox(self.groupBox_9)
        self.dSBoxSmallSPLBand_1.setObjectName(u"dSBoxSmallSPLBand_1")
        self.dSBoxSmallSPLBand_1.setDecimals(1)
        self.dSBoxSmallSPLBand_1.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_1.setMaximum(65535.500000000000000)

        self.horizontalLayout_39.addWidget(self.dSBoxSmallSPLBand_1)


        self.gridLayout_5.addLayout(self.horizontalLayout_39, 0, 0, 1, 1)

        self.horizontalLayout_40 = QHBoxLayout()
        self.horizontalLayout_40.setObjectName(u"horizontalLayout_40")
        self.label_31 = QLabel(self.groupBox_9)
        self.label_31.setObjectName(u"label_31")
        sizePolicy1.setHeightForWidth(self.label_31.sizePolicy().hasHeightForWidth())
        self.label_31.setSizePolicy(sizePolicy1)
        self.label_31.setMinimumSize(QSize(0, 0))
        self.label_31.setMaximumSize(QSize(16777215, 16777215))
        self.label_31.setFont(font1)

        self.horizontalLayout_40.addWidget(self.label_31)

        self.dSBoxSmallSPLGainBand_1 = QDoubleSpinBox(self.groupBox_9)
        self.dSBoxSmallSPLGainBand_1.setObjectName(u"dSBoxSmallSPLGainBand_1")
        self.dSBoxSmallSPLGainBand_1.setDecimals(1)
        self.dSBoxSmallSPLGainBand_1.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_1.setMaximum(65535.500000000000000)

        self.horizontalLayout_40.addWidget(self.dSBoxSmallSPLGainBand_1)


        self.gridLayout_5.addLayout(self.horizontalLayout_40, 0, 1, 1, 1)

        self.horizontalLayout_41 = QHBoxLayout()
        self.horizontalLayout_41.setObjectName(u"horizontalLayout_41")
        self.label_32 = QLabel(self.groupBox_9)
        self.label_32.setObjectName(u"label_32")
        sizePolicy1.setHeightForWidth(self.label_32.sizePolicy().hasHeightForWidth())
        self.label_32.setSizePolicy(sizePolicy1)
        self.label_32.setMinimumSize(QSize(30, 0))
        self.label_32.setMaximumSize(QSize(16777215, 16777215))
        self.label_32.setFont(font1)

        self.horizontalLayout_41.addWidget(self.label_32)

        self.dSBoxMediumSPLBand_1 = QDoubleSpinBox(self.groupBox_9)
        self.dSBoxMediumSPLBand_1.setObjectName(u"dSBoxMediumSPLBand_1")
        self.dSBoxMediumSPLBand_1.setDecimals(1)
        self.dSBoxMediumSPLBand_1.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_1.setMaximum(65535.500000000000000)

        self.horizontalLayout_41.addWidget(self.dSBoxMediumSPLBand_1)


        self.gridLayout_5.addLayout(self.horizontalLayout_41, 1, 0, 1, 1)

        self.horizontalLayout_42 = QHBoxLayout()
        self.horizontalLayout_42.setObjectName(u"horizontalLayout_42")
        self.label_33 = QLabel(self.groupBox_9)
        self.label_33.setObjectName(u"label_33")
        sizePolicy1.setHeightForWidth(self.label_33.sizePolicy().hasHeightForWidth())
        self.label_33.setSizePolicy(sizePolicy1)
        self.label_33.setMinimumSize(QSize(0, 0))
        self.label_33.setMaximumSize(QSize(16777215, 16777215))
        self.label_33.setFont(font1)

        self.horizontalLayout_42.addWidget(self.label_33)

        self.dSBoxMediumSPLGainBand_1 = QDoubleSpinBox(self.groupBox_9)
        self.dSBoxMediumSPLGainBand_1.setObjectName(u"dSBoxMediumSPLGainBand_1")
        self.dSBoxMediumSPLGainBand_1.setDecimals(1)
        self.dSBoxMediumSPLGainBand_1.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_1.setMaximum(65535.500000000000000)

        self.horizontalLayout_42.addWidget(self.dSBoxMediumSPLGainBand_1)


        self.gridLayout_5.addLayout(self.horizontalLayout_42, 1, 1, 1, 1)

        self.horizontalLayout_43 = QHBoxLayout()
        self.horizontalLayout_43.setObjectName(u"horizontalLayout_43")
        self.label_34 = QLabel(self.groupBox_9)
        self.label_34.setObjectName(u"label_34")
        sizePolicy1.setHeightForWidth(self.label_34.sizePolicy().hasHeightForWidth())
        self.label_34.setSizePolicy(sizePolicy1)
        self.label_34.setMinimumSize(QSize(30, 0))
        self.label_34.setMaximumSize(QSize(16777215, 16777215))
        self.label_34.setFont(font1)

        self.horizontalLayout_43.addWidget(self.label_34)

        self.dSBoxLargeSPLBand_1 = QDoubleSpinBox(self.groupBox_9)
        self.dSBoxLargeSPLBand_1.setObjectName(u"dSBoxLargeSPLBand_1")
        self.dSBoxLargeSPLBand_1.setDecimals(1)
        self.dSBoxLargeSPLBand_1.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_1.setMaximum(65535.500000000000000)

        self.horizontalLayout_43.addWidget(self.dSBoxLargeSPLBand_1)


        self.gridLayout_5.addLayout(self.horizontalLayout_43, 2, 0, 1, 1)

        self.horizontalLayout_44 = QHBoxLayout()
        self.horizontalLayout_44.setObjectName(u"horizontalLayout_44")
        self.label_35 = QLabel(self.groupBox_9)
        self.label_35.setObjectName(u"label_35")
        sizePolicy1.setHeightForWidth(self.label_35.sizePolicy().hasHeightForWidth())
        self.label_35.setSizePolicy(sizePolicy1)
        self.label_35.setMinimumSize(QSize(0, 0))
        self.label_35.setMaximumSize(QSize(16777215, 16777215))
        self.label_35.setFont(font1)

        self.horizontalLayout_44.addWidget(self.label_35)

        self.dSBoxLargeSPLGainBand_1 = QDoubleSpinBox(self.groupBox_9)
        self.dSBoxLargeSPLGainBand_1.setObjectName(u"dSBoxLargeSPLGainBand_1")
        self.dSBoxLargeSPLGainBand_1.setDecimals(1)
        self.dSBoxLargeSPLGainBand_1.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_1.setMaximum(65535.500000000000000)

        self.horizontalLayout_44.addWidget(self.dSBoxLargeSPLGainBand_1)


        self.gridLayout_5.addLayout(self.horizontalLayout_44, 2, 1, 1, 1)


        self.verticalLayout_5.addWidget(self.groupBox_9)


        self.gridLayout_15.addWidget(self.gpBand_1, 0, 1, 1, 1)

        self.gpBand_12 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_12.setObjectName(u"gpBand_12")
        sizePolicy8.setHeightForWidth(self.gpBand_12.sizePolicy().hasHeightForWidth())
        self.gpBand_12.setSizePolicy(sizePolicy8)
        self.gpBand_12.setMinimumSize(QSize(0, 0))
        self.gpBand_12.setFont(font1)
        self.verticalLayout_16 = QVBoxLayout(self.gpBand_12)
        self.verticalLayout_16.setObjectName(u"verticalLayout_16")
        self.horizontalLayout_158 = QHBoxLayout()
        self.horizontalLayout_158.setObjectName(u"horizontalLayout_158")
        self.horizontalLayout_158.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_159 = QHBoxLayout()
        self.horizontalLayout_159.setObjectName(u"horizontalLayout_159")
        self.label_141 = QLabel(self.gpBand_12)
        self.label_141.setObjectName(u"label_141")
        sizePolicy1.setHeightForWidth(self.label_141.sizePolicy().hasHeightForWidth())
        self.label_141.setSizePolicy(sizePolicy1)
        self.label_141.setMinimumSize(QSize(0, 0))
        self.label_141.setMaximumSize(QSize(16777215, 16777215))
        self.label_141.setFont(font1)

        self.horizontalLayout_159.addWidget(self.label_141)

        self.spinBoxAttackBand_12 = QSpinBox(self.gpBand_12)
        self.spinBoxAttackBand_12.setObjectName(u"spinBoxAttackBand_12")
        self.spinBoxAttackBand_12.setMaximum(65535)

        self.horizontalLayout_159.addWidget(self.spinBoxAttackBand_12)


        self.horizontalLayout_158.addLayout(self.horizontalLayout_159)

        self.horizontalLayout_160 = QHBoxLayout()
        self.horizontalLayout_160.setObjectName(u"horizontalLayout_160")
        self.label_142 = QLabel(self.gpBand_12)
        self.label_142.setObjectName(u"label_142")
        sizePolicy1.setHeightForWidth(self.label_142.sizePolicy().hasHeightForWidth())
        self.label_142.setSizePolicy(sizePolicy1)
        self.label_142.setMinimumSize(QSize(0, 0))
        self.label_142.setMaximumSize(QSize(16777215, 16777215))
        self.label_142.setFont(font1)

        self.horizontalLayout_160.addWidget(self.label_142)

        self.spinBoxReleaseBand_12 = QSpinBox(self.gpBand_12)
        self.spinBoxReleaseBand_12.setObjectName(u"spinBoxReleaseBand_12")
        self.spinBoxReleaseBand_12.setMaximum(65535)

        self.horizontalLayout_160.addWidget(self.spinBoxReleaseBand_12)


        self.horizontalLayout_158.addLayout(self.horizontalLayout_160)


        self.verticalLayout_16.addLayout(self.horizontalLayout_158)

        self.gpLimitBand_12 = QGroupBox(self.gpBand_12)
        self.gpLimitBand_12.setObjectName(u"gpLimitBand_12")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_12.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_12.setSizePolicy(sizePolicy6)
        self.gpLimitBand_12.setFont(font1)
        self.gridLayout_28 = QGridLayout(self.gpLimitBand_12)
        self.gridLayout_28.setObjectName(u"gridLayout_28")
        self.gridLayout_28.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_161 = QHBoxLayout()
        self.horizontalLayout_161.setObjectName(u"horizontalLayout_161")
        self.label_143 = QLabel(self.gpLimitBand_12)
        self.label_143.setObjectName(u"label_143")
        sizePolicy1.setHeightForWidth(self.label_143.sizePolicy().hasHeightForWidth())
        self.label_143.setSizePolicy(sizePolicy1)
        self.label_143.setMinimumSize(QSize(0, 0))
        self.label_143.setMaximumSize(QSize(16777215, 16777215))
        self.label_143.setFont(font1)

        self.horizontalLayout_161.addWidget(self.label_143)

        self.dSBoxLimitThresholdBand_12 = QDoubleSpinBox(self.gpLimitBand_12)
        self.dSBoxLimitThresholdBand_12.setObjectName(u"dSBoxLimitThresholdBand_12")
        self.dSBoxLimitThresholdBand_12.setDecimals(1)
        self.dSBoxLimitThresholdBand_12.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_12.setMaximum(65535.500000000000000)

        self.horizontalLayout_161.addWidget(self.dSBoxLimitThresholdBand_12)


        self.gridLayout_28.addLayout(self.horizontalLayout_161, 0, 0, 1, 1)

        self.horizontalLayout_162 = QHBoxLayout()
        self.horizontalLayout_162.setObjectName(u"horizontalLayout_162")
        self.label_144 = QLabel(self.gpLimitBand_12)
        self.label_144.setObjectName(u"label_144")
        sizePolicy1.setHeightForWidth(self.label_144.sizePolicy().hasHeightForWidth())
        self.label_144.setSizePolicy(sizePolicy1)
        self.label_144.setMinimumSize(QSize(0, 0))
        self.label_144.setMaximumSize(QSize(16777215, 16777215))
        self.label_144.setFont(font1)

        self.horizontalLayout_162.addWidget(self.label_144)

        self.dSBoxLimitKneeBand_12 = QDoubleSpinBox(self.gpLimitBand_12)
        self.dSBoxLimitKneeBand_12.setObjectName(u"dSBoxLimitKneeBand_12")
        self.dSBoxLimitKneeBand_12.setDecimals(1)
        self.dSBoxLimitKneeBand_12.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_12.setMaximum(30.000000000000000)

        self.horizontalLayout_162.addWidget(self.dSBoxLimitKneeBand_12)


        self.gridLayout_28.addLayout(self.horizontalLayout_162, 0, 1, 1, 1)


        self.verticalLayout_16.addWidget(self.gpLimitBand_12)

        self.groupBox_20 = QGroupBox(self.gpBand_12)
        self.groupBox_20.setObjectName(u"groupBox_20")
        sizePolicy6.setHeightForWidth(self.groupBox_20.sizePolicy().hasHeightForWidth())
        self.groupBox_20.setSizePolicy(sizePolicy6)
        self.gridLayout_29 = QGridLayout(self.groupBox_20)
        self.gridLayout_29.setObjectName(u"gridLayout_29")
        self.gridLayout_29.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_163 = QHBoxLayout()
        self.horizontalLayout_163.setObjectName(u"horizontalLayout_163")
        self.label_145 = QLabel(self.groupBox_20)
        self.label_145.setObjectName(u"label_145")
        sizePolicy1.setHeightForWidth(self.label_145.sizePolicy().hasHeightForWidth())
        self.label_145.setSizePolicy(sizePolicy1)
        self.label_145.setMinimumSize(QSize(30, 0))
        self.label_145.setMaximumSize(QSize(16777215, 16777215))
        self.label_145.setFont(font1)

        self.horizontalLayout_163.addWidget(self.label_145)

        self.dSBoxSmallSPLBand_12 = QDoubleSpinBox(self.groupBox_20)
        self.dSBoxSmallSPLBand_12.setObjectName(u"dSBoxSmallSPLBand_12")
        self.dSBoxSmallSPLBand_12.setDecimals(1)
        self.dSBoxSmallSPLBand_12.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_12.setMaximum(65535.500000000000000)

        self.horizontalLayout_163.addWidget(self.dSBoxSmallSPLBand_12)


        self.gridLayout_29.addLayout(self.horizontalLayout_163, 0, 0, 1, 1)

        self.horizontalLayout_164 = QHBoxLayout()
        self.horizontalLayout_164.setObjectName(u"horizontalLayout_164")
        self.label_146 = QLabel(self.groupBox_20)
        self.label_146.setObjectName(u"label_146")
        sizePolicy1.setHeightForWidth(self.label_146.sizePolicy().hasHeightForWidth())
        self.label_146.setSizePolicy(sizePolicy1)
        self.label_146.setMinimumSize(QSize(0, 0))
        self.label_146.setMaximumSize(QSize(16777215, 16777215))
        self.label_146.setFont(font1)

        self.horizontalLayout_164.addWidget(self.label_146)

        self.dSBoxSmallSPLGainBand_12 = QDoubleSpinBox(self.groupBox_20)
        self.dSBoxSmallSPLGainBand_12.setObjectName(u"dSBoxSmallSPLGainBand_12")
        self.dSBoxSmallSPLGainBand_12.setDecimals(1)
        self.dSBoxSmallSPLGainBand_12.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_12.setMaximum(65535.500000000000000)

        self.horizontalLayout_164.addWidget(self.dSBoxSmallSPLGainBand_12)


        self.gridLayout_29.addLayout(self.horizontalLayout_164, 0, 1, 1, 1)

        self.horizontalLayout_165 = QHBoxLayout()
        self.horizontalLayout_165.setObjectName(u"horizontalLayout_165")
        self.label_147 = QLabel(self.groupBox_20)
        self.label_147.setObjectName(u"label_147")
        sizePolicy1.setHeightForWidth(self.label_147.sizePolicy().hasHeightForWidth())
        self.label_147.setSizePolicy(sizePolicy1)
        self.label_147.setMinimumSize(QSize(30, 0))
        self.label_147.setMaximumSize(QSize(16777215, 16777215))
        self.label_147.setFont(font1)

        self.horizontalLayout_165.addWidget(self.label_147)

        self.dSBoxMediumSPLBand_12 = QDoubleSpinBox(self.groupBox_20)
        self.dSBoxMediumSPLBand_12.setObjectName(u"dSBoxMediumSPLBand_12")
        self.dSBoxMediumSPLBand_12.setDecimals(1)
        self.dSBoxMediumSPLBand_12.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_12.setMaximum(65535.500000000000000)

        self.horizontalLayout_165.addWidget(self.dSBoxMediumSPLBand_12)


        self.gridLayout_29.addLayout(self.horizontalLayout_165, 1, 0, 1, 1)

        self.horizontalLayout_166 = QHBoxLayout()
        self.horizontalLayout_166.setObjectName(u"horizontalLayout_166")
        self.label_148 = QLabel(self.groupBox_20)
        self.label_148.setObjectName(u"label_148")
        sizePolicy1.setHeightForWidth(self.label_148.sizePolicy().hasHeightForWidth())
        self.label_148.setSizePolicy(sizePolicy1)
        self.label_148.setMinimumSize(QSize(0, 0))
        self.label_148.setMaximumSize(QSize(16777215, 16777215))
        self.label_148.setFont(font1)

        self.horizontalLayout_166.addWidget(self.label_148)

        self.dSBoxMediumSPLGainBand_12 = QDoubleSpinBox(self.groupBox_20)
        self.dSBoxMediumSPLGainBand_12.setObjectName(u"dSBoxMediumSPLGainBand_12")
        self.dSBoxMediumSPLGainBand_12.setDecimals(1)
        self.dSBoxMediumSPLGainBand_12.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_12.setMaximum(65535.500000000000000)

        self.horizontalLayout_166.addWidget(self.dSBoxMediumSPLGainBand_12)


        self.gridLayout_29.addLayout(self.horizontalLayout_166, 1, 1, 1, 1)

        self.horizontalLayout_167 = QHBoxLayout()
        self.horizontalLayout_167.setObjectName(u"horizontalLayout_167")
        self.label_149 = QLabel(self.groupBox_20)
        self.label_149.setObjectName(u"label_149")
        sizePolicy1.setHeightForWidth(self.label_149.sizePolicy().hasHeightForWidth())
        self.label_149.setSizePolicy(sizePolicy1)
        self.label_149.setMinimumSize(QSize(30, 0))
        self.label_149.setMaximumSize(QSize(16777215, 16777215))
        self.label_149.setFont(font1)

        self.horizontalLayout_167.addWidget(self.label_149)

        self.dSBoxLargeSPLBand_12 = QDoubleSpinBox(self.groupBox_20)
        self.dSBoxLargeSPLBand_12.setObjectName(u"dSBoxLargeSPLBand_12")
        self.dSBoxLargeSPLBand_12.setDecimals(1)
        self.dSBoxLargeSPLBand_12.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_12.setMaximum(65535.500000000000000)

        self.horizontalLayout_167.addWidget(self.dSBoxLargeSPLBand_12)


        self.gridLayout_29.addLayout(self.horizontalLayout_167, 2, 0, 1, 1)

        self.horizontalLayout_168 = QHBoxLayout()
        self.horizontalLayout_168.setObjectName(u"horizontalLayout_168")
        self.label_150 = QLabel(self.groupBox_20)
        self.label_150.setObjectName(u"label_150")
        sizePolicy1.setHeightForWidth(self.label_150.sizePolicy().hasHeightForWidth())
        self.label_150.setSizePolicy(sizePolicy1)
        self.label_150.setMinimumSize(QSize(0, 0))
        self.label_150.setMaximumSize(QSize(16777215, 16777215))
        self.label_150.setFont(font1)

        self.horizontalLayout_168.addWidget(self.label_150)

        self.dSBoxLargeSPLGainBand_12 = QDoubleSpinBox(self.groupBox_20)
        self.dSBoxLargeSPLGainBand_12.setObjectName(u"dSBoxLargeSPLGainBand_12")
        self.dSBoxLargeSPLGainBand_12.setDecimals(1)
        self.dSBoxLargeSPLGainBand_12.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_12.setMaximum(65535.500000000000000)

        self.horizontalLayout_168.addWidget(self.dSBoxLargeSPLGainBand_12)


        self.gridLayout_29.addLayout(self.horizontalLayout_168, 2, 1, 1, 1)


        self.verticalLayout_16.addWidget(self.groupBox_20)


        self.gridLayout_15.addWidget(self.gpBand_12, 7, 0, 1, 1)

        self.gpBand_13 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_13.setObjectName(u"gpBand_13")
        sizePolicy8.setHeightForWidth(self.gpBand_13.sizePolicy().hasHeightForWidth())
        self.gpBand_13.setSizePolicy(sizePolicy8)
        self.gpBand_13.setMinimumSize(QSize(0, 0))
        self.gpBand_13.setFont(font1)
        self.verticalLayout_17 = QVBoxLayout(self.gpBand_13)
        self.verticalLayout_17.setObjectName(u"verticalLayout_17")
        self.horizontalLayout_169 = QHBoxLayout()
        self.horizontalLayout_169.setObjectName(u"horizontalLayout_169")
        self.horizontalLayout_169.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_170 = QHBoxLayout()
        self.horizontalLayout_170.setObjectName(u"horizontalLayout_170")
        self.label_151 = QLabel(self.gpBand_13)
        self.label_151.setObjectName(u"label_151")
        sizePolicy1.setHeightForWidth(self.label_151.sizePolicy().hasHeightForWidth())
        self.label_151.setSizePolicy(sizePolicy1)
        self.label_151.setMinimumSize(QSize(0, 0))
        self.label_151.setMaximumSize(QSize(16777215, 16777215))
        self.label_151.setFont(font1)

        self.horizontalLayout_170.addWidget(self.label_151)

        self.spinBoxAttackBand_13 = QSpinBox(self.gpBand_13)
        self.spinBoxAttackBand_13.setObjectName(u"spinBoxAttackBand_13")
        self.spinBoxAttackBand_13.setMaximum(65535)

        self.horizontalLayout_170.addWidget(self.spinBoxAttackBand_13)


        self.horizontalLayout_169.addLayout(self.horizontalLayout_170)

        self.horizontalLayout_171 = QHBoxLayout()
        self.horizontalLayout_171.setObjectName(u"horizontalLayout_171")
        self.label_152 = QLabel(self.gpBand_13)
        self.label_152.setObjectName(u"label_152")
        sizePolicy1.setHeightForWidth(self.label_152.sizePolicy().hasHeightForWidth())
        self.label_152.setSizePolicy(sizePolicy1)
        self.label_152.setMinimumSize(QSize(0, 0))
        self.label_152.setMaximumSize(QSize(16777215, 16777215))
        self.label_152.setFont(font1)

        self.horizontalLayout_171.addWidget(self.label_152)

        self.spinBoxReleaseBand_13 = QSpinBox(self.gpBand_13)
        self.spinBoxReleaseBand_13.setObjectName(u"spinBoxReleaseBand_13")
        self.spinBoxReleaseBand_13.setMaximum(65535)

        self.horizontalLayout_171.addWidget(self.spinBoxReleaseBand_13)


        self.horizontalLayout_169.addLayout(self.horizontalLayout_171)


        self.verticalLayout_17.addLayout(self.horizontalLayout_169)

        self.gpLimitBand_13 = QGroupBox(self.gpBand_13)
        self.gpLimitBand_13.setObjectName(u"gpLimitBand_13")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_13.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_13.setSizePolicy(sizePolicy6)
        self.gpLimitBand_13.setFont(font1)
        self.gridLayout_30 = QGridLayout(self.gpLimitBand_13)
        self.gridLayout_30.setObjectName(u"gridLayout_30")
        self.gridLayout_30.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_172 = QHBoxLayout()
        self.horizontalLayout_172.setObjectName(u"horizontalLayout_172")
        self.label_153 = QLabel(self.gpLimitBand_13)
        self.label_153.setObjectName(u"label_153")
        sizePolicy1.setHeightForWidth(self.label_153.sizePolicy().hasHeightForWidth())
        self.label_153.setSizePolicy(sizePolicy1)
        self.label_153.setMinimumSize(QSize(0, 0))
        self.label_153.setMaximumSize(QSize(16777215, 16777215))
        self.label_153.setFont(font1)

        self.horizontalLayout_172.addWidget(self.label_153)

        self.dSBoxLimitThresholdBand_13 = QDoubleSpinBox(self.gpLimitBand_13)
        self.dSBoxLimitThresholdBand_13.setObjectName(u"dSBoxLimitThresholdBand_13")
        self.dSBoxLimitThresholdBand_13.setDecimals(1)
        self.dSBoxLimitThresholdBand_13.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_13.setMaximum(65535.500000000000000)

        self.horizontalLayout_172.addWidget(self.dSBoxLimitThresholdBand_13)


        self.gridLayout_30.addLayout(self.horizontalLayout_172, 0, 0, 1, 1)

        self.horizontalLayout_173 = QHBoxLayout()
        self.horizontalLayout_173.setObjectName(u"horizontalLayout_173")
        self.label_154 = QLabel(self.gpLimitBand_13)
        self.label_154.setObjectName(u"label_154")
        sizePolicy1.setHeightForWidth(self.label_154.sizePolicy().hasHeightForWidth())
        self.label_154.setSizePolicy(sizePolicy1)
        self.label_154.setMinimumSize(QSize(0, 0))
        self.label_154.setMaximumSize(QSize(16777215, 16777215))
        self.label_154.setFont(font1)

        self.horizontalLayout_173.addWidget(self.label_154)

        self.dSBoxLimitKneeBand_13 = QDoubleSpinBox(self.gpLimitBand_13)
        self.dSBoxLimitKneeBand_13.setObjectName(u"dSBoxLimitKneeBand_13")
        self.dSBoxLimitKneeBand_13.setDecimals(1)
        self.dSBoxLimitKneeBand_13.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_13.setMaximum(30.000000000000000)

        self.horizontalLayout_173.addWidget(self.dSBoxLimitKneeBand_13)


        self.gridLayout_30.addLayout(self.horizontalLayout_173, 0, 1, 1, 1)


        self.verticalLayout_17.addWidget(self.gpLimitBand_13)

        self.groupBox_21 = QGroupBox(self.gpBand_13)
        self.groupBox_21.setObjectName(u"groupBox_21")
        sizePolicy6.setHeightForWidth(self.groupBox_21.sizePolicy().hasHeightForWidth())
        self.groupBox_21.setSizePolicy(sizePolicy6)
        self.gridLayout_31 = QGridLayout(self.groupBox_21)
        self.gridLayout_31.setObjectName(u"gridLayout_31")
        self.gridLayout_31.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_174 = QHBoxLayout()
        self.horizontalLayout_174.setObjectName(u"horizontalLayout_174")
        self.label_155 = QLabel(self.groupBox_21)
        self.label_155.setObjectName(u"label_155")
        sizePolicy1.setHeightForWidth(self.label_155.sizePolicy().hasHeightForWidth())
        self.label_155.setSizePolicy(sizePolicy1)
        self.label_155.setMinimumSize(QSize(30, 0))
        self.label_155.setMaximumSize(QSize(16777215, 16777215))
        self.label_155.setFont(font1)

        self.horizontalLayout_174.addWidget(self.label_155)

        self.dSBoxSmallSPLBand_13 = QDoubleSpinBox(self.groupBox_21)
        self.dSBoxSmallSPLBand_13.setObjectName(u"dSBoxSmallSPLBand_13")
        self.dSBoxSmallSPLBand_13.setDecimals(1)
        self.dSBoxSmallSPLBand_13.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_13.setMaximum(65535.500000000000000)

        self.horizontalLayout_174.addWidget(self.dSBoxSmallSPLBand_13)


        self.gridLayout_31.addLayout(self.horizontalLayout_174, 0, 0, 1, 1)

        self.horizontalLayout_175 = QHBoxLayout()
        self.horizontalLayout_175.setObjectName(u"horizontalLayout_175")
        self.label_156 = QLabel(self.groupBox_21)
        self.label_156.setObjectName(u"label_156")
        sizePolicy1.setHeightForWidth(self.label_156.sizePolicy().hasHeightForWidth())
        self.label_156.setSizePolicy(sizePolicy1)
        self.label_156.setMinimumSize(QSize(0, 0))
        self.label_156.setMaximumSize(QSize(16777215, 16777215))
        self.label_156.setFont(font1)

        self.horizontalLayout_175.addWidget(self.label_156)

        self.dSBoxSmallSPLGainBand_13 = QDoubleSpinBox(self.groupBox_21)
        self.dSBoxSmallSPLGainBand_13.setObjectName(u"dSBoxSmallSPLGainBand_13")
        self.dSBoxSmallSPLGainBand_13.setDecimals(1)
        self.dSBoxSmallSPLGainBand_13.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_13.setMaximum(65535.500000000000000)

        self.horizontalLayout_175.addWidget(self.dSBoxSmallSPLGainBand_13)


        self.gridLayout_31.addLayout(self.horizontalLayout_175, 0, 1, 1, 1)

        self.horizontalLayout_176 = QHBoxLayout()
        self.horizontalLayout_176.setObjectName(u"horizontalLayout_176")
        self.label_157 = QLabel(self.groupBox_21)
        self.label_157.setObjectName(u"label_157")
        sizePolicy1.setHeightForWidth(self.label_157.sizePolicy().hasHeightForWidth())
        self.label_157.setSizePolicy(sizePolicy1)
        self.label_157.setMinimumSize(QSize(30, 0))
        self.label_157.setMaximumSize(QSize(16777215, 16777215))
        self.label_157.setFont(font1)

        self.horizontalLayout_176.addWidget(self.label_157)

        self.dSBoxMediumSPLBand_13 = QDoubleSpinBox(self.groupBox_21)
        self.dSBoxMediumSPLBand_13.setObjectName(u"dSBoxMediumSPLBand_13")
        self.dSBoxMediumSPLBand_13.setDecimals(1)
        self.dSBoxMediumSPLBand_13.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_13.setMaximum(65535.500000000000000)

        self.horizontalLayout_176.addWidget(self.dSBoxMediumSPLBand_13)


        self.gridLayout_31.addLayout(self.horizontalLayout_176, 1, 0, 1, 1)

        self.horizontalLayout_177 = QHBoxLayout()
        self.horizontalLayout_177.setObjectName(u"horizontalLayout_177")
        self.label_158 = QLabel(self.groupBox_21)
        self.label_158.setObjectName(u"label_158")
        sizePolicy1.setHeightForWidth(self.label_158.sizePolicy().hasHeightForWidth())
        self.label_158.setSizePolicy(sizePolicy1)
        self.label_158.setMinimumSize(QSize(0, 0))
        self.label_158.setMaximumSize(QSize(16777215, 16777215))
        self.label_158.setFont(font1)

        self.horizontalLayout_177.addWidget(self.label_158)

        self.dSBoxMediumSPLGainBand_13 = QDoubleSpinBox(self.groupBox_21)
        self.dSBoxMediumSPLGainBand_13.setObjectName(u"dSBoxMediumSPLGainBand_13")
        self.dSBoxMediumSPLGainBand_13.setDecimals(1)
        self.dSBoxMediumSPLGainBand_13.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_13.setMaximum(65535.500000000000000)

        self.horizontalLayout_177.addWidget(self.dSBoxMediumSPLGainBand_13)


        self.gridLayout_31.addLayout(self.horizontalLayout_177, 1, 1, 1, 1)

        self.horizontalLayout_178 = QHBoxLayout()
        self.horizontalLayout_178.setObjectName(u"horizontalLayout_178")
        self.label_159 = QLabel(self.groupBox_21)
        self.label_159.setObjectName(u"label_159")
        sizePolicy1.setHeightForWidth(self.label_159.sizePolicy().hasHeightForWidth())
        self.label_159.setSizePolicy(sizePolicy1)
        self.label_159.setMinimumSize(QSize(30, 0))
        self.label_159.setMaximumSize(QSize(16777215, 16777215))
        self.label_159.setFont(font1)

        self.horizontalLayout_178.addWidget(self.label_159)

        self.dSBoxLargeSPLBand_13 = QDoubleSpinBox(self.groupBox_21)
        self.dSBoxLargeSPLBand_13.setObjectName(u"dSBoxLargeSPLBand_13")
        self.dSBoxLargeSPLBand_13.setDecimals(1)
        self.dSBoxLargeSPLBand_13.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_13.setMaximum(65535.500000000000000)

        self.horizontalLayout_178.addWidget(self.dSBoxLargeSPLBand_13)


        self.gridLayout_31.addLayout(self.horizontalLayout_178, 2, 0, 1, 1)

        self.horizontalLayout_179 = QHBoxLayout()
        self.horizontalLayout_179.setObjectName(u"horizontalLayout_179")
        self.label_160 = QLabel(self.groupBox_21)
        self.label_160.setObjectName(u"label_160")
        sizePolicy1.setHeightForWidth(self.label_160.sizePolicy().hasHeightForWidth())
        self.label_160.setSizePolicy(sizePolicy1)
        self.label_160.setMinimumSize(QSize(0, 0))
        self.label_160.setMaximumSize(QSize(16777215, 16777215))
        self.label_160.setFont(font1)

        self.horizontalLayout_179.addWidget(self.label_160)

        self.dSBoxLargeSPLGainBand_13 = QDoubleSpinBox(self.groupBox_21)
        self.dSBoxLargeSPLGainBand_13.setObjectName(u"dSBoxLargeSPLGainBand_13")
        self.dSBoxLargeSPLGainBand_13.setDecimals(1)
        self.dSBoxLargeSPLGainBand_13.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_13.setMaximum(65535.500000000000000)

        self.horizontalLayout_179.addWidget(self.dSBoxLargeSPLGainBand_13)


        self.gridLayout_31.addLayout(self.horizontalLayout_179, 2, 1, 1, 1)


        self.verticalLayout_17.addWidget(self.groupBox_21)


        self.gridLayout_15.addWidget(self.gpBand_13, 7, 1, 1, 1)

        self.gpBand_11 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_11.setObjectName(u"gpBand_11")
        sizePolicy8.setHeightForWidth(self.gpBand_11.sizePolicy().hasHeightForWidth())
        self.gpBand_11.setSizePolicy(sizePolicy8)
        self.gpBand_11.setMinimumSize(QSize(0, 0))
        self.gpBand_11.setFont(font1)
        self.verticalLayout_15 = QVBoxLayout(self.gpBand_11)
        self.verticalLayout_15.setObjectName(u"verticalLayout_15")
        self.horizontalLayout_147 = QHBoxLayout()
        self.horizontalLayout_147.setObjectName(u"horizontalLayout_147")
        self.horizontalLayout_147.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_148 = QHBoxLayout()
        self.horizontalLayout_148.setObjectName(u"horizontalLayout_148")
        self.label_131 = QLabel(self.gpBand_11)
        self.label_131.setObjectName(u"label_131")
        sizePolicy1.setHeightForWidth(self.label_131.sizePolicy().hasHeightForWidth())
        self.label_131.setSizePolicy(sizePolicy1)
        self.label_131.setMinimumSize(QSize(0, 0))
        self.label_131.setMaximumSize(QSize(16777215, 16777215))
        self.label_131.setFont(font1)

        self.horizontalLayout_148.addWidget(self.label_131)

        self.spinBoxAttackBand_11 = QSpinBox(self.gpBand_11)
        self.spinBoxAttackBand_11.setObjectName(u"spinBoxAttackBand_11")
        self.spinBoxAttackBand_11.setMaximum(65535)

        self.horizontalLayout_148.addWidget(self.spinBoxAttackBand_11)


        self.horizontalLayout_147.addLayout(self.horizontalLayout_148)

        self.horizontalLayout_149 = QHBoxLayout()
        self.horizontalLayout_149.setObjectName(u"horizontalLayout_149")
        self.label_132 = QLabel(self.gpBand_11)
        self.label_132.setObjectName(u"label_132")
        sizePolicy1.setHeightForWidth(self.label_132.sizePolicy().hasHeightForWidth())
        self.label_132.setSizePolicy(sizePolicy1)
        self.label_132.setMinimumSize(QSize(0, 0))
        self.label_132.setMaximumSize(QSize(16777215, 16777215))
        self.label_132.setFont(font1)

        self.horizontalLayout_149.addWidget(self.label_132)

        self.spinBoxReleaseBand_11 = QSpinBox(self.gpBand_11)
        self.spinBoxReleaseBand_11.setObjectName(u"spinBoxReleaseBand_11")
        self.spinBoxReleaseBand_11.setMaximum(65535)

        self.horizontalLayout_149.addWidget(self.spinBoxReleaseBand_11)


        self.horizontalLayout_147.addLayout(self.horizontalLayout_149)


        self.verticalLayout_15.addLayout(self.horizontalLayout_147)

        self.gpLimitBand_11 = QGroupBox(self.gpBand_11)
        self.gpLimitBand_11.setObjectName(u"gpLimitBand_11")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_11.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_11.setSizePolicy(sizePolicy6)
        self.gpLimitBand_11.setFont(font1)
        self.gridLayout_26 = QGridLayout(self.gpLimitBand_11)
        self.gridLayout_26.setObjectName(u"gridLayout_26")
        self.gridLayout_26.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_150 = QHBoxLayout()
        self.horizontalLayout_150.setObjectName(u"horizontalLayout_150")
        self.label_133 = QLabel(self.gpLimitBand_11)
        self.label_133.setObjectName(u"label_133")
        sizePolicy1.setHeightForWidth(self.label_133.sizePolicy().hasHeightForWidth())
        self.label_133.setSizePolicy(sizePolicy1)
        self.label_133.setMinimumSize(QSize(0, 0))
        self.label_133.setMaximumSize(QSize(16777215, 16777215))
        self.label_133.setFont(font1)

        self.horizontalLayout_150.addWidget(self.label_133)

        self.dSBoxLimitThresholdBand_11 = QDoubleSpinBox(self.gpLimitBand_11)
        self.dSBoxLimitThresholdBand_11.setObjectName(u"dSBoxLimitThresholdBand_11")
        self.dSBoxLimitThresholdBand_11.setDecimals(1)
        self.dSBoxLimitThresholdBand_11.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_11.setMaximum(65535.500000000000000)

        self.horizontalLayout_150.addWidget(self.dSBoxLimitThresholdBand_11)


        self.gridLayout_26.addLayout(self.horizontalLayout_150, 0, 0, 1, 1)

        self.horizontalLayout_151 = QHBoxLayout()
        self.horizontalLayout_151.setObjectName(u"horizontalLayout_151")
        self.label_134 = QLabel(self.gpLimitBand_11)
        self.label_134.setObjectName(u"label_134")
        sizePolicy1.setHeightForWidth(self.label_134.sizePolicy().hasHeightForWidth())
        self.label_134.setSizePolicy(sizePolicy1)
        self.label_134.setMinimumSize(QSize(0, 0))
        self.label_134.setMaximumSize(QSize(16777215, 16777215))
        self.label_134.setFont(font1)

        self.horizontalLayout_151.addWidget(self.label_134)

        self.dSBoxLimitKneeBand_11 = QDoubleSpinBox(self.gpLimitBand_11)
        self.dSBoxLimitKneeBand_11.setObjectName(u"dSBoxLimitKneeBand_11")
        self.dSBoxLimitKneeBand_11.setDecimals(1)
        self.dSBoxLimitKneeBand_11.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_11.setMaximum(30.000000000000000)

        self.horizontalLayout_151.addWidget(self.dSBoxLimitKneeBand_11)


        self.gridLayout_26.addLayout(self.horizontalLayout_151, 0, 1, 1, 1)


        self.verticalLayout_15.addWidget(self.gpLimitBand_11)

        self.groupBox_19 = QGroupBox(self.gpBand_11)
        self.groupBox_19.setObjectName(u"groupBox_19")
        sizePolicy6.setHeightForWidth(self.groupBox_19.sizePolicy().hasHeightForWidth())
        self.groupBox_19.setSizePolicy(sizePolicy6)
        self.gridLayout_27 = QGridLayout(self.groupBox_19)
        self.gridLayout_27.setObjectName(u"gridLayout_27")
        self.gridLayout_27.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_152 = QHBoxLayout()
        self.horizontalLayout_152.setObjectName(u"horizontalLayout_152")
        self.label_135 = QLabel(self.groupBox_19)
        self.label_135.setObjectName(u"label_135")
        sizePolicy1.setHeightForWidth(self.label_135.sizePolicy().hasHeightForWidth())
        self.label_135.setSizePolicy(sizePolicy1)
        self.label_135.setMinimumSize(QSize(30, 0))
        self.label_135.setMaximumSize(QSize(16777215, 16777215))
        self.label_135.setFont(font1)

        self.horizontalLayout_152.addWidget(self.label_135)

        self.dSBoxSmallSPLBand_11 = QDoubleSpinBox(self.groupBox_19)
        self.dSBoxSmallSPLBand_11.setObjectName(u"dSBoxSmallSPLBand_11")
        self.dSBoxSmallSPLBand_11.setDecimals(1)
        self.dSBoxSmallSPLBand_11.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_11.setMaximum(65535.500000000000000)

        self.horizontalLayout_152.addWidget(self.dSBoxSmallSPLBand_11)


        self.gridLayout_27.addLayout(self.horizontalLayout_152, 0, 0, 1, 1)

        self.horizontalLayout_153 = QHBoxLayout()
        self.horizontalLayout_153.setObjectName(u"horizontalLayout_153")
        self.label_136 = QLabel(self.groupBox_19)
        self.label_136.setObjectName(u"label_136")
        sizePolicy1.setHeightForWidth(self.label_136.sizePolicy().hasHeightForWidth())
        self.label_136.setSizePolicy(sizePolicy1)
        self.label_136.setMinimumSize(QSize(0, 0))
        self.label_136.setMaximumSize(QSize(16777215, 16777215))
        self.label_136.setFont(font1)

        self.horizontalLayout_153.addWidget(self.label_136)

        self.dSBoxSmallSPLGainBand_11 = QDoubleSpinBox(self.groupBox_19)
        self.dSBoxSmallSPLGainBand_11.setObjectName(u"dSBoxSmallSPLGainBand_11")
        self.dSBoxSmallSPLGainBand_11.setDecimals(1)
        self.dSBoxSmallSPLGainBand_11.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_11.setMaximum(65535.500000000000000)

        self.horizontalLayout_153.addWidget(self.dSBoxSmallSPLGainBand_11)


        self.gridLayout_27.addLayout(self.horizontalLayout_153, 0, 1, 1, 1)

        self.horizontalLayout_154 = QHBoxLayout()
        self.horizontalLayout_154.setObjectName(u"horizontalLayout_154")
        self.label_137 = QLabel(self.groupBox_19)
        self.label_137.setObjectName(u"label_137")
        sizePolicy1.setHeightForWidth(self.label_137.sizePolicy().hasHeightForWidth())
        self.label_137.setSizePolicy(sizePolicy1)
        self.label_137.setMinimumSize(QSize(30, 0))
        self.label_137.setMaximumSize(QSize(16777215, 16777215))
        self.label_137.setFont(font1)

        self.horizontalLayout_154.addWidget(self.label_137)

        self.dSBoxMediumSPLBand_11 = QDoubleSpinBox(self.groupBox_19)
        self.dSBoxMediumSPLBand_11.setObjectName(u"dSBoxMediumSPLBand_11")
        self.dSBoxMediumSPLBand_11.setDecimals(1)
        self.dSBoxMediumSPLBand_11.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_11.setMaximum(65535.500000000000000)

        self.horizontalLayout_154.addWidget(self.dSBoxMediumSPLBand_11)


        self.gridLayout_27.addLayout(self.horizontalLayout_154, 1, 0, 1, 1)

        self.horizontalLayout_155 = QHBoxLayout()
        self.horizontalLayout_155.setObjectName(u"horizontalLayout_155")
        self.label_138 = QLabel(self.groupBox_19)
        self.label_138.setObjectName(u"label_138")
        sizePolicy1.setHeightForWidth(self.label_138.sizePolicy().hasHeightForWidth())
        self.label_138.setSizePolicy(sizePolicy1)
        self.label_138.setMinimumSize(QSize(0, 0))
        self.label_138.setMaximumSize(QSize(16777215, 16777215))
        self.label_138.setFont(font1)

        self.horizontalLayout_155.addWidget(self.label_138)

        self.dSBoxMediumSPLGainBand_11 = QDoubleSpinBox(self.groupBox_19)
        self.dSBoxMediumSPLGainBand_11.setObjectName(u"dSBoxMediumSPLGainBand_11")
        self.dSBoxMediumSPLGainBand_11.setDecimals(1)
        self.dSBoxMediumSPLGainBand_11.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_11.setMaximum(65535.500000000000000)

        self.horizontalLayout_155.addWidget(self.dSBoxMediumSPLGainBand_11)


        self.gridLayout_27.addLayout(self.horizontalLayout_155, 1, 1, 1, 1)

        self.horizontalLayout_156 = QHBoxLayout()
        self.horizontalLayout_156.setObjectName(u"horizontalLayout_156")
        self.label_139 = QLabel(self.groupBox_19)
        self.label_139.setObjectName(u"label_139")
        sizePolicy1.setHeightForWidth(self.label_139.sizePolicy().hasHeightForWidth())
        self.label_139.setSizePolicy(sizePolicy1)
        self.label_139.setMinimumSize(QSize(30, 0))
        self.label_139.setMaximumSize(QSize(16777215, 16777215))
        self.label_139.setFont(font1)

        self.horizontalLayout_156.addWidget(self.label_139)

        self.dSBoxLargeSPLBand_11 = QDoubleSpinBox(self.groupBox_19)
        self.dSBoxLargeSPLBand_11.setObjectName(u"dSBoxLargeSPLBand_11")
        self.dSBoxLargeSPLBand_11.setDecimals(1)
        self.dSBoxLargeSPLBand_11.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_11.setMaximum(65535.500000000000000)

        self.horizontalLayout_156.addWidget(self.dSBoxLargeSPLBand_11)


        self.gridLayout_27.addLayout(self.horizontalLayout_156, 2, 0, 1, 1)

        self.horizontalLayout_157 = QHBoxLayout()
        self.horizontalLayout_157.setObjectName(u"horizontalLayout_157")
        self.label_140 = QLabel(self.groupBox_19)
        self.label_140.setObjectName(u"label_140")
        sizePolicy1.setHeightForWidth(self.label_140.sizePolicy().hasHeightForWidth())
        self.label_140.setSizePolicy(sizePolicy1)
        self.label_140.setMinimumSize(QSize(0, 0))
        self.label_140.setMaximumSize(QSize(16777215, 16777215))
        self.label_140.setFont(font1)

        self.horizontalLayout_157.addWidget(self.label_140)

        self.dSBoxLargeSPLGainBand_11 = QDoubleSpinBox(self.groupBox_19)
        self.dSBoxLargeSPLGainBand_11.setObjectName(u"dSBoxLargeSPLGainBand_11")
        self.dSBoxLargeSPLGainBand_11.setDecimals(1)
        self.dSBoxLargeSPLGainBand_11.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_11.setMaximum(65535.500000000000000)

        self.horizontalLayout_157.addWidget(self.dSBoxLargeSPLGainBand_11)


        self.gridLayout_27.addLayout(self.horizontalLayout_157, 2, 1, 1, 1)


        self.verticalLayout_15.addWidget(self.groupBox_19)


        self.gridLayout_15.addWidget(self.gpBand_11, 6, 1, 1, 1)

        self.gpBand_6 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_6.setObjectName(u"gpBand_6")
        sizePolicy8.setHeightForWidth(self.gpBand_6.sizePolicy().hasHeightForWidth())
        self.gpBand_6.setSizePolicy(sizePolicy8)
        self.gpBand_6.setMinimumSize(QSize(0, 0))
        self.gpBand_6.setFont(font1)
        self.verticalLayout_10 = QVBoxLayout(self.gpBand_6)
        self.verticalLayout_10.setObjectName(u"verticalLayout_10")
        self.horizontalLayout_90 = QHBoxLayout()
        self.horizontalLayout_90.setObjectName(u"horizontalLayout_90")
        self.horizontalLayout_90.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_91 = QHBoxLayout()
        self.horizontalLayout_91.setObjectName(u"horizontalLayout_91")
        self.label_80 = QLabel(self.gpBand_6)
        self.label_80.setObjectName(u"label_80")
        sizePolicy1.setHeightForWidth(self.label_80.sizePolicy().hasHeightForWidth())
        self.label_80.setSizePolicy(sizePolicy1)
        self.label_80.setMinimumSize(QSize(0, 0))
        self.label_80.setMaximumSize(QSize(16777215, 16777215))
        self.label_80.setFont(font1)

        self.horizontalLayout_91.addWidget(self.label_80)

        self.spinBoxAttackBand_6 = QSpinBox(self.gpBand_6)
        self.spinBoxAttackBand_6.setObjectName(u"spinBoxAttackBand_6")
        self.spinBoxAttackBand_6.setMaximum(65535)

        self.horizontalLayout_91.addWidget(self.spinBoxAttackBand_6)


        self.horizontalLayout_90.addLayout(self.horizontalLayout_91)

        self.horizontalLayout_92 = QHBoxLayout()
        self.horizontalLayout_92.setObjectName(u"horizontalLayout_92")
        self.label_81 = QLabel(self.gpBand_6)
        self.label_81.setObjectName(u"label_81")
        sizePolicy1.setHeightForWidth(self.label_81.sizePolicy().hasHeightForWidth())
        self.label_81.setSizePolicy(sizePolicy1)
        self.label_81.setMinimumSize(QSize(0, 0))
        self.label_81.setMaximumSize(QSize(16777215, 16777215))
        self.label_81.setFont(font1)

        self.horizontalLayout_92.addWidget(self.label_81)

        self.spinBoxReleaseBand_6 = QSpinBox(self.gpBand_6)
        self.spinBoxReleaseBand_6.setObjectName(u"spinBoxReleaseBand_6")
        self.spinBoxReleaseBand_6.setMaximum(65535)

        self.horizontalLayout_92.addWidget(self.spinBoxReleaseBand_6)


        self.horizontalLayout_90.addLayout(self.horizontalLayout_92)


        self.verticalLayout_10.addLayout(self.horizontalLayout_90)

        self.gpLimitBand_6 = QGroupBox(self.gpBand_6)
        self.gpLimitBand_6.setObjectName(u"gpLimitBand_6")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_6.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_6.setSizePolicy(sizePolicy6)
        self.gpLimitBand_6.setFont(font1)
        self.gridLayout_16 = QGridLayout(self.gpLimitBand_6)
        self.gridLayout_16.setObjectName(u"gridLayout_16")
        self.gridLayout_16.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_94 = QHBoxLayout()
        self.horizontalLayout_94.setObjectName(u"horizontalLayout_94")
        self.label_82 = QLabel(self.gpLimitBand_6)
        self.label_82.setObjectName(u"label_82")
        sizePolicy1.setHeightForWidth(self.label_82.sizePolicy().hasHeightForWidth())
        self.label_82.setSizePolicy(sizePolicy1)
        self.label_82.setMinimumSize(QSize(0, 0))
        self.label_82.setMaximumSize(QSize(16777215, 16777215))
        self.label_82.setFont(font1)

        self.horizontalLayout_94.addWidget(self.label_82)

        self.dSBoxLimitThresholdBand_6 = QDoubleSpinBox(self.gpLimitBand_6)
        self.dSBoxLimitThresholdBand_6.setObjectName(u"dSBoxLimitThresholdBand_6")
        self.dSBoxLimitThresholdBand_6.setDecimals(1)
        self.dSBoxLimitThresholdBand_6.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_6.setMaximum(65535.500000000000000)

        self.horizontalLayout_94.addWidget(self.dSBoxLimitThresholdBand_6)


        self.gridLayout_16.addLayout(self.horizontalLayout_94, 0, 0, 1, 1)

        self.horizontalLayout_95 = QHBoxLayout()
        self.horizontalLayout_95.setObjectName(u"horizontalLayout_95")
        self.label_83 = QLabel(self.gpLimitBand_6)
        self.label_83.setObjectName(u"label_83")
        sizePolicy1.setHeightForWidth(self.label_83.sizePolicy().hasHeightForWidth())
        self.label_83.setSizePolicy(sizePolicy1)
        self.label_83.setMinimumSize(QSize(0, 0))
        self.label_83.setMaximumSize(QSize(16777215, 16777215))
        self.label_83.setFont(font1)

        self.horizontalLayout_95.addWidget(self.label_83)

        self.dSBoxLimitKneeBand_6 = QDoubleSpinBox(self.gpLimitBand_6)
        self.dSBoxLimitKneeBand_6.setObjectName(u"dSBoxLimitKneeBand_6")
        self.dSBoxLimitKneeBand_6.setDecimals(1)
        self.dSBoxLimitKneeBand_6.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_6.setMaximum(30.000000000000000)

        self.horizontalLayout_95.addWidget(self.dSBoxLimitKneeBand_6)


        self.gridLayout_16.addLayout(self.horizontalLayout_95, 0, 1, 1, 1)


        self.verticalLayout_10.addWidget(self.gpLimitBand_6)

        self.groupBox_14 = QGroupBox(self.gpBand_6)
        self.groupBox_14.setObjectName(u"groupBox_14")
        sizePolicy6.setHeightForWidth(self.groupBox_14.sizePolicy().hasHeightForWidth())
        self.groupBox_14.setSizePolicy(sizePolicy6)
        self.gridLayout_17 = QGridLayout(self.groupBox_14)
        self.gridLayout_17.setObjectName(u"gridLayout_17")
        self.gridLayout_17.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_96 = QHBoxLayout()
        self.horizontalLayout_96.setObjectName(u"horizontalLayout_96")
        self.label_84 = QLabel(self.groupBox_14)
        self.label_84.setObjectName(u"label_84")
        sizePolicy1.setHeightForWidth(self.label_84.sizePolicy().hasHeightForWidth())
        self.label_84.setSizePolicy(sizePolicy1)
        self.label_84.setMinimumSize(QSize(30, 0))
        self.label_84.setMaximumSize(QSize(16777215, 16777215))
        self.label_84.setFont(font1)

        self.horizontalLayout_96.addWidget(self.label_84)

        self.dSBoxSmallSPLBand_6 = QDoubleSpinBox(self.groupBox_14)
        self.dSBoxSmallSPLBand_6.setObjectName(u"dSBoxSmallSPLBand_6")
        self.dSBoxSmallSPLBand_6.setDecimals(1)
        self.dSBoxSmallSPLBand_6.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_6.setMaximum(65535.500000000000000)

        self.horizontalLayout_96.addWidget(self.dSBoxSmallSPLBand_6)


        self.gridLayout_17.addLayout(self.horizontalLayout_96, 0, 0, 1, 1)

        self.horizontalLayout_97 = QHBoxLayout()
        self.horizontalLayout_97.setObjectName(u"horizontalLayout_97")
        self.label_85 = QLabel(self.groupBox_14)
        self.label_85.setObjectName(u"label_85")
        sizePolicy1.setHeightForWidth(self.label_85.sizePolicy().hasHeightForWidth())
        self.label_85.setSizePolicy(sizePolicy1)
        self.label_85.setMinimumSize(QSize(0, 0))
        self.label_85.setMaximumSize(QSize(16777215, 16777215))
        self.label_85.setFont(font1)

        self.horizontalLayout_97.addWidget(self.label_85)

        self.dSBoxSmallSPLGainBand_6 = QDoubleSpinBox(self.groupBox_14)
        self.dSBoxSmallSPLGainBand_6.setObjectName(u"dSBoxSmallSPLGainBand_6")
        self.dSBoxSmallSPLGainBand_6.setDecimals(1)
        self.dSBoxSmallSPLGainBand_6.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_6.setMaximum(65535.500000000000000)

        self.horizontalLayout_97.addWidget(self.dSBoxSmallSPLGainBand_6)


        self.gridLayout_17.addLayout(self.horizontalLayout_97, 0, 1, 1, 1)

        self.horizontalLayout_98 = QHBoxLayout()
        self.horizontalLayout_98.setObjectName(u"horizontalLayout_98")
        self.label_86 = QLabel(self.groupBox_14)
        self.label_86.setObjectName(u"label_86")
        sizePolicy1.setHeightForWidth(self.label_86.sizePolicy().hasHeightForWidth())
        self.label_86.setSizePolicy(sizePolicy1)
        self.label_86.setMinimumSize(QSize(30, 0))
        self.label_86.setMaximumSize(QSize(16777215, 16777215))
        self.label_86.setFont(font1)

        self.horizontalLayout_98.addWidget(self.label_86)

        self.dSBoxMediumSPLBand_6 = QDoubleSpinBox(self.groupBox_14)
        self.dSBoxMediumSPLBand_6.setObjectName(u"dSBoxMediumSPLBand_6")
        self.dSBoxMediumSPLBand_6.setDecimals(1)
        self.dSBoxMediumSPLBand_6.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_6.setMaximum(65535.500000000000000)

        self.horizontalLayout_98.addWidget(self.dSBoxMediumSPLBand_6)


        self.gridLayout_17.addLayout(self.horizontalLayout_98, 1, 0, 1, 1)

        self.horizontalLayout_99 = QHBoxLayout()
        self.horizontalLayout_99.setObjectName(u"horizontalLayout_99")
        self.label_87 = QLabel(self.groupBox_14)
        self.label_87.setObjectName(u"label_87")
        sizePolicy1.setHeightForWidth(self.label_87.sizePolicy().hasHeightForWidth())
        self.label_87.setSizePolicy(sizePolicy1)
        self.label_87.setMinimumSize(QSize(0, 0))
        self.label_87.setMaximumSize(QSize(16777215, 16777215))
        self.label_87.setFont(font1)

        self.horizontalLayout_99.addWidget(self.label_87)

        self.dSBoxMediumSPLGainBand_6 = QDoubleSpinBox(self.groupBox_14)
        self.dSBoxMediumSPLGainBand_6.setObjectName(u"dSBoxMediumSPLGainBand_6")
        self.dSBoxMediumSPLGainBand_6.setDecimals(1)
        self.dSBoxMediumSPLGainBand_6.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_6.setMaximum(65535.500000000000000)

        self.horizontalLayout_99.addWidget(self.dSBoxMediumSPLGainBand_6)


        self.gridLayout_17.addLayout(self.horizontalLayout_99, 1, 1, 1, 1)

        self.horizontalLayout_100 = QHBoxLayout()
        self.horizontalLayout_100.setObjectName(u"horizontalLayout_100")
        self.label_88 = QLabel(self.groupBox_14)
        self.label_88.setObjectName(u"label_88")
        sizePolicy1.setHeightForWidth(self.label_88.sizePolicy().hasHeightForWidth())
        self.label_88.setSizePolicy(sizePolicy1)
        self.label_88.setMinimumSize(QSize(30, 0))
        self.label_88.setMaximumSize(QSize(16777215, 16777215))
        self.label_88.setFont(font1)

        self.horizontalLayout_100.addWidget(self.label_88)

        self.dSBoxLargeSPLBand_6 = QDoubleSpinBox(self.groupBox_14)
        self.dSBoxLargeSPLBand_6.setObjectName(u"dSBoxLargeSPLBand_6")
        self.dSBoxLargeSPLBand_6.setDecimals(1)
        self.dSBoxLargeSPLBand_6.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_6.setMaximum(65535.500000000000000)

        self.horizontalLayout_100.addWidget(self.dSBoxLargeSPLBand_6)


        self.gridLayout_17.addLayout(self.horizontalLayout_100, 2, 0, 1, 1)

        self.horizontalLayout_101 = QHBoxLayout()
        self.horizontalLayout_101.setObjectName(u"horizontalLayout_101")
        self.label_89 = QLabel(self.groupBox_14)
        self.label_89.setObjectName(u"label_89")
        sizePolicy1.setHeightForWidth(self.label_89.sizePolicy().hasHeightForWidth())
        self.label_89.setSizePolicy(sizePolicy1)
        self.label_89.setMinimumSize(QSize(0, 0))
        self.label_89.setMaximumSize(QSize(16777215, 16777215))
        self.label_89.setFont(font1)

        self.horizontalLayout_101.addWidget(self.label_89)

        self.dSBoxLargeSPLGainBand_6 = QDoubleSpinBox(self.groupBox_14)
        self.dSBoxLargeSPLGainBand_6.setObjectName(u"dSBoxLargeSPLGainBand_6")
        self.dSBoxLargeSPLGainBand_6.setDecimals(1)
        self.dSBoxLargeSPLGainBand_6.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_6.setMaximum(65535.500000000000000)

        self.horizontalLayout_101.addWidget(self.dSBoxLargeSPLGainBand_6)


        self.gridLayout_17.addLayout(self.horizontalLayout_101, 2, 1, 1, 1)


        self.verticalLayout_10.addWidget(self.groupBox_14)


        self.gridLayout_15.addWidget(self.gpBand_6, 4, 0, 1, 1)

        self.gpBand_9 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_9.setObjectName(u"gpBand_9")
        sizePolicy8.setHeightForWidth(self.gpBand_9.sizePolicy().hasHeightForWidth())
        self.gpBand_9.setSizePolicy(sizePolicy8)
        self.gpBand_9.setMinimumSize(QSize(0, 0))
        self.gpBand_9.setFont(font1)
        self.verticalLayout_13 = QVBoxLayout(self.gpBand_9)
        self.verticalLayout_13.setObjectName(u"verticalLayout_13")
        self.horizontalLayout_125 = QHBoxLayout()
        self.horizontalLayout_125.setObjectName(u"horizontalLayout_125")
        self.horizontalLayout_125.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_126 = QHBoxLayout()
        self.horizontalLayout_126.setObjectName(u"horizontalLayout_126")
        self.label_111 = QLabel(self.gpBand_9)
        self.label_111.setObjectName(u"label_111")
        sizePolicy1.setHeightForWidth(self.label_111.sizePolicy().hasHeightForWidth())
        self.label_111.setSizePolicy(sizePolicy1)
        self.label_111.setMinimumSize(QSize(0, 0))
        self.label_111.setMaximumSize(QSize(16777215, 16777215))
        self.label_111.setFont(font1)

        self.horizontalLayout_126.addWidget(self.label_111)

        self.spinBoxAttackBand_9 = QSpinBox(self.gpBand_9)
        self.spinBoxAttackBand_9.setObjectName(u"spinBoxAttackBand_9")
        self.spinBoxAttackBand_9.setMaximum(65535)

        self.horizontalLayout_126.addWidget(self.spinBoxAttackBand_9)


        self.horizontalLayout_125.addLayout(self.horizontalLayout_126)

        self.horizontalLayout_127 = QHBoxLayout()
        self.horizontalLayout_127.setObjectName(u"horizontalLayout_127")
        self.label_112 = QLabel(self.gpBand_9)
        self.label_112.setObjectName(u"label_112")
        sizePolicy1.setHeightForWidth(self.label_112.sizePolicy().hasHeightForWidth())
        self.label_112.setSizePolicy(sizePolicy1)
        self.label_112.setMinimumSize(QSize(0, 0))
        self.label_112.setMaximumSize(QSize(16777215, 16777215))
        self.label_112.setFont(font1)

        self.horizontalLayout_127.addWidget(self.label_112)

        self.spinBoxReleaseBand_9 = QSpinBox(self.gpBand_9)
        self.spinBoxReleaseBand_9.setObjectName(u"spinBoxReleaseBand_9")
        self.spinBoxReleaseBand_9.setMaximum(65535)

        self.horizontalLayout_127.addWidget(self.spinBoxReleaseBand_9)


        self.horizontalLayout_125.addLayout(self.horizontalLayout_127)


        self.verticalLayout_13.addLayout(self.horizontalLayout_125)

        self.gpLimitBand_9 = QGroupBox(self.gpBand_9)
        self.gpLimitBand_9.setObjectName(u"gpLimitBand_9")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_9.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_9.setSizePolicy(sizePolicy6)
        self.gpLimitBand_9.setFont(font1)
        self.gridLayout_22 = QGridLayout(self.gpLimitBand_9)
        self.gridLayout_22.setObjectName(u"gridLayout_22")
        self.gridLayout_22.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_128 = QHBoxLayout()
        self.horizontalLayout_128.setObjectName(u"horizontalLayout_128")
        self.label_113 = QLabel(self.gpLimitBand_9)
        self.label_113.setObjectName(u"label_113")
        sizePolicy1.setHeightForWidth(self.label_113.sizePolicy().hasHeightForWidth())
        self.label_113.setSizePolicy(sizePolicy1)
        self.label_113.setMinimumSize(QSize(0, 0))
        self.label_113.setMaximumSize(QSize(16777215, 16777215))
        self.label_113.setFont(font1)

        self.horizontalLayout_128.addWidget(self.label_113)

        self.dSBoxLimitThresholdBand_9 = QDoubleSpinBox(self.gpLimitBand_9)
        self.dSBoxLimitThresholdBand_9.setObjectName(u"dSBoxLimitThresholdBand_9")
        self.dSBoxLimitThresholdBand_9.setDecimals(1)
        self.dSBoxLimitThresholdBand_9.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_9.setMaximum(65535.500000000000000)

        self.horizontalLayout_128.addWidget(self.dSBoxLimitThresholdBand_9)


        self.gridLayout_22.addLayout(self.horizontalLayout_128, 0, 0, 1, 1)

        self.horizontalLayout_129 = QHBoxLayout()
        self.horizontalLayout_129.setObjectName(u"horizontalLayout_129")
        self.label_114 = QLabel(self.gpLimitBand_9)
        self.label_114.setObjectName(u"label_114")
        sizePolicy1.setHeightForWidth(self.label_114.sizePolicy().hasHeightForWidth())
        self.label_114.setSizePolicy(sizePolicy1)
        self.label_114.setMinimumSize(QSize(0, 0))
        self.label_114.setMaximumSize(QSize(16777215, 16777215))
        self.label_114.setFont(font1)

        self.horizontalLayout_129.addWidget(self.label_114)

        self.dSBoxLimitKneeBand_9 = QDoubleSpinBox(self.gpLimitBand_9)
        self.dSBoxLimitKneeBand_9.setObjectName(u"dSBoxLimitKneeBand_9")
        self.dSBoxLimitKneeBand_9.setDecimals(1)
        self.dSBoxLimitKneeBand_9.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_9.setMaximum(30.000000000000000)

        self.horizontalLayout_129.addWidget(self.dSBoxLimitKneeBand_9)


        self.gridLayout_22.addLayout(self.horizontalLayout_129, 0, 1, 1, 1)


        self.verticalLayout_13.addWidget(self.gpLimitBand_9)

        self.groupBox_17 = QGroupBox(self.gpBand_9)
        self.groupBox_17.setObjectName(u"groupBox_17")
        sizePolicy6.setHeightForWidth(self.groupBox_17.sizePolicy().hasHeightForWidth())
        self.groupBox_17.setSizePolicy(sizePolicy6)
        self.gridLayout_23 = QGridLayout(self.groupBox_17)
        self.gridLayout_23.setObjectName(u"gridLayout_23")
        self.gridLayout_23.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_130 = QHBoxLayout()
        self.horizontalLayout_130.setObjectName(u"horizontalLayout_130")
        self.label_115 = QLabel(self.groupBox_17)
        self.label_115.setObjectName(u"label_115")
        sizePolicy1.setHeightForWidth(self.label_115.sizePolicy().hasHeightForWidth())
        self.label_115.setSizePolicy(sizePolicy1)
        self.label_115.setMinimumSize(QSize(30, 0))
        self.label_115.setMaximumSize(QSize(16777215, 16777215))
        self.label_115.setFont(font1)

        self.horizontalLayout_130.addWidget(self.label_115)

        self.dSBoxSmallSPLBand_9 = QDoubleSpinBox(self.groupBox_17)
        self.dSBoxSmallSPLBand_9.setObjectName(u"dSBoxSmallSPLBand_9")
        self.dSBoxSmallSPLBand_9.setDecimals(1)
        self.dSBoxSmallSPLBand_9.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_9.setMaximum(65535.500000000000000)

        self.horizontalLayout_130.addWidget(self.dSBoxSmallSPLBand_9)


        self.gridLayout_23.addLayout(self.horizontalLayout_130, 0, 0, 1, 1)

        self.horizontalLayout_131 = QHBoxLayout()
        self.horizontalLayout_131.setObjectName(u"horizontalLayout_131")
        self.label_116 = QLabel(self.groupBox_17)
        self.label_116.setObjectName(u"label_116")
        sizePolicy1.setHeightForWidth(self.label_116.sizePolicy().hasHeightForWidth())
        self.label_116.setSizePolicy(sizePolicy1)
        self.label_116.setMinimumSize(QSize(0, 0))
        self.label_116.setMaximumSize(QSize(16777215, 16777215))
        self.label_116.setFont(font1)

        self.horizontalLayout_131.addWidget(self.label_116)

        self.dSBoxSmallSPLGainBand_9 = QDoubleSpinBox(self.groupBox_17)
        self.dSBoxSmallSPLGainBand_9.setObjectName(u"dSBoxSmallSPLGainBand_9")
        self.dSBoxSmallSPLGainBand_9.setDecimals(1)
        self.dSBoxSmallSPLGainBand_9.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_9.setMaximum(65535.500000000000000)

        self.horizontalLayout_131.addWidget(self.dSBoxSmallSPLGainBand_9)


        self.gridLayout_23.addLayout(self.horizontalLayout_131, 0, 1, 1, 1)

        self.horizontalLayout_132 = QHBoxLayout()
        self.horizontalLayout_132.setObjectName(u"horizontalLayout_132")
        self.label_117 = QLabel(self.groupBox_17)
        self.label_117.setObjectName(u"label_117")
        sizePolicy1.setHeightForWidth(self.label_117.sizePolicy().hasHeightForWidth())
        self.label_117.setSizePolicy(sizePolicy1)
        self.label_117.setMinimumSize(QSize(30, 0))
        self.label_117.setMaximumSize(QSize(16777215, 16777215))
        self.label_117.setFont(font1)

        self.horizontalLayout_132.addWidget(self.label_117)

        self.dSBoxMediumSPLBand_9 = QDoubleSpinBox(self.groupBox_17)
        self.dSBoxMediumSPLBand_9.setObjectName(u"dSBoxMediumSPLBand_9")
        self.dSBoxMediumSPLBand_9.setDecimals(1)
        self.dSBoxMediumSPLBand_9.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_9.setMaximum(65535.500000000000000)

        self.horizontalLayout_132.addWidget(self.dSBoxMediumSPLBand_9)


        self.gridLayout_23.addLayout(self.horizontalLayout_132, 1, 0, 1, 1)

        self.horizontalLayout_133 = QHBoxLayout()
        self.horizontalLayout_133.setObjectName(u"horizontalLayout_133")
        self.label_118 = QLabel(self.groupBox_17)
        self.label_118.setObjectName(u"label_118")
        sizePolicy1.setHeightForWidth(self.label_118.sizePolicy().hasHeightForWidth())
        self.label_118.setSizePolicy(sizePolicy1)
        self.label_118.setMinimumSize(QSize(0, 0))
        self.label_118.setMaximumSize(QSize(16777215, 16777215))
        self.label_118.setFont(font1)

        self.horizontalLayout_133.addWidget(self.label_118)

        self.dSBoxMediumSPLGainBand_9 = QDoubleSpinBox(self.groupBox_17)
        self.dSBoxMediumSPLGainBand_9.setObjectName(u"dSBoxMediumSPLGainBand_9")
        self.dSBoxMediumSPLGainBand_9.setDecimals(1)
        self.dSBoxMediumSPLGainBand_9.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_9.setMaximum(65535.500000000000000)

        self.horizontalLayout_133.addWidget(self.dSBoxMediumSPLGainBand_9)


        self.gridLayout_23.addLayout(self.horizontalLayout_133, 1, 1, 1, 1)

        self.horizontalLayout_134 = QHBoxLayout()
        self.horizontalLayout_134.setObjectName(u"horizontalLayout_134")
        self.label_119 = QLabel(self.groupBox_17)
        self.label_119.setObjectName(u"label_119")
        sizePolicy1.setHeightForWidth(self.label_119.sizePolicy().hasHeightForWidth())
        self.label_119.setSizePolicy(sizePolicy1)
        self.label_119.setMinimumSize(QSize(30, 0))
        self.label_119.setMaximumSize(QSize(16777215, 16777215))
        self.label_119.setFont(font1)

        self.horizontalLayout_134.addWidget(self.label_119)

        self.dSBoxLargeSPLBand_9 = QDoubleSpinBox(self.groupBox_17)
        self.dSBoxLargeSPLBand_9.setObjectName(u"dSBoxLargeSPLBand_9")
        self.dSBoxLargeSPLBand_9.setDecimals(1)
        self.dSBoxLargeSPLBand_9.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_9.setMaximum(65535.500000000000000)

        self.horizontalLayout_134.addWidget(self.dSBoxLargeSPLBand_9)


        self.gridLayout_23.addLayout(self.horizontalLayout_134, 2, 0, 1, 1)

        self.horizontalLayout_135 = QHBoxLayout()
        self.horizontalLayout_135.setObjectName(u"horizontalLayout_135")
        self.label_120 = QLabel(self.groupBox_17)
        self.label_120.setObjectName(u"label_120")
        sizePolicy1.setHeightForWidth(self.label_120.sizePolicy().hasHeightForWidth())
        self.label_120.setSizePolicy(sizePolicy1)
        self.label_120.setMinimumSize(QSize(0, 0))
        self.label_120.setMaximumSize(QSize(16777215, 16777215))
        self.label_120.setFont(font1)

        self.horizontalLayout_135.addWidget(self.label_120)

        self.dSBoxLargeSPLGainBand_9 = QDoubleSpinBox(self.groupBox_17)
        self.dSBoxLargeSPLGainBand_9.setObjectName(u"dSBoxLargeSPLGainBand_9")
        self.dSBoxLargeSPLGainBand_9.setDecimals(1)
        self.dSBoxLargeSPLGainBand_9.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_9.setMaximum(65535.500000000000000)

        self.horizontalLayout_135.addWidget(self.dSBoxLargeSPLGainBand_9)


        self.gridLayout_23.addLayout(self.horizontalLayout_135, 2, 1, 1, 1)


        self.verticalLayout_13.addWidget(self.groupBox_17)


        self.gridLayout_15.addWidget(self.gpBand_9, 5, 1, 1, 1)

        self.gpBand_17 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_17.setObjectName(u"gpBand_17")
        sizePolicy8.setHeightForWidth(self.gpBand_17.sizePolicy().hasHeightForWidth())
        self.gpBand_17.setSizePolicy(sizePolicy8)
        self.gpBand_17.setMinimumSize(QSize(0, 0))
        self.gpBand_17.setFont(font1)
        self.verticalLayout_21 = QVBoxLayout(self.gpBand_17)
        self.verticalLayout_21.setObjectName(u"verticalLayout_21")
        self.horizontalLayout_213 = QHBoxLayout()
        self.horizontalLayout_213.setObjectName(u"horizontalLayout_213")
        self.horizontalLayout_213.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_214 = QHBoxLayout()
        self.horizontalLayout_214.setObjectName(u"horizontalLayout_214")
        self.label_191 = QLabel(self.gpBand_17)
        self.label_191.setObjectName(u"label_191")
        sizePolicy1.setHeightForWidth(self.label_191.sizePolicy().hasHeightForWidth())
        self.label_191.setSizePolicy(sizePolicy1)
        self.label_191.setMinimumSize(QSize(0, 0))
        self.label_191.setMaximumSize(QSize(16777215, 16777215))
        self.label_191.setFont(font1)

        self.horizontalLayout_214.addWidget(self.label_191)

        self.spinBoxAttackBand_17 = QSpinBox(self.gpBand_17)
        self.spinBoxAttackBand_17.setObjectName(u"spinBoxAttackBand_17")
        self.spinBoxAttackBand_17.setMaximum(65535)

        self.horizontalLayout_214.addWidget(self.spinBoxAttackBand_17)


        self.horizontalLayout_213.addLayout(self.horizontalLayout_214)

        self.horizontalLayout_215 = QHBoxLayout()
        self.horizontalLayout_215.setObjectName(u"horizontalLayout_215")
        self.label_192 = QLabel(self.gpBand_17)
        self.label_192.setObjectName(u"label_192")
        sizePolicy1.setHeightForWidth(self.label_192.sizePolicy().hasHeightForWidth())
        self.label_192.setSizePolicy(sizePolicy1)
        self.label_192.setMinimumSize(QSize(0, 0))
        self.label_192.setMaximumSize(QSize(16777215, 16777215))
        self.label_192.setFont(font1)

        self.horizontalLayout_215.addWidget(self.label_192)

        self.spinBoxReleaseBand_17 = QSpinBox(self.gpBand_17)
        self.spinBoxReleaseBand_17.setObjectName(u"spinBoxReleaseBand_17")
        self.spinBoxReleaseBand_17.setMaximum(65535)

        self.horizontalLayout_215.addWidget(self.spinBoxReleaseBand_17)


        self.horizontalLayout_213.addLayout(self.horizontalLayout_215)


        self.verticalLayout_21.addLayout(self.horizontalLayout_213)

        self.gpLimitBand_17 = QGroupBox(self.gpBand_17)
        self.gpLimitBand_17.setObjectName(u"gpLimitBand_17")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_17.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_17.setSizePolicy(sizePolicy6)
        self.gpLimitBand_17.setFont(font1)
        self.gridLayout_38 = QGridLayout(self.gpLimitBand_17)
        self.gridLayout_38.setObjectName(u"gridLayout_38")
        self.gridLayout_38.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_216 = QHBoxLayout()
        self.horizontalLayout_216.setObjectName(u"horizontalLayout_216")
        self.label_193 = QLabel(self.gpLimitBand_17)
        self.label_193.setObjectName(u"label_193")
        sizePolicy1.setHeightForWidth(self.label_193.sizePolicy().hasHeightForWidth())
        self.label_193.setSizePolicy(sizePolicy1)
        self.label_193.setMinimumSize(QSize(0, 0))
        self.label_193.setMaximumSize(QSize(16777215, 16777215))
        self.label_193.setFont(font1)

        self.horizontalLayout_216.addWidget(self.label_193)

        self.dSBoxLimitThresholdBand_17 = QDoubleSpinBox(self.gpLimitBand_17)
        self.dSBoxLimitThresholdBand_17.setObjectName(u"dSBoxLimitThresholdBand_17")
        self.dSBoxLimitThresholdBand_17.setDecimals(1)
        self.dSBoxLimitThresholdBand_17.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_17.setMaximum(65535.500000000000000)

        self.horizontalLayout_216.addWidget(self.dSBoxLimitThresholdBand_17)


        self.gridLayout_38.addLayout(self.horizontalLayout_216, 0, 0, 1, 1)

        self.horizontalLayout_217 = QHBoxLayout()
        self.horizontalLayout_217.setObjectName(u"horizontalLayout_217")
        self.label_194 = QLabel(self.gpLimitBand_17)
        self.label_194.setObjectName(u"label_194")
        sizePolicy1.setHeightForWidth(self.label_194.sizePolicy().hasHeightForWidth())
        self.label_194.setSizePolicy(sizePolicy1)
        self.label_194.setMinimumSize(QSize(0, 0))
        self.label_194.setMaximumSize(QSize(16777215, 16777215))
        self.label_194.setFont(font1)

        self.horizontalLayout_217.addWidget(self.label_194)

        self.dSBoxLimitKneeBand_17 = QDoubleSpinBox(self.gpLimitBand_17)
        self.dSBoxLimitKneeBand_17.setObjectName(u"dSBoxLimitKneeBand_17")
        self.dSBoxLimitKneeBand_17.setDecimals(1)
        self.dSBoxLimitKneeBand_17.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_17.setMaximum(30.000000000000000)

        self.horizontalLayout_217.addWidget(self.dSBoxLimitKneeBand_17)


        self.gridLayout_38.addLayout(self.horizontalLayout_217, 0, 1, 1, 1)


        self.verticalLayout_21.addWidget(self.gpLimitBand_17)

        self.groupBox_25 = QGroupBox(self.gpBand_17)
        self.groupBox_25.setObjectName(u"groupBox_25")
        sizePolicy6.setHeightForWidth(self.groupBox_25.sizePolicy().hasHeightForWidth())
        self.groupBox_25.setSizePolicy(sizePolicy6)
        self.gridLayout_39 = QGridLayout(self.groupBox_25)
        self.gridLayout_39.setObjectName(u"gridLayout_39")
        self.gridLayout_39.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_218 = QHBoxLayout()
        self.horizontalLayout_218.setObjectName(u"horizontalLayout_218")
        self.label_195 = QLabel(self.groupBox_25)
        self.label_195.setObjectName(u"label_195")
        sizePolicy1.setHeightForWidth(self.label_195.sizePolicy().hasHeightForWidth())
        self.label_195.setSizePolicy(sizePolicy1)
        self.label_195.setMinimumSize(QSize(30, 0))
        self.label_195.setMaximumSize(QSize(16777215, 16777215))
        self.label_195.setFont(font1)

        self.horizontalLayout_218.addWidget(self.label_195)

        self.dSBoxSmallSPLBand_17 = QDoubleSpinBox(self.groupBox_25)
        self.dSBoxSmallSPLBand_17.setObjectName(u"dSBoxSmallSPLBand_17")
        self.dSBoxSmallSPLBand_17.setDecimals(1)
        self.dSBoxSmallSPLBand_17.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_17.setMaximum(65535.500000000000000)

        self.horizontalLayout_218.addWidget(self.dSBoxSmallSPLBand_17)


        self.gridLayout_39.addLayout(self.horizontalLayout_218, 0, 0, 1, 1)

        self.horizontalLayout_219 = QHBoxLayout()
        self.horizontalLayout_219.setObjectName(u"horizontalLayout_219")
        self.label_196 = QLabel(self.groupBox_25)
        self.label_196.setObjectName(u"label_196")
        sizePolicy1.setHeightForWidth(self.label_196.sizePolicy().hasHeightForWidth())
        self.label_196.setSizePolicy(sizePolicy1)
        self.label_196.setMinimumSize(QSize(0, 0))
        self.label_196.setMaximumSize(QSize(16777215, 16777215))
        self.label_196.setFont(font1)

        self.horizontalLayout_219.addWidget(self.label_196)

        self.dSBoxSmallSPLGainBand_17 = QDoubleSpinBox(self.groupBox_25)
        self.dSBoxSmallSPLGainBand_17.setObjectName(u"dSBoxSmallSPLGainBand_17")
        self.dSBoxSmallSPLGainBand_17.setDecimals(1)
        self.dSBoxSmallSPLGainBand_17.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_17.setMaximum(65535.500000000000000)

        self.horizontalLayout_219.addWidget(self.dSBoxSmallSPLGainBand_17)


        self.gridLayout_39.addLayout(self.horizontalLayout_219, 0, 1, 1, 1)

        self.horizontalLayout_220 = QHBoxLayout()
        self.horizontalLayout_220.setObjectName(u"horizontalLayout_220")
        self.label_197 = QLabel(self.groupBox_25)
        self.label_197.setObjectName(u"label_197")
        sizePolicy1.setHeightForWidth(self.label_197.sizePolicy().hasHeightForWidth())
        self.label_197.setSizePolicy(sizePolicy1)
        self.label_197.setMinimumSize(QSize(30, 0))
        self.label_197.setMaximumSize(QSize(16777215, 16777215))
        self.label_197.setFont(font1)

        self.horizontalLayout_220.addWidget(self.label_197)

        self.dSBoxMediumSPLBand_17 = QDoubleSpinBox(self.groupBox_25)
        self.dSBoxMediumSPLBand_17.setObjectName(u"dSBoxMediumSPLBand_17")
        self.dSBoxMediumSPLBand_17.setDecimals(1)
        self.dSBoxMediumSPLBand_17.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_17.setMaximum(65535.500000000000000)

        self.horizontalLayout_220.addWidget(self.dSBoxMediumSPLBand_17)


        self.gridLayout_39.addLayout(self.horizontalLayout_220, 1, 0, 1, 1)

        self.horizontalLayout_221 = QHBoxLayout()
        self.horizontalLayout_221.setObjectName(u"horizontalLayout_221")
        self.label_198 = QLabel(self.groupBox_25)
        self.label_198.setObjectName(u"label_198")
        sizePolicy1.setHeightForWidth(self.label_198.sizePolicy().hasHeightForWidth())
        self.label_198.setSizePolicy(sizePolicy1)
        self.label_198.setMinimumSize(QSize(0, 0))
        self.label_198.setMaximumSize(QSize(16777215, 16777215))
        self.label_198.setFont(font1)

        self.horizontalLayout_221.addWidget(self.label_198)

        self.dSBoxMediumSPLGainBand_17 = QDoubleSpinBox(self.groupBox_25)
        self.dSBoxMediumSPLGainBand_17.setObjectName(u"dSBoxMediumSPLGainBand_17")
        self.dSBoxMediumSPLGainBand_17.setDecimals(1)
        self.dSBoxMediumSPLGainBand_17.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_17.setMaximum(65535.500000000000000)

        self.horizontalLayout_221.addWidget(self.dSBoxMediumSPLGainBand_17)


        self.gridLayout_39.addLayout(self.horizontalLayout_221, 1, 1, 1, 1)

        self.horizontalLayout_222 = QHBoxLayout()
        self.horizontalLayout_222.setObjectName(u"horizontalLayout_222")
        self.label_199 = QLabel(self.groupBox_25)
        self.label_199.setObjectName(u"label_199")
        sizePolicy1.setHeightForWidth(self.label_199.sizePolicy().hasHeightForWidth())
        self.label_199.setSizePolicy(sizePolicy1)
        self.label_199.setMinimumSize(QSize(30, 0))
        self.label_199.setMaximumSize(QSize(16777215, 16777215))
        self.label_199.setFont(font1)

        self.horizontalLayout_222.addWidget(self.label_199)

        self.dSBoxLargeSPLBand_17 = QDoubleSpinBox(self.groupBox_25)
        self.dSBoxLargeSPLBand_17.setObjectName(u"dSBoxLargeSPLBand_17")
        self.dSBoxLargeSPLBand_17.setDecimals(1)
        self.dSBoxLargeSPLBand_17.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_17.setMaximum(65535.500000000000000)

        self.horizontalLayout_222.addWidget(self.dSBoxLargeSPLBand_17)


        self.gridLayout_39.addLayout(self.horizontalLayout_222, 2, 0, 1, 1)

        self.horizontalLayout_223 = QHBoxLayout()
        self.horizontalLayout_223.setObjectName(u"horizontalLayout_223")
        self.label_200 = QLabel(self.groupBox_25)
        self.label_200.setObjectName(u"label_200")
        sizePolicy1.setHeightForWidth(self.label_200.sizePolicy().hasHeightForWidth())
        self.label_200.setSizePolicy(sizePolicy1)
        self.label_200.setMinimumSize(QSize(0, 0))
        self.label_200.setMaximumSize(QSize(16777215, 16777215))
        self.label_200.setFont(font1)

        self.horizontalLayout_223.addWidget(self.label_200)

        self.dSBoxLargeSPLGainBand_17 = QDoubleSpinBox(self.groupBox_25)
        self.dSBoxLargeSPLGainBand_17.setObjectName(u"dSBoxLargeSPLGainBand_17")
        self.dSBoxLargeSPLGainBand_17.setDecimals(1)
        self.dSBoxLargeSPLGainBand_17.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_17.setMaximum(65535.500000000000000)

        self.horizontalLayout_223.addWidget(self.dSBoxLargeSPLGainBand_17)


        self.gridLayout_39.addLayout(self.horizontalLayout_223, 2, 1, 1, 1)


        self.verticalLayout_21.addWidget(self.groupBox_25)


        self.gridLayout_15.addWidget(self.gpBand_17, 9, 1, 1, 1)

        self.gpBand_15 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_15.setObjectName(u"gpBand_15")
        sizePolicy8.setHeightForWidth(self.gpBand_15.sizePolicy().hasHeightForWidth())
        self.gpBand_15.setSizePolicy(sizePolicy8)
        self.gpBand_15.setMinimumSize(QSize(0, 0))
        self.gpBand_15.setFont(font1)
        self.verticalLayout_19 = QVBoxLayout(self.gpBand_15)
        self.verticalLayout_19.setObjectName(u"verticalLayout_19")
        self.horizontalLayout_191 = QHBoxLayout()
        self.horizontalLayout_191.setObjectName(u"horizontalLayout_191")
        self.horizontalLayout_191.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_192 = QHBoxLayout()
        self.horizontalLayout_192.setObjectName(u"horizontalLayout_192")
        self.label_171 = QLabel(self.gpBand_15)
        self.label_171.setObjectName(u"label_171")
        sizePolicy1.setHeightForWidth(self.label_171.sizePolicy().hasHeightForWidth())
        self.label_171.setSizePolicy(sizePolicy1)
        self.label_171.setMinimumSize(QSize(0, 0))
        self.label_171.setMaximumSize(QSize(16777215, 16777215))
        self.label_171.setFont(font1)

        self.horizontalLayout_192.addWidget(self.label_171)

        self.spinBoxAttackBand_15 = QSpinBox(self.gpBand_15)
        self.spinBoxAttackBand_15.setObjectName(u"spinBoxAttackBand_15")
        self.spinBoxAttackBand_15.setMaximum(65535)

        self.horizontalLayout_192.addWidget(self.spinBoxAttackBand_15)


        self.horizontalLayout_191.addLayout(self.horizontalLayout_192)

        self.horizontalLayout_193 = QHBoxLayout()
        self.horizontalLayout_193.setObjectName(u"horizontalLayout_193")
        self.label_172 = QLabel(self.gpBand_15)
        self.label_172.setObjectName(u"label_172")
        sizePolicy1.setHeightForWidth(self.label_172.sizePolicy().hasHeightForWidth())
        self.label_172.setSizePolicy(sizePolicy1)
        self.label_172.setMinimumSize(QSize(0, 0))
        self.label_172.setMaximumSize(QSize(16777215, 16777215))
        self.label_172.setFont(font1)

        self.horizontalLayout_193.addWidget(self.label_172)

        self.spinBoxReleaseBand_15 = QSpinBox(self.gpBand_15)
        self.spinBoxReleaseBand_15.setObjectName(u"spinBoxReleaseBand_15")
        self.spinBoxReleaseBand_15.setMaximum(65535)

        self.horizontalLayout_193.addWidget(self.spinBoxReleaseBand_15)


        self.horizontalLayout_191.addLayout(self.horizontalLayout_193)


        self.verticalLayout_19.addLayout(self.horizontalLayout_191)

        self.gpLimitBand_15 = QGroupBox(self.gpBand_15)
        self.gpLimitBand_15.setObjectName(u"gpLimitBand_15")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_15.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_15.setSizePolicy(sizePolicy6)
        self.gpLimitBand_15.setFont(font1)
        self.gridLayout_34 = QGridLayout(self.gpLimitBand_15)
        self.gridLayout_34.setObjectName(u"gridLayout_34")
        self.gridLayout_34.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_194 = QHBoxLayout()
        self.horizontalLayout_194.setObjectName(u"horizontalLayout_194")
        self.label_173 = QLabel(self.gpLimitBand_15)
        self.label_173.setObjectName(u"label_173")
        sizePolicy1.setHeightForWidth(self.label_173.sizePolicy().hasHeightForWidth())
        self.label_173.setSizePolicy(sizePolicy1)
        self.label_173.setMinimumSize(QSize(0, 0))
        self.label_173.setMaximumSize(QSize(16777215, 16777215))
        self.label_173.setFont(font1)

        self.horizontalLayout_194.addWidget(self.label_173)

        self.dSBoxLimitThresholdBand_15 = QDoubleSpinBox(self.gpLimitBand_15)
        self.dSBoxLimitThresholdBand_15.setObjectName(u"dSBoxLimitThresholdBand_15")
        self.dSBoxLimitThresholdBand_15.setDecimals(1)
        self.dSBoxLimitThresholdBand_15.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_15.setMaximum(65535.500000000000000)

        self.horizontalLayout_194.addWidget(self.dSBoxLimitThresholdBand_15)


        self.gridLayout_34.addLayout(self.horizontalLayout_194, 0, 0, 1, 1)

        self.horizontalLayout_195 = QHBoxLayout()
        self.horizontalLayout_195.setObjectName(u"horizontalLayout_195")
        self.label_174 = QLabel(self.gpLimitBand_15)
        self.label_174.setObjectName(u"label_174")
        sizePolicy1.setHeightForWidth(self.label_174.sizePolicy().hasHeightForWidth())
        self.label_174.setSizePolicy(sizePolicy1)
        self.label_174.setMinimumSize(QSize(0, 0))
        self.label_174.setMaximumSize(QSize(16777215, 16777215))
        self.label_174.setFont(font1)

        self.horizontalLayout_195.addWidget(self.label_174)

        self.dSBoxLimitKneeBand_15 = QDoubleSpinBox(self.gpLimitBand_15)
        self.dSBoxLimitKneeBand_15.setObjectName(u"dSBoxLimitKneeBand_15")
        self.dSBoxLimitKneeBand_15.setDecimals(1)
        self.dSBoxLimitKneeBand_15.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_15.setMaximum(30.000000000000000)

        self.horizontalLayout_195.addWidget(self.dSBoxLimitKneeBand_15)


        self.gridLayout_34.addLayout(self.horizontalLayout_195, 0, 1, 1, 1)


        self.verticalLayout_19.addWidget(self.gpLimitBand_15)

        self.groupBox_23 = QGroupBox(self.gpBand_15)
        self.groupBox_23.setObjectName(u"groupBox_23")
        sizePolicy6.setHeightForWidth(self.groupBox_23.sizePolicy().hasHeightForWidth())
        self.groupBox_23.setSizePolicy(sizePolicy6)
        self.gridLayout_35 = QGridLayout(self.groupBox_23)
        self.gridLayout_35.setObjectName(u"gridLayout_35")
        self.gridLayout_35.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_196 = QHBoxLayout()
        self.horizontalLayout_196.setObjectName(u"horizontalLayout_196")
        self.label_175 = QLabel(self.groupBox_23)
        self.label_175.setObjectName(u"label_175")
        sizePolicy1.setHeightForWidth(self.label_175.sizePolicy().hasHeightForWidth())
        self.label_175.setSizePolicy(sizePolicy1)
        self.label_175.setMinimumSize(QSize(30, 0))
        self.label_175.setMaximumSize(QSize(16777215, 16777215))
        self.label_175.setFont(font1)

        self.horizontalLayout_196.addWidget(self.label_175)

        self.dSBoxSmallSPLBand_15 = QDoubleSpinBox(self.groupBox_23)
        self.dSBoxSmallSPLBand_15.setObjectName(u"dSBoxSmallSPLBand_15")
        self.dSBoxSmallSPLBand_15.setDecimals(1)
        self.dSBoxSmallSPLBand_15.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_15.setMaximum(65535.500000000000000)

        self.horizontalLayout_196.addWidget(self.dSBoxSmallSPLBand_15)


        self.gridLayout_35.addLayout(self.horizontalLayout_196, 0, 0, 1, 1)

        self.horizontalLayout_197 = QHBoxLayout()
        self.horizontalLayout_197.setObjectName(u"horizontalLayout_197")
        self.label_176 = QLabel(self.groupBox_23)
        self.label_176.setObjectName(u"label_176")
        sizePolicy1.setHeightForWidth(self.label_176.sizePolicy().hasHeightForWidth())
        self.label_176.setSizePolicy(sizePolicy1)
        self.label_176.setMinimumSize(QSize(0, 0))
        self.label_176.setMaximumSize(QSize(16777215, 16777215))
        self.label_176.setFont(font1)

        self.horizontalLayout_197.addWidget(self.label_176)

        self.dSBoxSmallSPLGainBand_15 = QDoubleSpinBox(self.groupBox_23)
        self.dSBoxSmallSPLGainBand_15.setObjectName(u"dSBoxSmallSPLGainBand_15")
        self.dSBoxSmallSPLGainBand_15.setDecimals(1)
        self.dSBoxSmallSPLGainBand_15.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_15.setMaximum(65535.500000000000000)

        self.horizontalLayout_197.addWidget(self.dSBoxSmallSPLGainBand_15)


        self.gridLayout_35.addLayout(self.horizontalLayout_197, 0, 1, 1, 1)

        self.horizontalLayout_198 = QHBoxLayout()
        self.horizontalLayout_198.setObjectName(u"horizontalLayout_198")
        self.label_177 = QLabel(self.groupBox_23)
        self.label_177.setObjectName(u"label_177")
        sizePolicy1.setHeightForWidth(self.label_177.sizePolicy().hasHeightForWidth())
        self.label_177.setSizePolicy(sizePolicy1)
        self.label_177.setMinimumSize(QSize(30, 0))
        self.label_177.setMaximumSize(QSize(16777215, 16777215))
        self.label_177.setFont(font1)

        self.horizontalLayout_198.addWidget(self.label_177)

        self.dSBoxMediumSPLBand_15 = QDoubleSpinBox(self.groupBox_23)
        self.dSBoxMediumSPLBand_15.setObjectName(u"dSBoxMediumSPLBand_15")
        self.dSBoxMediumSPLBand_15.setDecimals(1)
        self.dSBoxMediumSPLBand_15.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_15.setMaximum(65535.500000000000000)

        self.horizontalLayout_198.addWidget(self.dSBoxMediumSPLBand_15)


        self.gridLayout_35.addLayout(self.horizontalLayout_198, 1, 0, 1, 1)

        self.horizontalLayout_199 = QHBoxLayout()
        self.horizontalLayout_199.setObjectName(u"horizontalLayout_199")
        self.label_178 = QLabel(self.groupBox_23)
        self.label_178.setObjectName(u"label_178")
        sizePolicy1.setHeightForWidth(self.label_178.sizePolicy().hasHeightForWidth())
        self.label_178.setSizePolicy(sizePolicy1)
        self.label_178.setMinimumSize(QSize(0, 0))
        self.label_178.setMaximumSize(QSize(16777215, 16777215))
        self.label_178.setFont(font1)

        self.horizontalLayout_199.addWidget(self.label_178)

        self.dSBoxMediumSPLGainBand_15 = QDoubleSpinBox(self.groupBox_23)
        self.dSBoxMediumSPLGainBand_15.setObjectName(u"dSBoxMediumSPLGainBand_15")
        self.dSBoxMediumSPLGainBand_15.setDecimals(1)
        self.dSBoxMediumSPLGainBand_15.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_15.setMaximum(65535.500000000000000)

        self.horizontalLayout_199.addWidget(self.dSBoxMediumSPLGainBand_15)


        self.gridLayout_35.addLayout(self.horizontalLayout_199, 1, 1, 1, 1)

        self.horizontalLayout_200 = QHBoxLayout()
        self.horizontalLayout_200.setObjectName(u"horizontalLayout_200")
        self.label_179 = QLabel(self.groupBox_23)
        self.label_179.setObjectName(u"label_179")
        sizePolicy1.setHeightForWidth(self.label_179.sizePolicy().hasHeightForWidth())
        self.label_179.setSizePolicy(sizePolicy1)
        self.label_179.setMinimumSize(QSize(30, 0))
        self.label_179.setMaximumSize(QSize(16777215, 16777215))
        self.label_179.setFont(font1)

        self.horizontalLayout_200.addWidget(self.label_179)

        self.dSBoxLargeSPLBand_15 = QDoubleSpinBox(self.groupBox_23)
        self.dSBoxLargeSPLBand_15.setObjectName(u"dSBoxLargeSPLBand_15")
        self.dSBoxLargeSPLBand_15.setDecimals(1)
        self.dSBoxLargeSPLBand_15.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_15.setMaximum(65535.500000000000000)

        self.horizontalLayout_200.addWidget(self.dSBoxLargeSPLBand_15)


        self.gridLayout_35.addLayout(self.horizontalLayout_200, 2, 0, 1, 1)

        self.horizontalLayout_201 = QHBoxLayout()
        self.horizontalLayout_201.setObjectName(u"horizontalLayout_201")
        self.label_180 = QLabel(self.groupBox_23)
        self.label_180.setObjectName(u"label_180")
        sizePolicy1.setHeightForWidth(self.label_180.sizePolicy().hasHeightForWidth())
        self.label_180.setSizePolicy(sizePolicy1)
        self.label_180.setMinimumSize(QSize(0, 0))
        self.label_180.setMaximumSize(QSize(16777215, 16777215))
        self.label_180.setFont(font1)

        self.horizontalLayout_201.addWidget(self.label_180)

        self.dSBoxLargeSPLGainBand_15 = QDoubleSpinBox(self.groupBox_23)
        self.dSBoxLargeSPLGainBand_15.setObjectName(u"dSBoxLargeSPLGainBand_15")
        self.dSBoxLargeSPLGainBand_15.setDecimals(1)
        self.dSBoxLargeSPLGainBand_15.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_15.setMaximum(65535.500000000000000)

        self.horizontalLayout_201.addWidget(self.dSBoxLargeSPLGainBand_15)


        self.gridLayout_35.addLayout(self.horizontalLayout_201, 2, 1, 1, 1)


        self.verticalLayout_19.addWidget(self.groupBox_23)


        self.gridLayout_15.addWidget(self.gpBand_15, 8, 1, 1, 1)

        self.gpBand_14 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_14.setObjectName(u"gpBand_14")
        sizePolicy8.setHeightForWidth(self.gpBand_14.sizePolicy().hasHeightForWidth())
        self.gpBand_14.setSizePolicy(sizePolicy8)
        self.gpBand_14.setMinimumSize(QSize(0, 0))
        self.gpBand_14.setFont(font1)
        self.verticalLayout_18 = QVBoxLayout(self.gpBand_14)
        self.verticalLayout_18.setObjectName(u"verticalLayout_18")
        self.horizontalLayout_180 = QHBoxLayout()
        self.horizontalLayout_180.setObjectName(u"horizontalLayout_180")
        self.horizontalLayout_180.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_181 = QHBoxLayout()
        self.horizontalLayout_181.setObjectName(u"horizontalLayout_181")
        self.label_161 = QLabel(self.gpBand_14)
        self.label_161.setObjectName(u"label_161")
        sizePolicy1.setHeightForWidth(self.label_161.sizePolicy().hasHeightForWidth())
        self.label_161.setSizePolicy(sizePolicy1)
        self.label_161.setMinimumSize(QSize(0, 0))
        self.label_161.setMaximumSize(QSize(16777215, 16777215))
        self.label_161.setFont(font1)

        self.horizontalLayout_181.addWidget(self.label_161)

        self.spinBoxAttackBand_14 = QSpinBox(self.gpBand_14)
        self.spinBoxAttackBand_14.setObjectName(u"spinBoxAttackBand_14")
        self.spinBoxAttackBand_14.setMaximum(65535)

        self.horizontalLayout_181.addWidget(self.spinBoxAttackBand_14)


        self.horizontalLayout_180.addLayout(self.horizontalLayout_181)

        self.horizontalLayout_182 = QHBoxLayout()
        self.horizontalLayout_182.setObjectName(u"horizontalLayout_182")
        self.label_162 = QLabel(self.gpBand_14)
        self.label_162.setObjectName(u"label_162")
        sizePolicy1.setHeightForWidth(self.label_162.sizePolicy().hasHeightForWidth())
        self.label_162.setSizePolicy(sizePolicy1)
        self.label_162.setMinimumSize(QSize(0, 0))
        self.label_162.setMaximumSize(QSize(16777215, 16777215))
        self.label_162.setFont(font1)

        self.horizontalLayout_182.addWidget(self.label_162)

        self.spinBoxReleaseBand_14 = QSpinBox(self.gpBand_14)
        self.spinBoxReleaseBand_14.setObjectName(u"spinBoxReleaseBand_14")
        self.spinBoxReleaseBand_14.setMaximum(65535)

        self.horizontalLayout_182.addWidget(self.spinBoxReleaseBand_14)


        self.horizontalLayout_180.addLayout(self.horizontalLayout_182)


        self.verticalLayout_18.addLayout(self.horizontalLayout_180)

        self.gpLimitBand_14 = QGroupBox(self.gpBand_14)
        self.gpLimitBand_14.setObjectName(u"gpLimitBand_14")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_14.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_14.setSizePolicy(sizePolicy6)
        self.gpLimitBand_14.setFont(font1)
        self.gridLayout_32 = QGridLayout(self.gpLimitBand_14)
        self.gridLayout_32.setObjectName(u"gridLayout_32")
        self.gridLayout_32.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_183 = QHBoxLayout()
        self.horizontalLayout_183.setObjectName(u"horizontalLayout_183")
        self.label_163 = QLabel(self.gpLimitBand_14)
        self.label_163.setObjectName(u"label_163")
        sizePolicy1.setHeightForWidth(self.label_163.sizePolicy().hasHeightForWidth())
        self.label_163.setSizePolicy(sizePolicy1)
        self.label_163.setMinimumSize(QSize(0, 0))
        self.label_163.setMaximumSize(QSize(16777215, 16777215))
        self.label_163.setFont(font1)

        self.horizontalLayout_183.addWidget(self.label_163)

        self.dSBoxLimitThresholdBand_14 = QDoubleSpinBox(self.gpLimitBand_14)
        self.dSBoxLimitThresholdBand_14.setObjectName(u"dSBoxLimitThresholdBand_14")
        self.dSBoxLimitThresholdBand_14.setDecimals(1)
        self.dSBoxLimitThresholdBand_14.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_14.setMaximum(65535.500000000000000)

        self.horizontalLayout_183.addWidget(self.dSBoxLimitThresholdBand_14)


        self.gridLayout_32.addLayout(self.horizontalLayout_183, 0, 0, 1, 1)

        self.horizontalLayout_184 = QHBoxLayout()
        self.horizontalLayout_184.setObjectName(u"horizontalLayout_184")
        self.label_164 = QLabel(self.gpLimitBand_14)
        self.label_164.setObjectName(u"label_164")
        sizePolicy1.setHeightForWidth(self.label_164.sizePolicy().hasHeightForWidth())
        self.label_164.setSizePolicy(sizePolicy1)
        self.label_164.setMinimumSize(QSize(0, 0))
        self.label_164.setMaximumSize(QSize(16777215, 16777215))
        self.label_164.setFont(font1)

        self.horizontalLayout_184.addWidget(self.label_164)

        self.dSBoxLimitKneeBand_14 = QDoubleSpinBox(self.gpLimitBand_14)
        self.dSBoxLimitKneeBand_14.setObjectName(u"dSBoxLimitKneeBand_14")
        self.dSBoxLimitKneeBand_14.setDecimals(1)
        self.dSBoxLimitKneeBand_14.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_14.setMaximum(30.000000000000000)

        self.horizontalLayout_184.addWidget(self.dSBoxLimitKneeBand_14)


        self.gridLayout_32.addLayout(self.horizontalLayout_184, 0, 1, 1, 1)


        self.verticalLayout_18.addWidget(self.gpLimitBand_14)

        self.groupBox_22 = QGroupBox(self.gpBand_14)
        self.groupBox_22.setObjectName(u"groupBox_22")
        sizePolicy6.setHeightForWidth(self.groupBox_22.sizePolicy().hasHeightForWidth())
        self.groupBox_22.setSizePolicy(sizePolicy6)
        self.gridLayout_33 = QGridLayout(self.groupBox_22)
        self.gridLayout_33.setObjectName(u"gridLayout_33")
        self.gridLayout_33.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_185 = QHBoxLayout()
        self.horizontalLayout_185.setObjectName(u"horizontalLayout_185")
        self.label_165 = QLabel(self.groupBox_22)
        self.label_165.setObjectName(u"label_165")
        sizePolicy1.setHeightForWidth(self.label_165.sizePolicy().hasHeightForWidth())
        self.label_165.setSizePolicy(sizePolicy1)
        self.label_165.setMinimumSize(QSize(30, 0))
        self.label_165.setMaximumSize(QSize(16777215, 16777215))
        self.label_165.setFont(font1)

        self.horizontalLayout_185.addWidget(self.label_165)

        self.dSBoxSmallSPLBand_14 = QDoubleSpinBox(self.groupBox_22)
        self.dSBoxSmallSPLBand_14.setObjectName(u"dSBoxSmallSPLBand_14")
        self.dSBoxSmallSPLBand_14.setDecimals(1)
        self.dSBoxSmallSPLBand_14.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_14.setMaximum(65535.500000000000000)

        self.horizontalLayout_185.addWidget(self.dSBoxSmallSPLBand_14)


        self.gridLayout_33.addLayout(self.horizontalLayout_185, 0, 0, 1, 1)

        self.horizontalLayout_186 = QHBoxLayout()
        self.horizontalLayout_186.setObjectName(u"horizontalLayout_186")
        self.label_166 = QLabel(self.groupBox_22)
        self.label_166.setObjectName(u"label_166")
        sizePolicy1.setHeightForWidth(self.label_166.sizePolicy().hasHeightForWidth())
        self.label_166.setSizePolicy(sizePolicy1)
        self.label_166.setMinimumSize(QSize(0, 0))
        self.label_166.setMaximumSize(QSize(16777215, 16777215))
        self.label_166.setFont(font1)

        self.horizontalLayout_186.addWidget(self.label_166)

        self.dSBoxSmallSPLGainBand_14 = QDoubleSpinBox(self.groupBox_22)
        self.dSBoxSmallSPLGainBand_14.setObjectName(u"dSBoxSmallSPLGainBand_14")
        self.dSBoxSmallSPLGainBand_14.setDecimals(1)
        self.dSBoxSmallSPLGainBand_14.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_14.setMaximum(65535.500000000000000)

        self.horizontalLayout_186.addWidget(self.dSBoxSmallSPLGainBand_14)


        self.gridLayout_33.addLayout(self.horizontalLayout_186, 0, 1, 1, 1)

        self.horizontalLayout_187 = QHBoxLayout()
        self.horizontalLayout_187.setObjectName(u"horizontalLayout_187")
        self.label_167 = QLabel(self.groupBox_22)
        self.label_167.setObjectName(u"label_167")
        sizePolicy1.setHeightForWidth(self.label_167.sizePolicy().hasHeightForWidth())
        self.label_167.setSizePolicy(sizePolicy1)
        self.label_167.setMinimumSize(QSize(30, 0))
        self.label_167.setMaximumSize(QSize(16777215, 16777215))
        self.label_167.setFont(font1)

        self.horizontalLayout_187.addWidget(self.label_167)

        self.dSBoxMediumSPLBand_14 = QDoubleSpinBox(self.groupBox_22)
        self.dSBoxMediumSPLBand_14.setObjectName(u"dSBoxMediumSPLBand_14")
        self.dSBoxMediumSPLBand_14.setDecimals(1)
        self.dSBoxMediumSPLBand_14.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_14.setMaximum(65535.500000000000000)

        self.horizontalLayout_187.addWidget(self.dSBoxMediumSPLBand_14)


        self.gridLayout_33.addLayout(self.horizontalLayout_187, 1, 0, 1, 1)

        self.horizontalLayout_188 = QHBoxLayout()
        self.horizontalLayout_188.setObjectName(u"horizontalLayout_188")
        self.label_168 = QLabel(self.groupBox_22)
        self.label_168.setObjectName(u"label_168")
        sizePolicy1.setHeightForWidth(self.label_168.sizePolicy().hasHeightForWidth())
        self.label_168.setSizePolicy(sizePolicy1)
        self.label_168.setMinimumSize(QSize(0, 0))
        self.label_168.setMaximumSize(QSize(16777215, 16777215))
        self.label_168.setFont(font1)

        self.horizontalLayout_188.addWidget(self.label_168)

        self.dSBoxMediumSPLGainBand_14 = QDoubleSpinBox(self.groupBox_22)
        self.dSBoxMediumSPLGainBand_14.setObjectName(u"dSBoxMediumSPLGainBand_14")
        self.dSBoxMediumSPLGainBand_14.setDecimals(1)
        self.dSBoxMediumSPLGainBand_14.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_14.setMaximum(65535.500000000000000)

        self.horizontalLayout_188.addWidget(self.dSBoxMediumSPLGainBand_14)


        self.gridLayout_33.addLayout(self.horizontalLayout_188, 1, 1, 1, 1)

        self.horizontalLayout_189 = QHBoxLayout()
        self.horizontalLayout_189.setObjectName(u"horizontalLayout_189")
        self.label_169 = QLabel(self.groupBox_22)
        self.label_169.setObjectName(u"label_169")
        sizePolicy1.setHeightForWidth(self.label_169.sizePolicy().hasHeightForWidth())
        self.label_169.setSizePolicy(sizePolicy1)
        self.label_169.setMinimumSize(QSize(30, 0))
        self.label_169.setMaximumSize(QSize(16777215, 16777215))
        self.label_169.setFont(font1)

        self.horizontalLayout_189.addWidget(self.label_169)

        self.dSBoxLargeSPLBand_14 = QDoubleSpinBox(self.groupBox_22)
        self.dSBoxLargeSPLBand_14.setObjectName(u"dSBoxLargeSPLBand_14")
        self.dSBoxLargeSPLBand_14.setDecimals(1)
        self.dSBoxLargeSPLBand_14.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_14.setMaximum(65535.500000000000000)

        self.horizontalLayout_189.addWidget(self.dSBoxLargeSPLBand_14)


        self.gridLayout_33.addLayout(self.horizontalLayout_189, 2, 0, 1, 1)

        self.horizontalLayout_190 = QHBoxLayout()
        self.horizontalLayout_190.setObjectName(u"horizontalLayout_190")
        self.label_170 = QLabel(self.groupBox_22)
        self.label_170.setObjectName(u"label_170")
        sizePolicy1.setHeightForWidth(self.label_170.sizePolicy().hasHeightForWidth())
        self.label_170.setSizePolicy(sizePolicy1)
        self.label_170.setMinimumSize(QSize(0, 0))
        self.label_170.setMaximumSize(QSize(16777215, 16777215))
        self.label_170.setFont(font1)

        self.horizontalLayout_190.addWidget(self.label_170)

        self.dSBoxLargeSPLGainBand_14 = QDoubleSpinBox(self.groupBox_22)
        self.dSBoxLargeSPLGainBand_14.setObjectName(u"dSBoxLargeSPLGainBand_14")
        self.dSBoxLargeSPLGainBand_14.setDecimals(1)
        self.dSBoxLargeSPLGainBand_14.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_14.setMaximum(65535.500000000000000)

        self.horizontalLayout_190.addWidget(self.dSBoxLargeSPLGainBand_14)


        self.gridLayout_33.addLayout(self.horizontalLayout_190, 2, 1, 1, 1)


        self.verticalLayout_18.addWidget(self.groupBox_22)


        self.gridLayout_15.addWidget(self.gpBand_14, 8, 0, 1, 1)

        self.gpBand_3 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_3.setObjectName(u"gpBand_3")
        sizePolicy8.setHeightForWidth(self.gpBand_3.sizePolicy().hasHeightForWidth())
        self.gpBand_3.setSizePolicy(sizePolicy8)
        self.gpBand_3.setMinimumSize(QSize(0, 0))
        self.gpBand_3.setFont(font1)
        self.verticalLayout_7 = QVBoxLayout(self.gpBand_3)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.horizontalLayout_22 = QHBoxLayout()
        self.horizontalLayout_22.setObjectName(u"horizontalLayout_22")
        self.horizontalLayout_22.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_56 = QHBoxLayout()
        self.horizontalLayout_56.setObjectName(u"horizontalLayout_56")
        self.label_47 = QLabel(self.gpBand_3)
        self.label_47.setObjectName(u"label_47")
        sizePolicy1.setHeightForWidth(self.label_47.sizePolicy().hasHeightForWidth())
        self.label_47.setSizePolicy(sizePolicy1)
        self.label_47.setMinimumSize(QSize(0, 0))
        self.label_47.setMaximumSize(QSize(16777215, 16777215))
        self.label_47.setFont(font1)

        self.horizontalLayout_56.addWidget(self.label_47)

        self.spinBoxAttackBand_3 = QSpinBox(self.gpBand_3)
        self.spinBoxAttackBand_3.setObjectName(u"spinBoxAttackBand_3")
        self.spinBoxAttackBand_3.setMaximum(65535)

        self.horizontalLayout_56.addWidget(self.spinBoxAttackBand_3)


        self.horizontalLayout_22.addLayout(self.horizontalLayout_56)

        self.horizontalLayout_57 = QHBoxLayout()
        self.horizontalLayout_57.setObjectName(u"horizontalLayout_57")
        self.label_48 = QLabel(self.gpBand_3)
        self.label_48.setObjectName(u"label_48")
        sizePolicy1.setHeightForWidth(self.label_48.sizePolicy().hasHeightForWidth())
        self.label_48.setSizePolicy(sizePolicy1)
        self.label_48.setMinimumSize(QSize(0, 0))
        self.label_48.setMaximumSize(QSize(16777215, 16777215))
        self.label_48.setFont(font1)

        self.horizontalLayout_57.addWidget(self.label_48)

        self.spinBoxReleaseBand_3 = QSpinBox(self.gpBand_3)
        self.spinBoxReleaseBand_3.setObjectName(u"spinBoxReleaseBand_3")
        self.spinBoxReleaseBand_3.setMaximum(65535)

        self.horizontalLayout_57.addWidget(self.spinBoxReleaseBand_3)


        self.horizontalLayout_22.addLayout(self.horizontalLayout_57)


        self.verticalLayout_7.addLayout(self.horizontalLayout_22)

        self.gpLimitBand_3 = QGroupBox(self.gpBand_3)
        self.gpLimitBand_3.setObjectName(u"gpLimitBand_3")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_3.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_3.setSizePolicy(sizePolicy6)
        self.gpLimitBand_3.setFont(font1)
        self.gridLayout_8 = QGridLayout(self.gpLimitBand_3)
        self.gridLayout_8.setObjectName(u"gridLayout_8")
        self.gridLayout_8.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_23 = QHBoxLayout()
        self.horizontalLayout_23.setObjectName(u"horizontalLayout_23")
        self.label_14 = QLabel(self.gpLimitBand_3)
        self.label_14.setObjectName(u"label_14")
        sizePolicy1.setHeightForWidth(self.label_14.sizePolicy().hasHeightForWidth())
        self.label_14.setSizePolicy(sizePolicy1)
        self.label_14.setMinimumSize(QSize(0, 0))
        self.label_14.setMaximumSize(QSize(16777215, 16777215))
        self.label_14.setFont(font1)

        self.horizontalLayout_23.addWidget(self.label_14)

        self.dSBoxLimitThresholdBand_3 = QDoubleSpinBox(self.gpLimitBand_3)
        self.dSBoxLimitThresholdBand_3.setObjectName(u"dSBoxLimitThresholdBand_3")
        self.dSBoxLimitThresholdBand_3.setDecimals(1)
        self.dSBoxLimitThresholdBand_3.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_3.setMaximum(65535.500000000000000)

        self.horizontalLayout_23.addWidget(self.dSBoxLimitThresholdBand_3)


        self.gridLayout_8.addLayout(self.horizontalLayout_23, 0, 0, 1, 1)

        self.horizontalLayout_58 = QHBoxLayout()
        self.horizontalLayout_58.setObjectName(u"horizontalLayout_58")
        self.label_49 = QLabel(self.gpLimitBand_3)
        self.label_49.setObjectName(u"label_49")
        sizePolicy1.setHeightForWidth(self.label_49.sizePolicy().hasHeightForWidth())
        self.label_49.setSizePolicy(sizePolicy1)
        self.label_49.setMinimumSize(QSize(0, 0))
        self.label_49.setMaximumSize(QSize(16777215, 16777215))
        self.label_49.setFont(font1)

        self.horizontalLayout_58.addWidget(self.label_49)

        self.dSBoxLimitKneeBand_3 = QDoubleSpinBox(self.gpLimitBand_3)
        self.dSBoxLimitKneeBand_3.setObjectName(u"dSBoxLimitKneeBand_3")
        self.dSBoxLimitKneeBand_3.setDecimals(1)
        self.dSBoxLimitKneeBand_3.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_3.setMaximum(30.000000000000000)

        self.horizontalLayout_58.addWidget(self.dSBoxLimitKneeBand_3)


        self.gridLayout_8.addLayout(self.horizontalLayout_58, 0, 1, 1, 1)


        self.verticalLayout_7.addWidget(self.gpLimitBand_3)

        self.groupBox_11 = QGroupBox(self.gpBand_3)
        self.groupBox_11.setObjectName(u"groupBox_11")
        sizePolicy6.setHeightForWidth(self.groupBox_11.sizePolicy().hasHeightForWidth())
        self.groupBox_11.setSizePolicy(sizePolicy6)
        self.gridLayout_9 = QGridLayout(self.groupBox_11)
        self.gridLayout_9.setObjectName(u"gridLayout_9")
        self.gridLayout_9.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_59 = QHBoxLayout()
        self.horizontalLayout_59.setObjectName(u"horizontalLayout_59")
        self.label_50 = QLabel(self.groupBox_11)
        self.label_50.setObjectName(u"label_50")
        sizePolicy1.setHeightForWidth(self.label_50.sizePolicy().hasHeightForWidth())
        self.label_50.setSizePolicy(sizePolicy1)
        self.label_50.setMinimumSize(QSize(30, 0))
        self.label_50.setMaximumSize(QSize(16777215, 16777215))
        self.label_50.setFont(font1)

        self.horizontalLayout_59.addWidget(self.label_50)

        self.dSBoxSmallSPLBand_3 = QDoubleSpinBox(self.groupBox_11)
        self.dSBoxSmallSPLBand_3.setObjectName(u"dSBoxSmallSPLBand_3")
        self.dSBoxSmallSPLBand_3.setDecimals(1)
        self.dSBoxSmallSPLBand_3.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_3.setMaximum(65535.500000000000000)

        self.horizontalLayout_59.addWidget(self.dSBoxSmallSPLBand_3)


        self.gridLayout_9.addLayout(self.horizontalLayout_59, 0, 0, 1, 1)

        self.horizontalLayout_60 = QHBoxLayout()
        self.horizontalLayout_60.setObjectName(u"horizontalLayout_60")
        self.label_51 = QLabel(self.groupBox_11)
        self.label_51.setObjectName(u"label_51")
        sizePolicy1.setHeightForWidth(self.label_51.sizePolicy().hasHeightForWidth())
        self.label_51.setSizePolicy(sizePolicy1)
        self.label_51.setMinimumSize(QSize(0, 0))
        self.label_51.setMaximumSize(QSize(16777215, 16777215))
        self.label_51.setFont(font1)

        self.horizontalLayout_60.addWidget(self.label_51)

        self.dSBoxSmallSPLGainBand_3 = QDoubleSpinBox(self.groupBox_11)
        self.dSBoxSmallSPLGainBand_3.setObjectName(u"dSBoxSmallSPLGainBand_3")
        self.dSBoxSmallSPLGainBand_3.setDecimals(1)
        self.dSBoxSmallSPLGainBand_3.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_3.setMaximum(65535.500000000000000)

        self.horizontalLayout_60.addWidget(self.dSBoxSmallSPLGainBand_3)


        self.gridLayout_9.addLayout(self.horizontalLayout_60, 0, 1, 1, 1)

        self.horizontalLayout_61 = QHBoxLayout()
        self.horizontalLayout_61.setObjectName(u"horizontalLayout_61")
        self.label_52 = QLabel(self.groupBox_11)
        self.label_52.setObjectName(u"label_52")
        sizePolicy1.setHeightForWidth(self.label_52.sizePolicy().hasHeightForWidth())
        self.label_52.setSizePolicy(sizePolicy1)
        self.label_52.setMinimumSize(QSize(30, 0))
        self.label_52.setMaximumSize(QSize(16777215, 16777215))
        self.label_52.setFont(font1)

        self.horizontalLayout_61.addWidget(self.label_52)

        self.dSBoxMediumSPLBand_3 = QDoubleSpinBox(self.groupBox_11)
        self.dSBoxMediumSPLBand_3.setObjectName(u"dSBoxMediumSPLBand_3")
        self.dSBoxMediumSPLBand_3.setDecimals(1)
        self.dSBoxMediumSPLBand_3.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_3.setMaximum(65535.500000000000000)

        self.horizontalLayout_61.addWidget(self.dSBoxMediumSPLBand_3)


        self.gridLayout_9.addLayout(self.horizontalLayout_61, 1, 0, 1, 1)

        self.horizontalLayout_62 = QHBoxLayout()
        self.horizontalLayout_62.setObjectName(u"horizontalLayout_62")
        self.label_53 = QLabel(self.groupBox_11)
        self.label_53.setObjectName(u"label_53")
        sizePolicy1.setHeightForWidth(self.label_53.sizePolicy().hasHeightForWidth())
        self.label_53.setSizePolicy(sizePolicy1)
        self.label_53.setMinimumSize(QSize(0, 0))
        self.label_53.setMaximumSize(QSize(16777215, 16777215))
        self.label_53.setFont(font1)

        self.horizontalLayout_62.addWidget(self.label_53)

        self.dSBoxMediumSPLGainBand_3 = QDoubleSpinBox(self.groupBox_11)
        self.dSBoxMediumSPLGainBand_3.setObjectName(u"dSBoxMediumSPLGainBand_3")
        self.dSBoxMediumSPLGainBand_3.setDecimals(1)
        self.dSBoxMediumSPLGainBand_3.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_3.setMaximum(65535.500000000000000)

        self.horizontalLayout_62.addWidget(self.dSBoxMediumSPLGainBand_3)


        self.gridLayout_9.addLayout(self.horizontalLayout_62, 1, 1, 1, 1)

        self.horizontalLayout_63 = QHBoxLayout()
        self.horizontalLayout_63.setObjectName(u"horizontalLayout_63")
        self.label_54 = QLabel(self.groupBox_11)
        self.label_54.setObjectName(u"label_54")
        sizePolicy1.setHeightForWidth(self.label_54.sizePolicy().hasHeightForWidth())
        self.label_54.setSizePolicy(sizePolicy1)
        self.label_54.setMinimumSize(QSize(30, 0))
        self.label_54.setMaximumSize(QSize(16777215, 16777215))
        self.label_54.setFont(font1)

        self.horizontalLayout_63.addWidget(self.label_54)

        self.dSBoxLargeSPLBand_3 = QDoubleSpinBox(self.groupBox_11)
        self.dSBoxLargeSPLBand_3.setObjectName(u"dSBoxLargeSPLBand_3")
        self.dSBoxLargeSPLBand_3.setDecimals(1)
        self.dSBoxLargeSPLBand_3.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_3.setMaximum(65535.500000000000000)

        self.horizontalLayout_63.addWidget(self.dSBoxLargeSPLBand_3)


        self.gridLayout_9.addLayout(self.horizontalLayout_63, 2, 0, 1, 1)

        self.horizontalLayout_64 = QHBoxLayout()
        self.horizontalLayout_64.setObjectName(u"horizontalLayout_64")
        self.label_55 = QLabel(self.groupBox_11)
        self.label_55.setObjectName(u"label_55")
        sizePolicy1.setHeightForWidth(self.label_55.sizePolicy().hasHeightForWidth())
        self.label_55.setSizePolicy(sizePolicy1)
        self.label_55.setMinimumSize(QSize(0, 0))
        self.label_55.setMaximumSize(QSize(16777215, 16777215))
        self.label_55.setFont(font1)

        self.horizontalLayout_64.addWidget(self.label_55)

        self.dSBoxLargeSPLGainBand_3 = QDoubleSpinBox(self.groupBox_11)
        self.dSBoxLargeSPLGainBand_3.setObjectName(u"dSBoxLargeSPLGainBand_3")
        self.dSBoxLargeSPLGainBand_3.setDecimals(1)
        self.dSBoxLargeSPLGainBand_3.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_3.setMaximum(65535.500000000000000)

        self.horizontalLayout_64.addWidget(self.dSBoxLargeSPLGainBand_3)


        self.gridLayout_9.addLayout(self.horizontalLayout_64, 2, 1, 1, 1)


        self.verticalLayout_7.addWidget(self.groupBox_11)


        self.gridLayout_15.addWidget(self.gpBand_3, 1, 1, 1, 1)

        self.gpBand_2 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_2.setObjectName(u"gpBand_2")
        sizePolicy8.setHeightForWidth(self.gpBand_2.sizePolicy().hasHeightForWidth())
        self.gpBand_2.setSizePolicy(sizePolicy8)
        self.gpBand_2.setMinimumSize(QSize(0, 0))
        self.gpBand_2.setFont(font1)
        self.verticalLayout_6 = QVBoxLayout(self.gpBand_2)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.horizontalLayout_20 = QHBoxLayout()
        self.horizontalLayout_20.setObjectName(u"horizontalLayout_20")
        self.horizontalLayout_20.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_46 = QHBoxLayout()
        self.horizontalLayout_46.setObjectName(u"horizontalLayout_46")
        self.label_37 = QLabel(self.gpBand_2)
        self.label_37.setObjectName(u"label_37")
        sizePolicy1.setHeightForWidth(self.label_37.sizePolicy().hasHeightForWidth())
        self.label_37.setSizePolicy(sizePolicy1)
        self.label_37.setMinimumSize(QSize(0, 0))
        self.label_37.setMaximumSize(QSize(16777215, 16777215))
        self.label_37.setFont(font1)

        self.horizontalLayout_46.addWidget(self.label_37)

        self.spinBoxAttackBand_2 = QSpinBox(self.gpBand_2)
        self.spinBoxAttackBand_2.setObjectName(u"spinBoxAttackBand_2")
        self.spinBoxAttackBand_2.setMaximum(65535)

        self.horizontalLayout_46.addWidget(self.spinBoxAttackBand_2)


        self.horizontalLayout_20.addLayout(self.horizontalLayout_46)

        self.horizontalLayout_47 = QHBoxLayout()
        self.horizontalLayout_47.setObjectName(u"horizontalLayout_47")
        self.label_38 = QLabel(self.gpBand_2)
        self.label_38.setObjectName(u"label_38")
        sizePolicy1.setHeightForWidth(self.label_38.sizePolicy().hasHeightForWidth())
        self.label_38.setSizePolicy(sizePolicy1)
        self.label_38.setMinimumSize(QSize(0, 0))
        self.label_38.setMaximumSize(QSize(16777215, 16777215))
        self.label_38.setFont(font1)

        self.horizontalLayout_47.addWidget(self.label_38)

        self.spinBoxReleaseBand_2 = QSpinBox(self.gpBand_2)
        self.spinBoxReleaseBand_2.setObjectName(u"spinBoxReleaseBand_2")
        self.spinBoxReleaseBand_2.setMaximum(65535)

        self.horizontalLayout_47.addWidget(self.spinBoxReleaseBand_2)


        self.horizontalLayout_20.addLayout(self.horizontalLayout_47)


        self.verticalLayout_6.addLayout(self.horizontalLayout_20)

        self.gpLimitBand_2 = QGroupBox(self.gpBand_2)
        self.gpLimitBand_2.setObjectName(u"gpLimitBand_2")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_2.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_2.setSizePolicy(sizePolicy6)
        self.gpLimitBand_2.setFont(font1)
        self.gridLayout_6 = QGridLayout(self.gpLimitBand_2)
        self.gridLayout_6.setObjectName(u"gridLayout_6")
        self.gridLayout_6.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_21 = QHBoxLayout()
        self.horizontalLayout_21.setObjectName(u"horizontalLayout_21")
        self.label_13 = QLabel(self.gpLimitBand_2)
        self.label_13.setObjectName(u"label_13")
        sizePolicy1.setHeightForWidth(self.label_13.sizePolicy().hasHeightForWidth())
        self.label_13.setSizePolicy(sizePolicy1)
        self.label_13.setMinimumSize(QSize(0, 0))
        self.label_13.setMaximumSize(QSize(16777215, 16777215))
        self.label_13.setFont(font1)

        self.horizontalLayout_21.addWidget(self.label_13)

        self.dSBoxLimitThresholdBand_2 = QDoubleSpinBox(self.gpLimitBand_2)
        self.dSBoxLimitThresholdBand_2.setObjectName(u"dSBoxLimitThresholdBand_2")
        self.dSBoxLimitThresholdBand_2.setDecimals(1)
        self.dSBoxLimitThresholdBand_2.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_2.setMaximum(65535.500000000000000)

        self.horizontalLayout_21.addWidget(self.dSBoxLimitThresholdBand_2)


        self.gridLayout_6.addLayout(self.horizontalLayout_21, 0, 0, 1, 1)

        self.horizontalLayout_48 = QHBoxLayout()
        self.horizontalLayout_48.setObjectName(u"horizontalLayout_48")
        self.label_39 = QLabel(self.gpLimitBand_2)
        self.label_39.setObjectName(u"label_39")
        sizePolicy1.setHeightForWidth(self.label_39.sizePolicy().hasHeightForWidth())
        self.label_39.setSizePolicy(sizePolicy1)
        self.label_39.setMinimumSize(QSize(0, 0))
        self.label_39.setMaximumSize(QSize(16777215, 16777215))
        self.label_39.setFont(font1)

        self.horizontalLayout_48.addWidget(self.label_39)

        self.dSBoxLimitKneeBand_2 = QDoubleSpinBox(self.gpLimitBand_2)
        self.dSBoxLimitKneeBand_2.setObjectName(u"dSBoxLimitKneeBand_2")
        self.dSBoxLimitKneeBand_2.setDecimals(1)
        self.dSBoxLimitKneeBand_2.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_2.setMaximum(30.000000000000000)

        self.horizontalLayout_48.addWidget(self.dSBoxLimitKneeBand_2)


        self.gridLayout_6.addLayout(self.horizontalLayout_48, 0, 1, 1, 1)


        self.verticalLayout_6.addWidget(self.gpLimitBand_2)

        self.groupBox_10 = QGroupBox(self.gpBand_2)
        self.groupBox_10.setObjectName(u"groupBox_10")
        sizePolicy6.setHeightForWidth(self.groupBox_10.sizePolicy().hasHeightForWidth())
        self.groupBox_10.setSizePolicy(sizePolicy6)
        self.gridLayout_7 = QGridLayout(self.groupBox_10)
        self.gridLayout_7.setObjectName(u"gridLayout_7")
        self.gridLayout_7.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_49 = QHBoxLayout()
        self.horizontalLayout_49.setObjectName(u"horizontalLayout_49")
        self.label_40 = QLabel(self.groupBox_10)
        self.label_40.setObjectName(u"label_40")
        sizePolicy1.setHeightForWidth(self.label_40.sizePolicy().hasHeightForWidth())
        self.label_40.setSizePolicy(sizePolicy1)
        self.label_40.setMinimumSize(QSize(30, 0))
        self.label_40.setMaximumSize(QSize(16777215, 16777215))
        self.label_40.setFont(font1)

        self.horizontalLayout_49.addWidget(self.label_40)

        self.dSBoxSmallSPLBand_2 = QDoubleSpinBox(self.groupBox_10)
        self.dSBoxSmallSPLBand_2.setObjectName(u"dSBoxSmallSPLBand_2")
        self.dSBoxSmallSPLBand_2.setDecimals(1)
        self.dSBoxSmallSPLBand_2.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_2.setMaximum(65535.500000000000000)

        self.horizontalLayout_49.addWidget(self.dSBoxSmallSPLBand_2)


        self.gridLayout_7.addLayout(self.horizontalLayout_49, 0, 0, 1, 1)

        self.horizontalLayout_50 = QHBoxLayout()
        self.horizontalLayout_50.setObjectName(u"horizontalLayout_50")
        self.label_41 = QLabel(self.groupBox_10)
        self.label_41.setObjectName(u"label_41")
        sizePolicy1.setHeightForWidth(self.label_41.sizePolicy().hasHeightForWidth())
        self.label_41.setSizePolicy(sizePolicy1)
        self.label_41.setMinimumSize(QSize(0, 0))
        self.label_41.setMaximumSize(QSize(16777215, 16777215))
        self.label_41.setFont(font1)

        self.horizontalLayout_50.addWidget(self.label_41)

        self.dSBoxSmallSPLGainBand_2 = QDoubleSpinBox(self.groupBox_10)
        self.dSBoxSmallSPLGainBand_2.setObjectName(u"dSBoxSmallSPLGainBand_2")
        self.dSBoxSmallSPLGainBand_2.setDecimals(1)
        self.dSBoxSmallSPLGainBand_2.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_2.setMaximum(65535.500000000000000)

        self.horizontalLayout_50.addWidget(self.dSBoxSmallSPLGainBand_2)


        self.gridLayout_7.addLayout(self.horizontalLayout_50, 0, 1, 1, 1)

        self.horizontalLayout_51 = QHBoxLayout()
        self.horizontalLayout_51.setObjectName(u"horizontalLayout_51")
        self.label_42 = QLabel(self.groupBox_10)
        self.label_42.setObjectName(u"label_42")
        sizePolicy1.setHeightForWidth(self.label_42.sizePolicy().hasHeightForWidth())
        self.label_42.setSizePolicy(sizePolicy1)
        self.label_42.setMinimumSize(QSize(30, 0))
        self.label_42.setMaximumSize(QSize(16777215, 16777215))
        self.label_42.setFont(font1)

        self.horizontalLayout_51.addWidget(self.label_42)

        self.dSBoxMediumSPLBand_2 = QDoubleSpinBox(self.groupBox_10)
        self.dSBoxMediumSPLBand_2.setObjectName(u"dSBoxMediumSPLBand_2")
        self.dSBoxMediumSPLBand_2.setDecimals(1)
        self.dSBoxMediumSPLBand_2.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_2.setMaximum(65535.500000000000000)

        self.horizontalLayout_51.addWidget(self.dSBoxMediumSPLBand_2)


        self.gridLayout_7.addLayout(self.horizontalLayout_51, 1, 0, 1, 1)

        self.horizontalLayout_52 = QHBoxLayout()
        self.horizontalLayout_52.setObjectName(u"horizontalLayout_52")
        self.label_43 = QLabel(self.groupBox_10)
        self.label_43.setObjectName(u"label_43")
        sizePolicy1.setHeightForWidth(self.label_43.sizePolicy().hasHeightForWidth())
        self.label_43.setSizePolicy(sizePolicy1)
        self.label_43.setMinimumSize(QSize(0, 0))
        self.label_43.setMaximumSize(QSize(16777215, 16777215))
        self.label_43.setFont(font1)

        self.horizontalLayout_52.addWidget(self.label_43)

        self.dSBoxMediumSPLGainBand_2 = QDoubleSpinBox(self.groupBox_10)
        self.dSBoxMediumSPLGainBand_2.setObjectName(u"dSBoxMediumSPLGainBand_2")
        self.dSBoxMediumSPLGainBand_2.setDecimals(1)
        self.dSBoxMediumSPLGainBand_2.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_2.setMaximum(65535.500000000000000)

        self.horizontalLayout_52.addWidget(self.dSBoxMediumSPLGainBand_2)


        self.gridLayout_7.addLayout(self.horizontalLayout_52, 1, 1, 1, 1)

        self.horizontalLayout_53 = QHBoxLayout()
        self.horizontalLayout_53.setObjectName(u"horizontalLayout_53")
        self.label_44 = QLabel(self.groupBox_10)
        self.label_44.setObjectName(u"label_44")
        sizePolicy1.setHeightForWidth(self.label_44.sizePolicy().hasHeightForWidth())
        self.label_44.setSizePolicy(sizePolicy1)
        self.label_44.setMinimumSize(QSize(30, 0))
        self.label_44.setMaximumSize(QSize(16777215, 16777215))
        self.label_44.setFont(font1)

        self.horizontalLayout_53.addWidget(self.label_44)

        self.dSBoxLargeSPLBand_2 = QDoubleSpinBox(self.groupBox_10)
        self.dSBoxLargeSPLBand_2.setObjectName(u"dSBoxLargeSPLBand_2")
        self.dSBoxLargeSPLBand_2.setDecimals(1)
        self.dSBoxLargeSPLBand_2.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_2.setMaximum(65535.500000000000000)

        self.horizontalLayout_53.addWidget(self.dSBoxLargeSPLBand_2)


        self.gridLayout_7.addLayout(self.horizontalLayout_53, 2, 0, 1, 1)

        self.horizontalLayout_54 = QHBoxLayout()
        self.horizontalLayout_54.setObjectName(u"horizontalLayout_54")
        self.label_45 = QLabel(self.groupBox_10)
        self.label_45.setObjectName(u"label_45")
        sizePolicy1.setHeightForWidth(self.label_45.sizePolicy().hasHeightForWidth())
        self.label_45.setSizePolicy(sizePolicy1)
        self.label_45.setMinimumSize(QSize(0, 0))
        self.label_45.setMaximumSize(QSize(16777215, 16777215))
        self.label_45.setFont(font1)

        self.horizontalLayout_54.addWidget(self.label_45)

        self.dSBoxLargeSPLGainBand_2 = QDoubleSpinBox(self.groupBox_10)
        self.dSBoxLargeSPLGainBand_2.setObjectName(u"dSBoxLargeSPLGainBand_2")
        self.dSBoxLargeSPLGainBand_2.setDecimals(1)
        self.dSBoxLargeSPLGainBand_2.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_2.setMaximum(65535.500000000000000)

        self.horizontalLayout_54.addWidget(self.dSBoxLargeSPLGainBand_2)


        self.gridLayout_7.addLayout(self.horizontalLayout_54, 2, 1, 1, 1)


        self.verticalLayout_6.addWidget(self.groupBox_10)


        self.gridLayout_15.addWidget(self.gpBand_2, 1, 0, 1, 1)

        self.gpBand_0 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_0.setObjectName(u"gpBand_0")
        sizePolicy8.setHeightForWidth(self.gpBand_0.sizePolicy().hasHeightForWidth())
        self.gpBand_0.setSizePolicy(sizePolicy8)
        self.gpBand_0.setMinimumSize(QSize(0, 0))
        self.gpBand_0.setFont(font1)
        self.verticalLayout_4 = QVBoxLayout(self.gpBand_0)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout_16 = QHBoxLayout()
        self.horizontalLayout_16.setObjectName(u"horizontalLayout_16")
        self.horizontalLayout_16.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_24 = QHBoxLayout()
        self.horizontalLayout_24.setObjectName(u"horizontalLayout_24")
        self.label_15 = QLabel(self.gpBand_0)
        self.label_15.setObjectName(u"label_15")
        sizePolicy1.setHeightForWidth(self.label_15.sizePolicy().hasHeightForWidth())
        self.label_15.setSizePolicy(sizePolicy1)
        self.label_15.setMinimumSize(QSize(0, 0))
        self.label_15.setMaximumSize(QSize(16777215, 16777215))
        self.label_15.setFont(font1)

        self.horizontalLayout_24.addWidget(self.label_15)

        self.spinBoxAttackBand_0 = QSpinBox(self.gpBand_0)
        self.spinBoxAttackBand_0.setObjectName(u"spinBoxAttackBand_0")
        self.spinBoxAttackBand_0.setMaximum(65535)

        self.horizontalLayout_24.addWidget(self.spinBoxAttackBand_0)


        self.horizontalLayout_16.addLayout(self.horizontalLayout_24)

        self.horizontalLayout_25 = QHBoxLayout()
        self.horizontalLayout_25.setObjectName(u"horizontalLayout_25")
        self.label_16 = QLabel(self.gpBand_0)
        self.label_16.setObjectName(u"label_16")
        sizePolicy1.setHeightForWidth(self.label_16.sizePolicy().hasHeightForWidth())
        self.label_16.setSizePolicy(sizePolicy1)
        self.label_16.setMinimumSize(QSize(0, 0))
        self.label_16.setMaximumSize(QSize(16777215, 16777215))
        self.label_16.setFont(font1)

        self.horizontalLayout_25.addWidget(self.label_16)

        self.spinBoxReleaseBand_0 = QSpinBox(self.gpBand_0)
        self.spinBoxReleaseBand_0.setObjectName(u"spinBoxReleaseBand_0")
        self.spinBoxReleaseBand_0.setMaximum(65535)

        self.horizontalLayout_25.addWidget(self.spinBoxReleaseBand_0)


        self.horizontalLayout_16.addLayout(self.horizontalLayout_25)


        self.verticalLayout_4.addLayout(self.horizontalLayout_16)

        self.gpLimitBand_0 = QGroupBox(self.gpBand_0)
        self.gpLimitBand_0.setObjectName(u"gpLimitBand_0")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_0.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_0.setSizePolicy(sizePolicy6)
        self.gpLimitBand_0.setFont(font1)
        self.gridLayout_2 = QGridLayout(self.gpLimitBand_0)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.gridLayout_2.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_17 = QHBoxLayout()
        self.horizontalLayout_17.setObjectName(u"horizontalLayout_17")
        self.label_11 = QLabel(self.gpLimitBand_0)
        self.label_11.setObjectName(u"label_11")
        sizePolicy1.setHeightForWidth(self.label_11.sizePolicy().hasHeightForWidth())
        self.label_11.setSizePolicy(sizePolicy1)
        self.label_11.setMinimumSize(QSize(0, 0))
        self.label_11.setMaximumSize(QSize(16777215, 16777215))
        self.label_11.setFont(font1)

        self.horizontalLayout_17.addWidget(self.label_11)

        self.dSBoxLimitThresholdBand_0 = QDoubleSpinBox(self.gpLimitBand_0)
        self.dSBoxLimitThresholdBand_0.setObjectName(u"dSBoxLimitThresholdBand_0")
        self.dSBoxLimitThresholdBand_0.setDecimals(1)
        self.dSBoxLimitThresholdBand_0.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_0.setMaximum(65535.500000000000000)

        self.horizontalLayout_17.addWidget(self.dSBoxLimitThresholdBand_0)


        self.gridLayout_2.addLayout(self.horizontalLayout_17, 0, 0, 1, 1)

        self.horizontalLayout_27 = QHBoxLayout()
        self.horizontalLayout_27.setObjectName(u"horizontalLayout_27")
        self.label_18 = QLabel(self.gpLimitBand_0)
        self.label_18.setObjectName(u"label_18")
        sizePolicy1.setHeightForWidth(self.label_18.sizePolicy().hasHeightForWidth())
        self.label_18.setSizePolicy(sizePolicy1)
        self.label_18.setMinimumSize(QSize(0, 0))
        self.label_18.setMaximumSize(QSize(16777215, 16777215))
        self.label_18.setFont(font1)

        self.horizontalLayout_27.addWidget(self.label_18)

        self.dSBoxLimitKneeBand_0 = QDoubleSpinBox(self.gpLimitBand_0)
        self.dSBoxLimitKneeBand_0.setObjectName(u"dSBoxLimitKneeBand_0")
        self.dSBoxLimitKneeBand_0.setDecimals(1)
        self.dSBoxLimitKneeBand_0.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_0.setMaximum(30.000000000000000)

        self.horizontalLayout_27.addWidget(self.dSBoxLimitKneeBand_0)


        self.gridLayout_2.addLayout(self.horizontalLayout_27, 0, 1, 1, 1)


        self.verticalLayout_4.addWidget(self.gpLimitBand_0)

        self.groupBox_8 = QGroupBox(self.gpBand_0)
        self.groupBox_8.setObjectName(u"groupBox_8")
        sizePolicy6.setHeightForWidth(self.groupBox_8.sizePolicy().hasHeightForWidth())
        self.groupBox_8.setSizePolicy(sizePolicy6)
        self.gridLayout_3 = QGridLayout(self.groupBox_8)
        self.gridLayout_3.setObjectName(u"gridLayout_3")
        self.gridLayout_3.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_30 = QHBoxLayout()
        self.horizontalLayout_30.setObjectName(u"horizontalLayout_30")
        self.label_20 = QLabel(self.groupBox_8)
        self.label_20.setObjectName(u"label_20")
        sizePolicy1.setHeightForWidth(self.label_20.sizePolicy().hasHeightForWidth())
        self.label_20.setSizePolicy(sizePolicy1)
        self.label_20.setMinimumSize(QSize(30, 0))
        self.label_20.setMaximumSize(QSize(16777215, 16777215))
        self.label_20.setFont(font1)

        self.horizontalLayout_30.addWidget(self.label_20)

        self.dSBoxSmallSPLBand_0 = QDoubleSpinBox(self.groupBox_8)
        self.dSBoxSmallSPLBand_0.setObjectName(u"dSBoxSmallSPLBand_0")
        self.dSBoxSmallSPLBand_0.setDecimals(1)
        self.dSBoxSmallSPLBand_0.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_0.setMaximum(65535.500000000000000)

        self.horizontalLayout_30.addWidget(self.dSBoxSmallSPLBand_0)


        self.gridLayout_3.addLayout(self.horizontalLayout_30, 0, 0, 1, 1)

        self.horizontalLayout_33 = QHBoxLayout()
        self.horizontalLayout_33.setObjectName(u"horizontalLayout_33")
        self.label_23 = QLabel(self.groupBox_8)
        self.label_23.setObjectName(u"label_23")
        sizePolicy1.setHeightForWidth(self.label_23.sizePolicy().hasHeightForWidth())
        self.label_23.setSizePolicy(sizePolicy1)
        self.label_23.setMinimumSize(QSize(0, 0))
        self.label_23.setMaximumSize(QSize(16777215, 16777215))
        self.label_23.setFont(font1)

        self.horizontalLayout_33.addWidget(self.label_23)

        self.dSBoxSmallSPLGainBand_0 = QDoubleSpinBox(self.groupBox_8)
        self.dSBoxSmallSPLGainBand_0.setObjectName(u"dSBoxSmallSPLGainBand_0")
        self.dSBoxSmallSPLGainBand_0.setDecimals(1)
        self.dSBoxSmallSPLGainBand_0.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_0.setMaximum(65535.500000000000000)

        self.horizontalLayout_33.addWidget(self.dSBoxSmallSPLGainBand_0)


        self.gridLayout_3.addLayout(self.horizontalLayout_33, 0, 1, 1, 1)

        self.horizontalLayout_31 = QHBoxLayout()
        self.horizontalLayout_31.setObjectName(u"horizontalLayout_31")
        self.label_21 = QLabel(self.groupBox_8)
        self.label_21.setObjectName(u"label_21")
        sizePolicy1.setHeightForWidth(self.label_21.sizePolicy().hasHeightForWidth())
        self.label_21.setSizePolicy(sizePolicy1)
        self.label_21.setMinimumSize(QSize(30, 0))
        self.label_21.setMaximumSize(QSize(16777215, 16777215))
        self.label_21.setFont(font1)

        self.horizontalLayout_31.addWidget(self.label_21)

        self.dSBoxMediumSPLBand_0 = QDoubleSpinBox(self.groupBox_8)
        self.dSBoxMediumSPLBand_0.setObjectName(u"dSBoxMediumSPLBand_0")
        self.dSBoxMediumSPLBand_0.setDecimals(1)
        self.dSBoxMediumSPLBand_0.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_0.setMaximum(65535.500000000000000)

        self.horizontalLayout_31.addWidget(self.dSBoxMediumSPLBand_0)


        self.gridLayout_3.addLayout(self.horizontalLayout_31, 1, 0, 1, 1)

        self.horizontalLayout_34 = QHBoxLayout()
        self.horizontalLayout_34.setObjectName(u"horizontalLayout_34")
        self.label_24 = QLabel(self.groupBox_8)
        self.label_24.setObjectName(u"label_24")
        sizePolicy1.setHeightForWidth(self.label_24.sizePolicy().hasHeightForWidth())
        self.label_24.setSizePolicy(sizePolicy1)
        self.label_24.setMinimumSize(QSize(0, 0))
        self.label_24.setMaximumSize(QSize(16777215, 16777215))
        self.label_24.setFont(font1)

        self.horizontalLayout_34.addWidget(self.label_24)

        self.dSBoxMediumSPLGainBand_0 = QDoubleSpinBox(self.groupBox_8)
        self.dSBoxMediumSPLGainBand_0.setObjectName(u"dSBoxMediumSPLGainBand_0")
        self.dSBoxMediumSPLGainBand_0.setDecimals(1)
        self.dSBoxMediumSPLGainBand_0.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_0.setMaximum(65535.500000000000000)

        self.horizontalLayout_34.addWidget(self.dSBoxMediumSPLGainBand_0)


        self.gridLayout_3.addLayout(self.horizontalLayout_34, 1, 1, 1, 1)

        self.horizontalLayout_32 = QHBoxLayout()
        self.horizontalLayout_32.setObjectName(u"horizontalLayout_32")
        self.label_22 = QLabel(self.groupBox_8)
        self.label_22.setObjectName(u"label_22")
        sizePolicy1.setHeightForWidth(self.label_22.sizePolicy().hasHeightForWidth())
        self.label_22.setSizePolicy(sizePolicy1)
        self.label_22.setMinimumSize(QSize(30, 0))
        self.label_22.setMaximumSize(QSize(16777215, 16777215))
        self.label_22.setFont(font1)

        self.horizontalLayout_32.addWidget(self.label_22)

        self.dSBoxLargeSPLBand_0 = QDoubleSpinBox(self.groupBox_8)
        self.dSBoxLargeSPLBand_0.setObjectName(u"dSBoxLargeSPLBand_0")
        self.dSBoxLargeSPLBand_0.setDecimals(1)
        self.dSBoxLargeSPLBand_0.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_0.setMaximum(65535.500000000000000)

        self.horizontalLayout_32.addWidget(self.dSBoxLargeSPLBand_0)


        self.gridLayout_3.addLayout(self.horizontalLayout_32, 2, 0, 1, 1)

        self.horizontalLayout_35 = QHBoxLayout()
        self.horizontalLayout_35.setObjectName(u"horizontalLayout_35")
        self.label_25 = QLabel(self.groupBox_8)
        self.label_25.setObjectName(u"label_25")
        sizePolicy1.setHeightForWidth(self.label_25.sizePolicy().hasHeightForWidth())
        self.label_25.setSizePolicy(sizePolicy1)
        self.label_25.setMinimumSize(QSize(0, 0))
        self.label_25.setMaximumSize(QSize(16777215, 16777215))
        self.label_25.setFont(font1)

        self.horizontalLayout_35.addWidget(self.label_25)

        self.dSBoxLargeSPLGainBand_0 = QDoubleSpinBox(self.groupBox_8)
        self.dSBoxLargeSPLGainBand_0.setObjectName(u"dSBoxLargeSPLGainBand_0")
        self.dSBoxLargeSPLGainBand_0.setDecimals(1)
        self.dSBoxLargeSPLGainBand_0.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_0.setMaximum(65535.500000000000000)

        self.horizontalLayout_35.addWidget(self.dSBoxLargeSPLGainBand_0)


        self.gridLayout_3.addLayout(self.horizontalLayout_35, 2, 1, 1, 1)


        self.verticalLayout_4.addWidget(self.groupBox_8)


        self.gridLayout_15.addWidget(self.gpBand_0, 0, 0, 1, 1)

        self.gpBand_7 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_7.setObjectName(u"gpBand_7")
        sizePolicy8.setHeightForWidth(self.gpBand_7.sizePolicy().hasHeightForWidth())
        self.gpBand_7.setSizePolicy(sizePolicy8)
        self.gpBand_7.setMinimumSize(QSize(0, 0))
        self.gpBand_7.setFont(font1)
        self.verticalLayout_11 = QVBoxLayout(self.gpBand_7)
        self.verticalLayout_11.setObjectName(u"verticalLayout_11")
        self.horizontalLayout_103 = QHBoxLayout()
        self.horizontalLayout_103.setObjectName(u"horizontalLayout_103")
        self.horizontalLayout_103.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_104 = QHBoxLayout()
        self.horizontalLayout_104.setObjectName(u"horizontalLayout_104")
        self.label_91 = QLabel(self.gpBand_7)
        self.label_91.setObjectName(u"label_91")
        sizePolicy1.setHeightForWidth(self.label_91.sizePolicy().hasHeightForWidth())
        self.label_91.setSizePolicy(sizePolicy1)
        self.label_91.setMinimumSize(QSize(0, 0))
        self.label_91.setMaximumSize(QSize(16777215, 16777215))
        self.label_91.setFont(font1)

        self.horizontalLayout_104.addWidget(self.label_91)

        self.spinBoxAttackBand_7 = QSpinBox(self.gpBand_7)
        self.spinBoxAttackBand_7.setObjectName(u"spinBoxAttackBand_7")
        self.spinBoxAttackBand_7.setMaximum(65535)

        self.horizontalLayout_104.addWidget(self.spinBoxAttackBand_7)


        self.horizontalLayout_103.addLayout(self.horizontalLayout_104)

        self.horizontalLayout_105 = QHBoxLayout()
        self.horizontalLayout_105.setObjectName(u"horizontalLayout_105")
        self.label_92 = QLabel(self.gpBand_7)
        self.label_92.setObjectName(u"label_92")
        sizePolicy1.setHeightForWidth(self.label_92.sizePolicy().hasHeightForWidth())
        self.label_92.setSizePolicy(sizePolicy1)
        self.label_92.setMinimumSize(QSize(0, 0))
        self.label_92.setMaximumSize(QSize(16777215, 16777215))
        self.label_92.setFont(font1)

        self.horizontalLayout_105.addWidget(self.label_92)

        self.spinBoxReleaseBand_7 = QSpinBox(self.gpBand_7)
        self.spinBoxReleaseBand_7.setObjectName(u"spinBoxReleaseBand_7")
        self.spinBoxReleaseBand_7.setMaximum(65535)

        self.horizontalLayout_105.addWidget(self.spinBoxReleaseBand_7)


        self.horizontalLayout_103.addLayout(self.horizontalLayout_105)


        self.verticalLayout_11.addLayout(self.horizontalLayout_103)

        self.gpLimitBand_7 = QGroupBox(self.gpBand_7)
        self.gpLimitBand_7.setObjectName(u"gpLimitBand_7")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_7.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_7.setSizePolicy(sizePolicy6)
        self.gpLimitBand_7.setFont(font1)
        self.gridLayout_18 = QGridLayout(self.gpLimitBand_7)
        self.gridLayout_18.setObjectName(u"gridLayout_18")
        self.gridLayout_18.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_106 = QHBoxLayout()
        self.horizontalLayout_106.setObjectName(u"horizontalLayout_106")
        self.label_93 = QLabel(self.gpLimitBand_7)
        self.label_93.setObjectName(u"label_93")
        sizePolicy1.setHeightForWidth(self.label_93.sizePolicy().hasHeightForWidth())
        self.label_93.setSizePolicy(sizePolicy1)
        self.label_93.setMinimumSize(QSize(0, 0))
        self.label_93.setMaximumSize(QSize(16777215, 16777215))
        self.label_93.setFont(font1)

        self.horizontalLayout_106.addWidget(self.label_93)

        self.dSBoxLimitThresholdBand_7 = QDoubleSpinBox(self.gpLimitBand_7)
        self.dSBoxLimitThresholdBand_7.setObjectName(u"dSBoxLimitThresholdBand_7")
        self.dSBoxLimitThresholdBand_7.setDecimals(1)
        self.dSBoxLimitThresholdBand_7.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_7.setMaximum(65535.500000000000000)

        self.horizontalLayout_106.addWidget(self.dSBoxLimitThresholdBand_7)


        self.gridLayout_18.addLayout(self.horizontalLayout_106, 0, 0, 1, 1)

        self.horizontalLayout_107 = QHBoxLayout()
        self.horizontalLayout_107.setObjectName(u"horizontalLayout_107")
        self.label_94 = QLabel(self.gpLimitBand_7)
        self.label_94.setObjectName(u"label_94")
        sizePolicy1.setHeightForWidth(self.label_94.sizePolicy().hasHeightForWidth())
        self.label_94.setSizePolicy(sizePolicy1)
        self.label_94.setMinimumSize(QSize(0, 0))
        self.label_94.setMaximumSize(QSize(16777215, 16777215))
        self.label_94.setFont(font1)

        self.horizontalLayout_107.addWidget(self.label_94)

        self.dSBoxLimitKneeBand_7 = QDoubleSpinBox(self.gpLimitBand_7)
        self.dSBoxLimitKneeBand_7.setObjectName(u"dSBoxLimitKneeBand_7")
        self.dSBoxLimitKneeBand_7.setDecimals(1)
        self.dSBoxLimitKneeBand_7.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_7.setMaximum(30.000000000000000)

        self.horizontalLayout_107.addWidget(self.dSBoxLimitKneeBand_7)


        self.gridLayout_18.addLayout(self.horizontalLayout_107, 0, 1, 1, 1)


        self.verticalLayout_11.addWidget(self.gpLimitBand_7)

        self.groupBox_15 = QGroupBox(self.gpBand_7)
        self.groupBox_15.setObjectName(u"groupBox_15")
        sizePolicy6.setHeightForWidth(self.groupBox_15.sizePolicy().hasHeightForWidth())
        self.groupBox_15.setSizePolicy(sizePolicy6)
        self.gridLayout_19 = QGridLayout(self.groupBox_15)
        self.gridLayout_19.setObjectName(u"gridLayout_19")
        self.gridLayout_19.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_108 = QHBoxLayout()
        self.horizontalLayout_108.setObjectName(u"horizontalLayout_108")
        self.label_95 = QLabel(self.groupBox_15)
        self.label_95.setObjectName(u"label_95")
        sizePolicy1.setHeightForWidth(self.label_95.sizePolicy().hasHeightForWidth())
        self.label_95.setSizePolicy(sizePolicy1)
        self.label_95.setMinimumSize(QSize(30, 0))
        self.label_95.setMaximumSize(QSize(16777215, 16777215))
        self.label_95.setFont(font1)

        self.horizontalLayout_108.addWidget(self.label_95)

        self.dSBoxSmallSPLBand_7 = QDoubleSpinBox(self.groupBox_15)
        self.dSBoxSmallSPLBand_7.setObjectName(u"dSBoxSmallSPLBand_7")
        self.dSBoxSmallSPLBand_7.setDecimals(1)
        self.dSBoxSmallSPLBand_7.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_7.setMaximum(65535.500000000000000)

        self.horizontalLayout_108.addWidget(self.dSBoxSmallSPLBand_7)


        self.gridLayout_19.addLayout(self.horizontalLayout_108, 0, 0, 1, 1)

        self.horizontalLayout_109 = QHBoxLayout()
        self.horizontalLayout_109.setObjectName(u"horizontalLayout_109")
        self.label_96 = QLabel(self.groupBox_15)
        self.label_96.setObjectName(u"label_96")
        sizePolicy1.setHeightForWidth(self.label_96.sizePolicy().hasHeightForWidth())
        self.label_96.setSizePolicy(sizePolicy1)
        self.label_96.setMinimumSize(QSize(0, 0))
        self.label_96.setMaximumSize(QSize(16777215, 16777215))
        self.label_96.setFont(font1)

        self.horizontalLayout_109.addWidget(self.label_96)

        self.dSBoxSmallSPLGainBand_7 = QDoubleSpinBox(self.groupBox_15)
        self.dSBoxSmallSPLGainBand_7.setObjectName(u"dSBoxSmallSPLGainBand_7")
        self.dSBoxSmallSPLGainBand_7.setDecimals(1)
        self.dSBoxSmallSPLGainBand_7.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_7.setMaximum(65535.500000000000000)

        self.horizontalLayout_109.addWidget(self.dSBoxSmallSPLGainBand_7)


        self.gridLayout_19.addLayout(self.horizontalLayout_109, 0, 1, 1, 1)

        self.horizontalLayout_110 = QHBoxLayout()
        self.horizontalLayout_110.setObjectName(u"horizontalLayout_110")
        self.label_97 = QLabel(self.groupBox_15)
        self.label_97.setObjectName(u"label_97")
        sizePolicy1.setHeightForWidth(self.label_97.sizePolicy().hasHeightForWidth())
        self.label_97.setSizePolicy(sizePolicy1)
        self.label_97.setMinimumSize(QSize(30, 0))
        self.label_97.setMaximumSize(QSize(16777215, 16777215))
        self.label_97.setFont(font1)

        self.horizontalLayout_110.addWidget(self.label_97)

        self.dSBoxMediumSPLBand_7 = QDoubleSpinBox(self.groupBox_15)
        self.dSBoxMediumSPLBand_7.setObjectName(u"dSBoxMediumSPLBand_7")
        self.dSBoxMediumSPLBand_7.setDecimals(1)
        self.dSBoxMediumSPLBand_7.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_7.setMaximum(65535.500000000000000)

        self.horizontalLayout_110.addWidget(self.dSBoxMediumSPLBand_7)


        self.gridLayout_19.addLayout(self.horizontalLayout_110, 1, 0, 1, 1)

        self.horizontalLayout_111 = QHBoxLayout()
        self.horizontalLayout_111.setObjectName(u"horizontalLayout_111")
        self.label_98 = QLabel(self.groupBox_15)
        self.label_98.setObjectName(u"label_98")
        sizePolicy1.setHeightForWidth(self.label_98.sizePolicy().hasHeightForWidth())
        self.label_98.setSizePolicy(sizePolicy1)
        self.label_98.setMinimumSize(QSize(0, 0))
        self.label_98.setMaximumSize(QSize(16777215, 16777215))
        self.label_98.setFont(font1)

        self.horizontalLayout_111.addWidget(self.label_98)

        self.dSBoxMediumSPLGainBand_7 = QDoubleSpinBox(self.groupBox_15)
        self.dSBoxMediumSPLGainBand_7.setObjectName(u"dSBoxMediumSPLGainBand_7")
        self.dSBoxMediumSPLGainBand_7.setDecimals(1)
        self.dSBoxMediumSPLGainBand_7.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_7.setMaximum(65535.500000000000000)

        self.horizontalLayout_111.addWidget(self.dSBoxMediumSPLGainBand_7)


        self.gridLayout_19.addLayout(self.horizontalLayout_111, 1, 1, 1, 1)

        self.horizontalLayout_112 = QHBoxLayout()
        self.horizontalLayout_112.setObjectName(u"horizontalLayout_112")
        self.label_99 = QLabel(self.groupBox_15)
        self.label_99.setObjectName(u"label_99")
        sizePolicy1.setHeightForWidth(self.label_99.sizePolicy().hasHeightForWidth())
        self.label_99.setSizePolicy(sizePolicy1)
        self.label_99.setMinimumSize(QSize(30, 0))
        self.label_99.setMaximumSize(QSize(16777215, 16777215))
        self.label_99.setFont(font1)

        self.horizontalLayout_112.addWidget(self.label_99)

        self.dSBoxLargeSPLBand_7 = QDoubleSpinBox(self.groupBox_15)
        self.dSBoxLargeSPLBand_7.setObjectName(u"dSBoxLargeSPLBand_7")
        self.dSBoxLargeSPLBand_7.setDecimals(1)
        self.dSBoxLargeSPLBand_7.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_7.setMaximum(65535.500000000000000)

        self.horizontalLayout_112.addWidget(self.dSBoxLargeSPLBand_7)


        self.gridLayout_19.addLayout(self.horizontalLayout_112, 2, 0, 1, 1)

        self.horizontalLayout_113 = QHBoxLayout()
        self.horizontalLayout_113.setObjectName(u"horizontalLayout_113")
        self.label_100 = QLabel(self.groupBox_15)
        self.label_100.setObjectName(u"label_100")
        sizePolicy1.setHeightForWidth(self.label_100.sizePolicy().hasHeightForWidth())
        self.label_100.setSizePolicy(sizePolicy1)
        self.label_100.setMinimumSize(QSize(0, 0))
        self.label_100.setMaximumSize(QSize(16777215, 16777215))
        self.label_100.setFont(font1)

        self.horizontalLayout_113.addWidget(self.label_100)

        self.dSBoxLargeSPLGainBand_7 = QDoubleSpinBox(self.groupBox_15)
        self.dSBoxLargeSPLGainBand_7.setObjectName(u"dSBoxLargeSPLGainBand_7")
        self.dSBoxLargeSPLGainBand_7.setDecimals(1)
        self.dSBoxLargeSPLGainBand_7.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_7.setMaximum(65535.500000000000000)

        self.horizontalLayout_113.addWidget(self.dSBoxLargeSPLGainBand_7)


        self.gridLayout_19.addLayout(self.horizontalLayout_113, 2, 1, 1, 1)


        self.verticalLayout_11.addWidget(self.groupBox_15)


        self.gridLayout_15.addWidget(self.gpBand_7, 4, 1, 1, 1)

        self.gpBand_8 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_8.setObjectName(u"gpBand_8")
        sizePolicy8.setHeightForWidth(self.gpBand_8.sizePolicy().hasHeightForWidth())
        self.gpBand_8.setSizePolicy(sizePolicy8)
        self.gpBand_8.setMinimumSize(QSize(0, 0))
        self.gpBand_8.setFont(font1)
        self.verticalLayout_12 = QVBoxLayout(self.gpBand_8)
        self.verticalLayout_12.setObjectName(u"verticalLayout_12")
        self.horizontalLayout_114 = QHBoxLayout()
        self.horizontalLayout_114.setObjectName(u"horizontalLayout_114")
        self.horizontalLayout_114.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_115 = QHBoxLayout()
        self.horizontalLayout_115.setObjectName(u"horizontalLayout_115")
        self.label_101 = QLabel(self.gpBand_8)
        self.label_101.setObjectName(u"label_101")
        sizePolicy1.setHeightForWidth(self.label_101.sizePolicy().hasHeightForWidth())
        self.label_101.setSizePolicy(sizePolicy1)
        self.label_101.setMinimumSize(QSize(0, 0))
        self.label_101.setMaximumSize(QSize(16777215, 16777215))
        self.label_101.setFont(font1)

        self.horizontalLayout_115.addWidget(self.label_101)

        self.spinBoxAttackBand_8 = QSpinBox(self.gpBand_8)
        self.spinBoxAttackBand_8.setObjectName(u"spinBoxAttackBand_8")
        self.spinBoxAttackBand_8.setMaximum(65535)

        self.horizontalLayout_115.addWidget(self.spinBoxAttackBand_8)


        self.horizontalLayout_114.addLayout(self.horizontalLayout_115)

        self.horizontalLayout_116 = QHBoxLayout()
        self.horizontalLayout_116.setObjectName(u"horizontalLayout_116")
        self.label_102 = QLabel(self.gpBand_8)
        self.label_102.setObjectName(u"label_102")
        sizePolicy1.setHeightForWidth(self.label_102.sizePolicy().hasHeightForWidth())
        self.label_102.setSizePolicy(sizePolicy1)
        self.label_102.setMinimumSize(QSize(0, 0))
        self.label_102.setMaximumSize(QSize(16777215, 16777215))
        self.label_102.setFont(font1)

        self.horizontalLayout_116.addWidget(self.label_102)

        self.spinBoxReleaseBand_8 = QSpinBox(self.gpBand_8)
        self.spinBoxReleaseBand_8.setObjectName(u"spinBoxReleaseBand_8")
        self.spinBoxReleaseBand_8.setMaximum(65535)

        self.horizontalLayout_116.addWidget(self.spinBoxReleaseBand_8)


        self.horizontalLayout_114.addLayout(self.horizontalLayout_116)


        self.verticalLayout_12.addLayout(self.horizontalLayout_114)

        self.gpLimitBand_8 = QGroupBox(self.gpBand_8)
        self.gpLimitBand_8.setObjectName(u"gpLimitBand_8")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_8.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_8.setSizePolicy(sizePolicy6)
        self.gpLimitBand_8.setFont(font1)
        self.gridLayout_20 = QGridLayout(self.gpLimitBand_8)
        self.gridLayout_20.setObjectName(u"gridLayout_20")
        self.gridLayout_20.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_117 = QHBoxLayout()
        self.horizontalLayout_117.setObjectName(u"horizontalLayout_117")
        self.label_103 = QLabel(self.gpLimitBand_8)
        self.label_103.setObjectName(u"label_103")
        sizePolicy1.setHeightForWidth(self.label_103.sizePolicy().hasHeightForWidth())
        self.label_103.setSizePolicy(sizePolicy1)
        self.label_103.setMinimumSize(QSize(0, 0))
        self.label_103.setMaximumSize(QSize(16777215, 16777215))
        self.label_103.setFont(font1)

        self.horizontalLayout_117.addWidget(self.label_103)

        self.dSBoxLimitThresholdBand_8 = QDoubleSpinBox(self.gpLimitBand_8)
        self.dSBoxLimitThresholdBand_8.setObjectName(u"dSBoxLimitThresholdBand_8")
        self.dSBoxLimitThresholdBand_8.setDecimals(1)
        self.dSBoxLimitThresholdBand_8.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_8.setMaximum(65535.500000000000000)

        self.horizontalLayout_117.addWidget(self.dSBoxLimitThresholdBand_8)


        self.gridLayout_20.addLayout(self.horizontalLayout_117, 0, 0, 1, 1)

        self.horizontalLayout_118 = QHBoxLayout()
        self.horizontalLayout_118.setObjectName(u"horizontalLayout_118")
        self.label_104 = QLabel(self.gpLimitBand_8)
        self.label_104.setObjectName(u"label_104")
        sizePolicy1.setHeightForWidth(self.label_104.sizePolicy().hasHeightForWidth())
        self.label_104.setSizePolicy(sizePolicy1)
        self.label_104.setMinimumSize(QSize(0, 0))
        self.label_104.setMaximumSize(QSize(16777215, 16777215))
        self.label_104.setFont(font1)

        self.horizontalLayout_118.addWidget(self.label_104)

        self.dSBoxLimitKneeBand_8 = QDoubleSpinBox(self.gpLimitBand_8)
        self.dSBoxLimitKneeBand_8.setObjectName(u"dSBoxLimitKneeBand_8")
        self.dSBoxLimitKneeBand_8.setDecimals(1)
        self.dSBoxLimitKneeBand_8.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_8.setMaximum(30.000000000000000)

        self.horizontalLayout_118.addWidget(self.dSBoxLimitKneeBand_8)


        self.gridLayout_20.addLayout(self.horizontalLayout_118, 0, 1, 1, 1)


        self.verticalLayout_12.addWidget(self.gpLimitBand_8)

        self.groupBox_16 = QGroupBox(self.gpBand_8)
        self.groupBox_16.setObjectName(u"groupBox_16")
        sizePolicy6.setHeightForWidth(self.groupBox_16.sizePolicy().hasHeightForWidth())
        self.groupBox_16.setSizePolicy(sizePolicy6)
        self.gridLayout_21 = QGridLayout(self.groupBox_16)
        self.gridLayout_21.setObjectName(u"gridLayout_21")
        self.gridLayout_21.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_119 = QHBoxLayout()
        self.horizontalLayout_119.setObjectName(u"horizontalLayout_119")
        self.label_105 = QLabel(self.groupBox_16)
        self.label_105.setObjectName(u"label_105")
        sizePolicy1.setHeightForWidth(self.label_105.sizePolicy().hasHeightForWidth())
        self.label_105.setSizePolicy(sizePolicy1)
        self.label_105.setMinimumSize(QSize(30, 0))
        self.label_105.setMaximumSize(QSize(16777215, 16777215))
        self.label_105.setFont(font1)

        self.horizontalLayout_119.addWidget(self.label_105)

        self.dSBoxSmallSPLBand_8 = QDoubleSpinBox(self.groupBox_16)
        self.dSBoxSmallSPLBand_8.setObjectName(u"dSBoxSmallSPLBand_8")
        self.dSBoxSmallSPLBand_8.setDecimals(1)
        self.dSBoxSmallSPLBand_8.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_8.setMaximum(65535.500000000000000)

        self.horizontalLayout_119.addWidget(self.dSBoxSmallSPLBand_8)


        self.gridLayout_21.addLayout(self.horizontalLayout_119, 0, 0, 1, 1)

        self.horizontalLayout_120 = QHBoxLayout()
        self.horizontalLayout_120.setObjectName(u"horizontalLayout_120")
        self.label_106 = QLabel(self.groupBox_16)
        self.label_106.setObjectName(u"label_106")
        sizePolicy1.setHeightForWidth(self.label_106.sizePolicy().hasHeightForWidth())
        self.label_106.setSizePolicy(sizePolicy1)
        self.label_106.setMinimumSize(QSize(0, 0))
        self.label_106.setMaximumSize(QSize(16777215, 16777215))
        self.label_106.setFont(font1)

        self.horizontalLayout_120.addWidget(self.label_106)

        self.dSBoxSmallSPLGainBand_8 = QDoubleSpinBox(self.groupBox_16)
        self.dSBoxSmallSPLGainBand_8.setObjectName(u"dSBoxSmallSPLGainBand_8")
        self.dSBoxSmallSPLGainBand_8.setDecimals(1)
        self.dSBoxSmallSPLGainBand_8.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_8.setMaximum(65535.500000000000000)

        self.horizontalLayout_120.addWidget(self.dSBoxSmallSPLGainBand_8)


        self.gridLayout_21.addLayout(self.horizontalLayout_120, 0, 1, 1, 1)

        self.horizontalLayout_121 = QHBoxLayout()
        self.horizontalLayout_121.setObjectName(u"horizontalLayout_121")
        self.label_107 = QLabel(self.groupBox_16)
        self.label_107.setObjectName(u"label_107")
        sizePolicy1.setHeightForWidth(self.label_107.sizePolicy().hasHeightForWidth())
        self.label_107.setSizePolicy(sizePolicy1)
        self.label_107.setMinimumSize(QSize(30, 0))
        self.label_107.setMaximumSize(QSize(16777215, 16777215))
        self.label_107.setFont(font1)

        self.horizontalLayout_121.addWidget(self.label_107)

        self.dSBoxMediumSPLBand_8 = QDoubleSpinBox(self.groupBox_16)
        self.dSBoxMediumSPLBand_8.setObjectName(u"dSBoxMediumSPLBand_8")
        self.dSBoxMediumSPLBand_8.setDecimals(1)
        self.dSBoxMediumSPLBand_8.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_8.setMaximum(65535.500000000000000)

        self.horizontalLayout_121.addWidget(self.dSBoxMediumSPLBand_8)


        self.gridLayout_21.addLayout(self.horizontalLayout_121, 1, 0, 1, 1)

        self.horizontalLayout_122 = QHBoxLayout()
        self.horizontalLayout_122.setObjectName(u"horizontalLayout_122")
        self.label_108 = QLabel(self.groupBox_16)
        self.label_108.setObjectName(u"label_108")
        sizePolicy1.setHeightForWidth(self.label_108.sizePolicy().hasHeightForWidth())
        self.label_108.setSizePolicy(sizePolicy1)
        self.label_108.setMinimumSize(QSize(0, 0))
        self.label_108.setMaximumSize(QSize(16777215, 16777215))
        self.label_108.setFont(font1)

        self.horizontalLayout_122.addWidget(self.label_108)

        self.dSBoxMediumSPLGainBand_8 = QDoubleSpinBox(self.groupBox_16)
        self.dSBoxMediumSPLGainBand_8.setObjectName(u"dSBoxMediumSPLGainBand_8")
        self.dSBoxMediumSPLGainBand_8.setDecimals(1)
        self.dSBoxMediumSPLGainBand_8.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_8.setMaximum(65535.500000000000000)

        self.horizontalLayout_122.addWidget(self.dSBoxMediumSPLGainBand_8)


        self.gridLayout_21.addLayout(self.horizontalLayout_122, 1, 1, 1, 1)

        self.horizontalLayout_123 = QHBoxLayout()
        self.horizontalLayout_123.setObjectName(u"horizontalLayout_123")
        self.label_109 = QLabel(self.groupBox_16)
        self.label_109.setObjectName(u"label_109")
        sizePolicy1.setHeightForWidth(self.label_109.sizePolicy().hasHeightForWidth())
        self.label_109.setSizePolicy(sizePolicy1)
        self.label_109.setMinimumSize(QSize(30, 0))
        self.label_109.setMaximumSize(QSize(16777215, 16777215))
        self.label_109.setFont(font1)

        self.horizontalLayout_123.addWidget(self.label_109)

        self.dSBoxLargeSPLBand_8 = QDoubleSpinBox(self.groupBox_16)
        self.dSBoxLargeSPLBand_8.setObjectName(u"dSBoxLargeSPLBand_8")
        self.dSBoxLargeSPLBand_8.setDecimals(1)
        self.dSBoxLargeSPLBand_8.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_8.setMaximum(65535.500000000000000)

        self.horizontalLayout_123.addWidget(self.dSBoxLargeSPLBand_8)


        self.gridLayout_21.addLayout(self.horizontalLayout_123, 2, 0, 1, 1)

        self.horizontalLayout_124 = QHBoxLayout()
        self.horizontalLayout_124.setObjectName(u"horizontalLayout_124")
        self.label_110 = QLabel(self.groupBox_16)
        self.label_110.setObjectName(u"label_110")
        sizePolicy1.setHeightForWidth(self.label_110.sizePolicy().hasHeightForWidth())
        self.label_110.setSizePolicy(sizePolicy1)
        self.label_110.setMinimumSize(QSize(0, 0))
        self.label_110.setMaximumSize(QSize(16777215, 16777215))
        self.label_110.setFont(font1)

        self.horizontalLayout_124.addWidget(self.label_110)

        self.dSBoxLargeSPLGainBand_8 = QDoubleSpinBox(self.groupBox_16)
        self.dSBoxLargeSPLGainBand_8.setObjectName(u"dSBoxLargeSPLGainBand_8")
        self.dSBoxLargeSPLGainBand_8.setDecimals(1)
        self.dSBoxLargeSPLGainBand_8.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_8.setMaximum(65535.500000000000000)

        self.horizontalLayout_124.addWidget(self.dSBoxLargeSPLGainBand_8)


        self.gridLayout_21.addLayout(self.horizontalLayout_124, 2, 1, 1, 1)


        self.verticalLayout_12.addWidget(self.groupBox_16)


        self.gridLayout_15.addWidget(self.gpBand_8, 5, 0, 1, 1)

        self.gpBand_4 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_4.setObjectName(u"gpBand_4")
        sizePolicy8.setHeightForWidth(self.gpBand_4.sizePolicy().hasHeightForWidth())
        self.gpBand_4.setSizePolicy(sizePolicy8)
        self.gpBand_4.setMinimumSize(QSize(0, 0))
        self.gpBand_4.setFont(font1)
        self.verticalLayout_8 = QVBoxLayout(self.gpBand_4)
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.horizontalLayout_66 = QHBoxLayout()
        self.horizontalLayout_66.setObjectName(u"horizontalLayout_66")
        self.horizontalLayout_66.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_67 = QHBoxLayout()
        self.horizontalLayout_67.setObjectName(u"horizontalLayout_67")
        self.label_57 = QLabel(self.gpBand_4)
        self.label_57.setObjectName(u"label_57")
        sizePolicy1.setHeightForWidth(self.label_57.sizePolicy().hasHeightForWidth())
        self.label_57.setSizePolicy(sizePolicy1)
        self.label_57.setMinimumSize(QSize(0, 0))
        self.label_57.setMaximumSize(QSize(16777215, 16777215))
        self.label_57.setFont(font1)

        self.horizontalLayout_67.addWidget(self.label_57)

        self.spinBoxAttackBand_4 = QSpinBox(self.gpBand_4)
        self.spinBoxAttackBand_4.setObjectName(u"spinBoxAttackBand_4")
        self.spinBoxAttackBand_4.setMaximum(65535)

        self.horizontalLayout_67.addWidget(self.spinBoxAttackBand_4)


        self.horizontalLayout_66.addLayout(self.horizontalLayout_67)

        self.horizontalLayout_68 = QHBoxLayout()
        self.horizontalLayout_68.setObjectName(u"horizontalLayout_68")
        self.label_58 = QLabel(self.gpBand_4)
        self.label_58.setObjectName(u"label_58")
        sizePolicy1.setHeightForWidth(self.label_58.sizePolicy().hasHeightForWidth())
        self.label_58.setSizePolicy(sizePolicy1)
        self.label_58.setMinimumSize(QSize(0, 0))
        self.label_58.setMaximumSize(QSize(16777215, 16777215))
        self.label_58.setFont(font1)

        self.horizontalLayout_68.addWidget(self.label_58)

        self.spinBoxReleaseBand_4 = QSpinBox(self.gpBand_4)
        self.spinBoxReleaseBand_4.setObjectName(u"spinBoxReleaseBand_4")
        self.spinBoxReleaseBand_4.setMaximum(65535)

        self.horizontalLayout_68.addWidget(self.spinBoxReleaseBand_4)


        self.horizontalLayout_66.addLayout(self.horizontalLayout_68)


        self.verticalLayout_8.addLayout(self.horizontalLayout_66)

        self.gpLimitBand_4 = QGroupBox(self.gpBand_4)
        self.gpLimitBand_4.setObjectName(u"gpLimitBand_4")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_4.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_4.setSizePolicy(sizePolicy6)
        self.gpLimitBand_4.setFont(font1)
        self.gridLayout_10 = QGridLayout(self.gpLimitBand_4)
        self.gridLayout_10.setObjectName(u"gridLayout_10")
        self.gridLayout_10.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_69 = QHBoxLayout()
        self.horizontalLayout_69.setObjectName(u"horizontalLayout_69")
        self.label_60 = QLabel(self.gpLimitBand_4)
        self.label_60.setObjectName(u"label_60")
        sizePolicy1.setHeightForWidth(self.label_60.sizePolicy().hasHeightForWidth())
        self.label_60.setSizePolicy(sizePolicy1)
        self.label_60.setMinimumSize(QSize(0, 0))
        self.label_60.setMaximumSize(QSize(16777215, 16777215))
        self.label_60.setFont(font1)

        self.horizontalLayout_69.addWidget(self.label_60)

        self.dSBoxLimitThresholdBand_4 = QDoubleSpinBox(self.gpLimitBand_4)
        self.dSBoxLimitThresholdBand_4.setObjectName(u"dSBoxLimitThresholdBand_4")
        self.dSBoxLimitThresholdBand_4.setDecimals(1)
        self.dSBoxLimitThresholdBand_4.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_4.setMaximum(65535.500000000000000)

        self.horizontalLayout_69.addWidget(self.dSBoxLimitThresholdBand_4)


        self.gridLayout_10.addLayout(self.horizontalLayout_69, 0, 0, 1, 1)

        self.horizontalLayout_70 = QHBoxLayout()
        self.horizontalLayout_70.setObjectName(u"horizontalLayout_70")
        self.label_61 = QLabel(self.gpLimitBand_4)
        self.label_61.setObjectName(u"label_61")
        sizePolicy1.setHeightForWidth(self.label_61.sizePolicy().hasHeightForWidth())
        self.label_61.setSizePolicy(sizePolicy1)
        self.label_61.setMinimumSize(QSize(0, 0))
        self.label_61.setMaximumSize(QSize(16777215, 16777215))
        self.label_61.setFont(font1)

        self.horizontalLayout_70.addWidget(self.label_61)

        self.dSBoxLimitKneeBand_4 = QDoubleSpinBox(self.gpLimitBand_4)
        self.dSBoxLimitKneeBand_4.setObjectName(u"dSBoxLimitKneeBand_4")
        self.dSBoxLimitKneeBand_4.setDecimals(1)
        self.dSBoxLimitKneeBand_4.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_4.setMaximum(30.000000000000000)

        self.horizontalLayout_70.addWidget(self.dSBoxLimitKneeBand_4)


        self.gridLayout_10.addLayout(self.horizontalLayout_70, 0, 1, 1, 1)


        self.verticalLayout_8.addWidget(self.gpLimitBand_4)

        self.groupBox_12 = QGroupBox(self.gpBand_4)
        self.groupBox_12.setObjectName(u"groupBox_12")
        sizePolicy6.setHeightForWidth(self.groupBox_12.sizePolicy().hasHeightForWidth())
        self.groupBox_12.setSizePolicy(sizePolicy6)
        self.gridLayout_12 = QGridLayout(self.groupBox_12)
        self.gridLayout_12.setObjectName(u"gridLayout_12")
        self.gridLayout_12.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_71 = QHBoxLayout()
        self.horizontalLayout_71.setObjectName(u"horizontalLayout_71")
        self.label_62 = QLabel(self.groupBox_12)
        self.label_62.setObjectName(u"label_62")
        sizePolicy1.setHeightForWidth(self.label_62.sizePolicy().hasHeightForWidth())
        self.label_62.setSizePolicy(sizePolicy1)
        self.label_62.setMinimumSize(QSize(30, 0))
        self.label_62.setMaximumSize(QSize(16777215, 16777215))
        self.label_62.setFont(font1)

        self.horizontalLayout_71.addWidget(self.label_62)

        self.dSBoxSmallSPLBand_4 = QDoubleSpinBox(self.groupBox_12)
        self.dSBoxSmallSPLBand_4.setObjectName(u"dSBoxSmallSPLBand_4")
        self.dSBoxSmallSPLBand_4.setDecimals(1)
        self.dSBoxSmallSPLBand_4.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_4.setMaximum(65535.500000000000000)

        self.horizontalLayout_71.addWidget(self.dSBoxSmallSPLBand_4)


        self.gridLayout_12.addLayout(self.horizontalLayout_71, 0, 0, 1, 1)

        self.horizontalLayout_72 = QHBoxLayout()
        self.horizontalLayout_72.setObjectName(u"horizontalLayout_72")
        self.label_63 = QLabel(self.groupBox_12)
        self.label_63.setObjectName(u"label_63")
        sizePolicy1.setHeightForWidth(self.label_63.sizePolicy().hasHeightForWidth())
        self.label_63.setSizePolicy(sizePolicy1)
        self.label_63.setMinimumSize(QSize(0, 0))
        self.label_63.setMaximumSize(QSize(16777215, 16777215))
        self.label_63.setFont(font1)

        self.horizontalLayout_72.addWidget(self.label_63)

        self.dSBoxSmallSPLGainBand_4 = QDoubleSpinBox(self.groupBox_12)
        self.dSBoxSmallSPLGainBand_4.setObjectName(u"dSBoxSmallSPLGainBand_4")
        self.dSBoxSmallSPLGainBand_4.setDecimals(1)
        self.dSBoxSmallSPLGainBand_4.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_4.setMaximum(65535.500000000000000)

        self.horizontalLayout_72.addWidget(self.dSBoxSmallSPLGainBand_4)


        self.gridLayout_12.addLayout(self.horizontalLayout_72, 0, 1, 1, 1)

        self.horizontalLayout_73 = QHBoxLayout()
        self.horizontalLayout_73.setObjectName(u"horizontalLayout_73")
        self.label_64 = QLabel(self.groupBox_12)
        self.label_64.setObjectName(u"label_64")
        sizePolicy1.setHeightForWidth(self.label_64.sizePolicy().hasHeightForWidth())
        self.label_64.setSizePolicy(sizePolicy1)
        self.label_64.setMinimumSize(QSize(30, 0))
        self.label_64.setMaximumSize(QSize(16777215, 16777215))
        self.label_64.setFont(font1)

        self.horizontalLayout_73.addWidget(self.label_64)

        self.dSBoxMediumSPLBand_4 = QDoubleSpinBox(self.groupBox_12)
        self.dSBoxMediumSPLBand_4.setObjectName(u"dSBoxMediumSPLBand_4")
        self.dSBoxMediumSPLBand_4.setDecimals(1)
        self.dSBoxMediumSPLBand_4.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_4.setMaximum(65535.500000000000000)

        self.horizontalLayout_73.addWidget(self.dSBoxMediumSPLBand_4)


        self.gridLayout_12.addLayout(self.horizontalLayout_73, 1, 0, 1, 1)

        self.horizontalLayout_74 = QHBoxLayout()
        self.horizontalLayout_74.setObjectName(u"horizontalLayout_74")
        self.label_65 = QLabel(self.groupBox_12)
        self.label_65.setObjectName(u"label_65")
        sizePolicy1.setHeightForWidth(self.label_65.sizePolicy().hasHeightForWidth())
        self.label_65.setSizePolicy(sizePolicy1)
        self.label_65.setMinimumSize(QSize(0, 0))
        self.label_65.setMaximumSize(QSize(16777215, 16777215))
        self.label_65.setFont(font1)

        self.horizontalLayout_74.addWidget(self.label_65)

        self.dSBoxMediumSPLGainBand_4 = QDoubleSpinBox(self.groupBox_12)
        self.dSBoxMediumSPLGainBand_4.setObjectName(u"dSBoxMediumSPLGainBand_4")
        self.dSBoxMediumSPLGainBand_4.setDecimals(1)
        self.dSBoxMediumSPLGainBand_4.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_4.setMaximum(65535.500000000000000)

        self.horizontalLayout_74.addWidget(self.dSBoxMediumSPLGainBand_4)


        self.gridLayout_12.addLayout(self.horizontalLayout_74, 1, 1, 1, 1)

        self.horizontalLayout_75 = QHBoxLayout()
        self.horizontalLayout_75.setObjectName(u"horizontalLayout_75")
        self.label_66 = QLabel(self.groupBox_12)
        self.label_66.setObjectName(u"label_66")
        sizePolicy1.setHeightForWidth(self.label_66.sizePolicy().hasHeightForWidth())
        self.label_66.setSizePolicy(sizePolicy1)
        self.label_66.setMinimumSize(QSize(30, 0))
        self.label_66.setMaximumSize(QSize(16777215, 16777215))
        self.label_66.setFont(font1)

        self.horizontalLayout_75.addWidget(self.label_66)

        self.dSBoxLargeSPLBand_4 = QDoubleSpinBox(self.groupBox_12)
        self.dSBoxLargeSPLBand_4.setObjectName(u"dSBoxLargeSPLBand_4")
        self.dSBoxLargeSPLBand_4.setDecimals(1)
        self.dSBoxLargeSPLBand_4.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_4.setMaximum(65535.500000000000000)

        self.horizontalLayout_75.addWidget(self.dSBoxLargeSPLBand_4)


        self.gridLayout_12.addLayout(self.horizontalLayout_75, 2, 0, 1, 1)

        self.horizontalLayout_76 = QHBoxLayout()
        self.horizontalLayout_76.setObjectName(u"horizontalLayout_76")
        self.label_67 = QLabel(self.groupBox_12)
        self.label_67.setObjectName(u"label_67")
        sizePolicy1.setHeightForWidth(self.label_67.sizePolicy().hasHeightForWidth())
        self.label_67.setSizePolicy(sizePolicy1)
        self.label_67.setMinimumSize(QSize(0, 0))
        self.label_67.setMaximumSize(QSize(16777215, 16777215))
        self.label_67.setFont(font1)

        self.horizontalLayout_76.addWidget(self.label_67)

        self.dSBoxLargeSPLGainBand_4 = QDoubleSpinBox(self.groupBox_12)
        self.dSBoxLargeSPLGainBand_4.setObjectName(u"dSBoxLargeSPLGainBand_4")
        self.dSBoxLargeSPLGainBand_4.setDecimals(1)
        self.dSBoxLargeSPLGainBand_4.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_4.setMaximum(65535.500000000000000)

        self.horizontalLayout_76.addWidget(self.dSBoxLargeSPLGainBand_4)


        self.gridLayout_12.addLayout(self.horizontalLayout_76, 2, 1, 1, 1)


        self.verticalLayout_8.addWidget(self.groupBox_12)


        self.gridLayout_15.addWidget(self.gpBand_4, 2, 0, 1, 1)

        self.gpBand_10 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_10.setObjectName(u"gpBand_10")
        sizePolicy8.setHeightForWidth(self.gpBand_10.sizePolicy().hasHeightForWidth())
        self.gpBand_10.setSizePolicy(sizePolicy8)
        self.gpBand_10.setMinimumSize(QSize(0, 0))
        self.gpBand_10.setFont(font1)
        self.verticalLayout_14 = QVBoxLayout(self.gpBand_10)
        self.verticalLayout_14.setObjectName(u"verticalLayout_14")
        self.horizontalLayout_136 = QHBoxLayout()
        self.horizontalLayout_136.setObjectName(u"horizontalLayout_136")
        self.horizontalLayout_136.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_137 = QHBoxLayout()
        self.horizontalLayout_137.setObjectName(u"horizontalLayout_137")
        self.label_121 = QLabel(self.gpBand_10)
        self.label_121.setObjectName(u"label_121")
        sizePolicy1.setHeightForWidth(self.label_121.sizePolicy().hasHeightForWidth())
        self.label_121.setSizePolicy(sizePolicy1)
        self.label_121.setMinimumSize(QSize(0, 0))
        self.label_121.setMaximumSize(QSize(16777215, 16777215))
        self.label_121.setFont(font1)

        self.horizontalLayout_137.addWidget(self.label_121)

        self.spinBoxAttackBand_10 = QSpinBox(self.gpBand_10)
        self.spinBoxAttackBand_10.setObjectName(u"spinBoxAttackBand_10")
        self.spinBoxAttackBand_10.setMaximum(65535)

        self.horizontalLayout_137.addWidget(self.spinBoxAttackBand_10)


        self.horizontalLayout_136.addLayout(self.horizontalLayout_137)

        self.horizontalLayout_138 = QHBoxLayout()
        self.horizontalLayout_138.setObjectName(u"horizontalLayout_138")
        self.label_122 = QLabel(self.gpBand_10)
        self.label_122.setObjectName(u"label_122")
        sizePolicy1.setHeightForWidth(self.label_122.sizePolicy().hasHeightForWidth())
        self.label_122.setSizePolicy(sizePolicy1)
        self.label_122.setMinimumSize(QSize(0, 0))
        self.label_122.setMaximumSize(QSize(16777215, 16777215))
        self.label_122.setFont(font1)

        self.horizontalLayout_138.addWidget(self.label_122)

        self.spinBoxReleaseBand_10 = QSpinBox(self.gpBand_10)
        self.spinBoxReleaseBand_10.setObjectName(u"spinBoxReleaseBand_10")
        self.spinBoxReleaseBand_10.setMaximum(65535)

        self.horizontalLayout_138.addWidget(self.spinBoxReleaseBand_10)


        self.horizontalLayout_136.addLayout(self.horizontalLayout_138)


        self.verticalLayout_14.addLayout(self.horizontalLayout_136)

        self.gpLimitBand_10 = QGroupBox(self.gpBand_10)
        self.gpLimitBand_10.setObjectName(u"gpLimitBand_10")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_10.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_10.setSizePolicy(sizePolicy6)
        self.gpLimitBand_10.setFont(font1)
        self.gridLayout_24 = QGridLayout(self.gpLimitBand_10)
        self.gridLayout_24.setObjectName(u"gridLayout_24")
        self.gridLayout_24.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_139 = QHBoxLayout()
        self.horizontalLayout_139.setObjectName(u"horizontalLayout_139")
        self.label_123 = QLabel(self.gpLimitBand_10)
        self.label_123.setObjectName(u"label_123")
        sizePolicy1.setHeightForWidth(self.label_123.sizePolicy().hasHeightForWidth())
        self.label_123.setSizePolicy(sizePolicy1)
        self.label_123.setMinimumSize(QSize(0, 0))
        self.label_123.setMaximumSize(QSize(16777215, 16777215))
        self.label_123.setFont(font1)

        self.horizontalLayout_139.addWidget(self.label_123)

        self.dSBoxLimitThresholdBand_10 = QDoubleSpinBox(self.gpLimitBand_10)
        self.dSBoxLimitThresholdBand_10.setObjectName(u"dSBoxLimitThresholdBand_10")
        self.dSBoxLimitThresholdBand_10.setDecimals(1)
        self.dSBoxLimitThresholdBand_10.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_10.setMaximum(65535.500000000000000)

        self.horizontalLayout_139.addWidget(self.dSBoxLimitThresholdBand_10)


        self.gridLayout_24.addLayout(self.horizontalLayout_139, 0, 0, 1, 1)

        self.horizontalLayout_140 = QHBoxLayout()
        self.horizontalLayout_140.setObjectName(u"horizontalLayout_140")
        self.label_124 = QLabel(self.gpLimitBand_10)
        self.label_124.setObjectName(u"label_124")
        sizePolicy1.setHeightForWidth(self.label_124.sizePolicy().hasHeightForWidth())
        self.label_124.setSizePolicy(sizePolicy1)
        self.label_124.setMinimumSize(QSize(0, 0))
        self.label_124.setMaximumSize(QSize(16777215, 16777215))
        self.label_124.setFont(font1)

        self.horizontalLayout_140.addWidget(self.label_124)

        self.dSBoxLimitKneeBand_10 = QDoubleSpinBox(self.gpLimitBand_10)
        self.dSBoxLimitKneeBand_10.setObjectName(u"dSBoxLimitKneeBand_10")
        self.dSBoxLimitKneeBand_10.setDecimals(1)
        self.dSBoxLimitKneeBand_10.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_10.setMaximum(30.000000000000000)

        self.horizontalLayout_140.addWidget(self.dSBoxLimitKneeBand_10)


        self.gridLayout_24.addLayout(self.horizontalLayout_140, 0, 1, 1, 1)


        self.verticalLayout_14.addWidget(self.gpLimitBand_10)

        self.groupBox_18 = QGroupBox(self.gpBand_10)
        self.groupBox_18.setObjectName(u"groupBox_18")
        sizePolicy6.setHeightForWidth(self.groupBox_18.sizePolicy().hasHeightForWidth())
        self.groupBox_18.setSizePolicy(sizePolicy6)
        self.gridLayout_25 = QGridLayout(self.groupBox_18)
        self.gridLayout_25.setObjectName(u"gridLayout_25")
        self.gridLayout_25.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_141 = QHBoxLayout()
        self.horizontalLayout_141.setObjectName(u"horizontalLayout_141")
        self.label_125 = QLabel(self.groupBox_18)
        self.label_125.setObjectName(u"label_125")
        sizePolicy1.setHeightForWidth(self.label_125.sizePolicy().hasHeightForWidth())
        self.label_125.setSizePolicy(sizePolicy1)
        self.label_125.setMinimumSize(QSize(30, 0))
        self.label_125.setMaximumSize(QSize(16777215, 16777215))
        self.label_125.setFont(font1)

        self.horizontalLayout_141.addWidget(self.label_125)

        self.dSBoxSmallSPLBand_10 = QDoubleSpinBox(self.groupBox_18)
        self.dSBoxSmallSPLBand_10.setObjectName(u"dSBoxSmallSPLBand_10")
        self.dSBoxSmallSPLBand_10.setDecimals(1)
        self.dSBoxSmallSPLBand_10.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_10.setMaximum(65535.500000000000000)

        self.horizontalLayout_141.addWidget(self.dSBoxSmallSPLBand_10)


        self.gridLayout_25.addLayout(self.horizontalLayout_141, 0, 0, 1, 1)

        self.horizontalLayout_142 = QHBoxLayout()
        self.horizontalLayout_142.setObjectName(u"horizontalLayout_142")
        self.label_126 = QLabel(self.groupBox_18)
        self.label_126.setObjectName(u"label_126")
        sizePolicy1.setHeightForWidth(self.label_126.sizePolicy().hasHeightForWidth())
        self.label_126.setSizePolicy(sizePolicy1)
        self.label_126.setMinimumSize(QSize(0, 0))
        self.label_126.setMaximumSize(QSize(16777215, 16777215))
        self.label_126.setFont(font1)

        self.horizontalLayout_142.addWidget(self.label_126)

        self.dSBoxSmallSPLGainBand_10 = QDoubleSpinBox(self.groupBox_18)
        self.dSBoxSmallSPLGainBand_10.setObjectName(u"dSBoxSmallSPLGainBand_10")
        self.dSBoxSmallSPLGainBand_10.setDecimals(1)
        self.dSBoxSmallSPLGainBand_10.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_10.setMaximum(65535.500000000000000)

        self.horizontalLayout_142.addWidget(self.dSBoxSmallSPLGainBand_10)


        self.gridLayout_25.addLayout(self.horizontalLayout_142, 0, 1, 1, 1)

        self.horizontalLayout_143 = QHBoxLayout()
        self.horizontalLayout_143.setObjectName(u"horizontalLayout_143")
        self.label_127 = QLabel(self.groupBox_18)
        self.label_127.setObjectName(u"label_127")
        sizePolicy1.setHeightForWidth(self.label_127.sizePolicy().hasHeightForWidth())
        self.label_127.setSizePolicy(sizePolicy1)
        self.label_127.setMinimumSize(QSize(30, 0))
        self.label_127.setMaximumSize(QSize(16777215, 16777215))
        self.label_127.setFont(font1)

        self.horizontalLayout_143.addWidget(self.label_127)

        self.dSBoxMediumSPLBand_10 = QDoubleSpinBox(self.groupBox_18)
        self.dSBoxMediumSPLBand_10.setObjectName(u"dSBoxMediumSPLBand_10")
        self.dSBoxMediumSPLBand_10.setDecimals(1)
        self.dSBoxMediumSPLBand_10.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_10.setMaximum(65535.500000000000000)

        self.horizontalLayout_143.addWidget(self.dSBoxMediumSPLBand_10)


        self.gridLayout_25.addLayout(self.horizontalLayout_143, 1, 0, 1, 1)

        self.horizontalLayout_144 = QHBoxLayout()
        self.horizontalLayout_144.setObjectName(u"horizontalLayout_144")
        self.label_128 = QLabel(self.groupBox_18)
        self.label_128.setObjectName(u"label_128")
        sizePolicy1.setHeightForWidth(self.label_128.sizePolicy().hasHeightForWidth())
        self.label_128.setSizePolicy(sizePolicy1)
        self.label_128.setMinimumSize(QSize(0, 0))
        self.label_128.setMaximumSize(QSize(16777215, 16777215))
        self.label_128.setFont(font1)

        self.horizontalLayout_144.addWidget(self.label_128)

        self.dSBoxMediumSPLGainBand_10 = QDoubleSpinBox(self.groupBox_18)
        self.dSBoxMediumSPLGainBand_10.setObjectName(u"dSBoxMediumSPLGainBand_10")
        self.dSBoxMediumSPLGainBand_10.setDecimals(1)
        self.dSBoxMediumSPLGainBand_10.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_10.setMaximum(65535.500000000000000)

        self.horizontalLayout_144.addWidget(self.dSBoxMediumSPLGainBand_10)


        self.gridLayout_25.addLayout(self.horizontalLayout_144, 1, 1, 1, 1)

        self.horizontalLayout_145 = QHBoxLayout()
        self.horizontalLayout_145.setObjectName(u"horizontalLayout_145")
        self.label_129 = QLabel(self.groupBox_18)
        self.label_129.setObjectName(u"label_129")
        sizePolicy1.setHeightForWidth(self.label_129.sizePolicy().hasHeightForWidth())
        self.label_129.setSizePolicy(sizePolicy1)
        self.label_129.setMinimumSize(QSize(30, 0))
        self.label_129.setMaximumSize(QSize(16777215, 16777215))
        self.label_129.setFont(font1)

        self.horizontalLayout_145.addWidget(self.label_129)

        self.dSBoxLargeSPLBand_10 = QDoubleSpinBox(self.groupBox_18)
        self.dSBoxLargeSPLBand_10.setObjectName(u"dSBoxLargeSPLBand_10")
        self.dSBoxLargeSPLBand_10.setDecimals(1)
        self.dSBoxLargeSPLBand_10.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_10.setMaximum(65535.500000000000000)

        self.horizontalLayout_145.addWidget(self.dSBoxLargeSPLBand_10)


        self.gridLayout_25.addLayout(self.horizontalLayout_145, 2, 0, 1, 1)

        self.horizontalLayout_146 = QHBoxLayout()
        self.horizontalLayout_146.setObjectName(u"horizontalLayout_146")
        self.label_130 = QLabel(self.groupBox_18)
        self.label_130.setObjectName(u"label_130")
        sizePolicy1.setHeightForWidth(self.label_130.sizePolicy().hasHeightForWidth())
        self.label_130.setSizePolicy(sizePolicy1)
        self.label_130.setMinimumSize(QSize(0, 0))
        self.label_130.setMaximumSize(QSize(16777215, 16777215))
        self.label_130.setFont(font1)

        self.horizontalLayout_146.addWidget(self.label_130)

        self.dSBoxLargeSPLGainBand_10 = QDoubleSpinBox(self.groupBox_18)
        self.dSBoxLargeSPLGainBand_10.setObjectName(u"dSBoxLargeSPLGainBand_10")
        self.dSBoxLargeSPLGainBand_10.setDecimals(1)
        self.dSBoxLargeSPLGainBand_10.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_10.setMaximum(65535.500000000000000)

        self.horizontalLayout_146.addWidget(self.dSBoxLargeSPLGainBand_10)


        self.gridLayout_25.addLayout(self.horizontalLayout_146, 2, 1, 1, 1)


        self.verticalLayout_14.addWidget(self.groupBox_18)


        self.gridLayout_15.addWidget(self.gpBand_10, 6, 0, 1, 1)

        self.gpBand_16 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_16.setObjectName(u"gpBand_16")
        sizePolicy8.setHeightForWidth(self.gpBand_16.sizePolicy().hasHeightForWidth())
        self.gpBand_16.setSizePolicy(sizePolicy8)
        self.gpBand_16.setMinimumSize(QSize(0, 0))
        self.gpBand_16.setFont(font1)
        self.verticalLayout_20 = QVBoxLayout(self.gpBand_16)
        self.verticalLayout_20.setObjectName(u"verticalLayout_20")
        self.horizontalLayout_202 = QHBoxLayout()
        self.horizontalLayout_202.setObjectName(u"horizontalLayout_202")
        self.horizontalLayout_202.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_203 = QHBoxLayout()
        self.horizontalLayout_203.setObjectName(u"horizontalLayout_203")
        self.label_181 = QLabel(self.gpBand_16)
        self.label_181.setObjectName(u"label_181")
        sizePolicy1.setHeightForWidth(self.label_181.sizePolicy().hasHeightForWidth())
        self.label_181.setSizePolicy(sizePolicy1)
        self.label_181.setMinimumSize(QSize(0, 0))
        self.label_181.setMaximumSize(QSize(16777215, 16777215))
        self.label_181.setFont(font1)

        self.horizontalLayout_203.addWidget(self.label_181)

        self.spinBoxAttackBand_16 = QSpinBox(self.gpBand_16)
        self.spinBoxAttackBand_16.setObjectName(u"spinBoxAttackBand_16")
        self.spinBoxAttackBand_16.setMaximum(65535)

        self.horizontalLayout_203.addWidget(self.spinBoxAttackBand_16)


        self.horizontalLayout_202.addLayout(self.horizontalLayout_203)

        self.horizontalLayout_204 = QHBoxLayout()
        self.horizontalLayout_204.setObjectName(u"horizontalLayout_204")
        self.label_182 = QLabel(self.gpBand_16)
        self.label_182.setObjectName(u"label_182")
        sizePolicy1.setHeightForWidth(self.label_182.sizePolicy().hasHeightForWidth())
        self.label_182.setSizePolicy(sizePolicy1)
        self.label_182.setMinimumSize(QSize(0, 0))
        self.label_182.setMaximumSize(QSize(16777215, 16777215))
        self.label_182.setFont(font1)

        self.horizontalLayout_204.addWidget(self.label_182)

        self.spinBoxReleaseBand_16 = QSpinBox(self.gpBand_16)
        self.spinBoxReleaseBand_16.setObjectName(u"spinBoxReleaseBand_16")
        self.spinBoxReleaseBand_16.setMaximum(65535)

        self.horizontalLayout_204.addWidget(self.spinBoxReleaseBand_16)


        self.horizontalLayout_202.addLayout(self.horizontalLayout_204)


        self.verticalLayout_20.addLayout(self.horizontalLayout_202)

        self.gpLimitBand_16 = QGroupBox(self.gpBand_16)
        self.gpLimitBand_16.setObjectName(u"gpLimitBand_16")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_16.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_16.setSizePolicy(sizePolicy6)
        self.gpLimitBand_16.setFont(font1)
        self.gridLayout_36 = QGridLayout(self.gpLimitBand_16)
        self.gridLayout_36.setObjectName(u"gridLayout_36")
        self.gridLayout_36.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_205 = QHBoxLayout()
        self.horizontalLayout_205.setObjectName(u"horizontalLayout_205")
        self.label_183 = QLabel(self.gpLimitBand_16)
        self.label_183.setObjectName(u"label_183")
        sizePolicy1.setHeightForWidth(self.label_183.sizePolicy().hasHeightForWidth())
        self.label_183.setSizePolicy(sizePolicy1)
        self.label_183.setMinimumSize(QSize(0, 0))
        self.label_183.setMaximumSize(QSize(16777215, 16777215))
        self.label_183.setFont(font1)

        self.horizontalLayout_205.addWidget(self.label_183)

        self.dSBoxLimitThresholdBand_16 = QDoubleSpinBox(self.gpLimitBand_16)
        self.dSBoxLimitThresholdBand_16.setObjectName(u"dSBoxLimitThresholdBand_16")
        self.dSBoxLimitThresholdBand_16.setDecimals(1)
        self.dSBoxLimitThresholdBand_16.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_16.setMaximum(65535.500000000000000)

        self.horizontalLayout_205.addWidget(self.dSBoxLimitThresholdBand_16)


        self.gridLayout_36.addLayout(self.horizontalLayout_205, 0, 0, 1, 1)

        self.horizontalLayout_206 = QHBoxLayout()
        self.horizontalLayout_206.setObjectName(u"horizontalLayout_206")
        self.label_184 = QLabel(self.gpLimitBand_16)
        self.label_184.setObjectName(u"label_184")
        sizePolicy1.setHeightForWidth(self.label_184.sizePolicy().hasHeightForWidth())
        self.label_184.setSizePolicy(sizePolicy1)
        self.label_184.setMinimumSize(QSize(0, 0))
        self.label_184.setMaximumSize(QSize(16777215, 16777215))
        self.label_184.setFont(font1)

        self.horizontalLayout_206.addWidget(self.label_184)

        self.dSBoxLimitKneeBand_16 = QDoubleSpinBox(self.gpLimitBand_16)
        self.dSBoxLimitKneeBand_16.setObjectName(u"dSBoxLimitKneeBand_16")
        self.dSBoxLimitKneeBand_16.setDecimals(1)
        self.dSBoxLimitKneeBand_16.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_16.setMaximum(30.000000000000000)

        self.horizontalLayout_206.addWidget(self.dSBoxLimitKneeBand_16)


        self.gridLayout_36.addLayout(self.horizontalLayout_206, 0, 1, 1, 1)


        self.verticalLayout_20.addWidget(self.gpLimitBand_16)

        self.groupBox_24 = QGroupBox(self.gpBand_16)
        self.groupBox_24.setObjectName(u"groupBox_24")
        sizePolicy6.setHeightForWidth(self.groupBox_24.sizePolicy().hasHeightForWidth())
        self.groupBox_24.setSizePolicy(sizePolicy6)
        self.gridLayout_37 = QGridLayout(self.groupBox_24)
        self.gridLayout_37.setObjectName(u"gridLayout_37")
        self.gridLayout_37.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_207 = QHBoxLayout()
        self.horizontalLayout_207.setObjectName(u"horizontalLayout_207")
        self.label_185 = QLabel(self.groupBox_24)
        self.label_185.setObjectName(u"label_185")
        sizePolicy1.setHeightForWidth(self.label_185.sizePolicy().hasHeightForWidth())
        self.label_185.setSizePolicy(sizePolicy1)
        self.label_185.setMinimumSize(QSize(30, 0))
        self.label_185.setMaximumSize(QSize(16777215, 16777215))
        self.label_185.setFont(font1)

        self.horizontalLayout_207.addWidget(self.label_185)

        self.dSBoxSmallSPLBand_16 = QDoubleSpinBox(self.groupBox_24)
        self.dSBoxSmallSPLBand_16.setObjectName(u"dSBoxSmallSPLBand_16")
        self.dSBoxSmallSPLBand_16.setDecimals(1)
        self.dSBoxSmallSPLBand_16.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_16.setMaximum(65535.500000000000000)

        self.horizontalLayout_207.addWidget(self.dSBoxSmallSPLBand_16)


        self.gridLayout_37.addLayout(self.horizontalLayout_207, 0, 0, 1, 1)

        self.horizontalLayout_208 = QHBoxLayout()
        self.horizontalLayout_208.setObjectName(u"horizontalLayout_208")
        self.label_186 = QLabel(self.groupBox_24)
        self.label_186.setObjectName(u"label_186")
        sizePolicy1.setHeightForWidth(self.label_186.sizePolicy().hasHeightForWidth())
        self.label_186.setSizePolicy(sizePolicy1)
        self.label_186.setMinimumSize(QSize(0, 0))
        self.label_186.setMaximumSize(QSize(16777215, 16777215))
        self.label_186.setFont(font1)

        self.horizontalLayout_208.addWidget(self.label_186)

        self.dSBoxSmallSPLGainBand_16 = QDoubleSpinBox(self.groupBox_24)
        self.dSBoxSmallSPLGainBand_16.setObjectName(u"dSBoxSmallSPLGainBand_16")
        self.dSBoxSmallSPLGainBand_16.setDecimals(1)
        self.dSBoxSmallSPLGainBand_16.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_16.setMaximum(65535.500000000000000)

        self.horizontalLayout_208.addWidget(self.dSBoxSmallSPLGainBand_16)


        self.gridLayout_37.addLayout(self.horizontalLayout_208, 0, 1, 1, 1)

        self.horizontalLayout_209 = QHBoxLayout()
        self.horizontalLayout_209.setObjectName(u"horizontalLayout_209")
        self.label_187 = QLabel(self.groupBox_24)
        self.label_187.setObjectName(u"label_187")
        sizePolicy1.setHeightForWidth(self.label_187.sizePolicy().hasHeightForWidth())
        self.label_187.setSizePolicy(sizePolicy1)
        self.label_187.setMinimumSize(QSize(30, 0))
        self.label_187.setMaximumSize(QSize(16777215, 16777215))
        self.label_187.setFont(font1)

        self.horizontalLayout_209.addWidget(self.label_187)

        self.dSBoxMediumSPLBand_16 = QDoubleSpinBox(self.groupBox_24)
        self.dSBoxMediumSPLBand_16.setObjectName(u"dSBoxMediumSPLBand_16")
        self.dSBoxMediumSPLBand_16.setDecimals(1)
        self.dSBoxMediumSPLBand_16.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_16.setMaximum(65535.500000000000000)

        self.horizontalLayout_209.addWidget(self.dSBoxMediumSPLBand_16)


        self.gridLayout_37.addLayout(self.horizontalLayout_209, 1, 0, 1, 1)

        self.horizontalLayout_210 = QHBoxLayout()
        self.horizontalLayout_210.setObjectName(u"horizontalLayout_210")
        self.label_188 = QLabel(self.groupBox_24)
        self.label_188.setObjectName(u"label_188")
        sizePolicy1.setHeightForWidth(self.label_188.sizePolicy().hasHeightForWidth())
        self.label_188.setSizePolicy(sizePolicy1)
        self.label_188.setMinimumSize(QSize(0, 0))
        self.label_188.setMaximumSize(QSize(16777215, 16777215))
        self.label_188.setFont(font1)

        self.horizontalLayout_210.addWidget(self.label_188)

        self.dSBoxMediumSPLGainBand_16 = QDoubleSpinBox(self.groupBox_24)
        self.dSBoxMediumSPLGainBand_16.setObjectName(u"dSBoxMediumSPLGainBand_16")
        self.dSBoxMediumSPLGainBand_16.setDecimals(1)
        self.dSBoxMediumSPLGainBand_16.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_16.setMaximum(65535.500000000000000)

        self.horizontalLayout_210.addWidget(self.dSBoxMediumSPLGainBand_16)


        self.gridLayout_37.addLayout(self.horizontalLayout_210, 1, 1, 1, 1)

        self.horizontalLayout_211 = QHBoxLayout()
        self.horizontalLayout_211.setObjectName(u"horizontalLayout_211")
        self.label_189 = QLabel(self.groupBox_24)
        self.label_189.setObjectName(u"label_189")
        sizePolicy1.setHeightForWidth(self.label_189.sizePolicy().hasHeightForWidth())
        self.label_189.setSizePolicy(sizePolicy1)
        self.label_189.setMinimumSize(QSize(30, 0))
        self.label_189.setMaximumSize(QSize(16777215, 16777215))
        self.label_189.setFont(font1)

        self.horizontalLayout_211.addWidget(self.label_189)

        self.dSBoxLargeSPLBand_16 = QDoubleSpinBox(self.groupBox_24)
        self.dSBoxLargeSPLBand_16.setObjectName(u"dSBoxLargeSPLBand_16")
        self.dSBoxLargeSPLBand_16.setDecimals(1)
        self.dSBoxLargeSPLBand_16.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_16.setMaximum(65535.500000000000000)

        self.horizontalLayout_211.addWidget(self.dSBoxLargeSPLBand_16)


        self.gridLayout_37.addLayout(self.horizontalLayout_211, 2, 0, 1, 1)

        self.horizontalLayout_212 = QHBoxLayout()
        self.horizontalLayout_212.setObjectName(u"horizontalLayout_212")
        self.label_190 = QLabel(self.groupBox_24)
        self.label_190.setObjectName(u"label_190")
        sizePolicy1.setHeightForWidth(self.label_190.sizePolicy().hasHeightForWidth())
        self.label_190.setSizePolicy(sizePolicy1)
        self.label_190.setMinimumSize(QSize(0, 0))
        self.label_190.setMaximumSize(QSize(16777215, 16777215))
        self.label_190.setFont(font1)

        self.horizontalLayout_212.addWidget(self.label_190)

        self.dSBoxLargeSPLGainBand_16 = QDoubleSpinBox(self.groupBox_24)
        self.dSBoxLargeSPLGainBand_16.setObjectName(u"dSBoxLargeSPLGainBand_16")
        self.dSBoxLargeSPLGainBand_16.setDecimals(1)
        self.dSBoxLargeSPLGainBand_16.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_16.setMaximum(65535.500000000000000)

        self.horizontalLayout_212.addWidget(self.dSBoxLargeSPLGainBand_16)


        self.gridLayout_37.addLayout(self.horizontalLayout_212, 2, 1, 1, 1)


        self.verticalLayout_20.addWidget(self.groupBox_24)


        self.gridLayout_15.addWidget(self.gpBand_16, 9, 0, 1, 1)

        self.gpBand_5 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_5.setObjectName(u"gpBand_5")
        sizePolicy8.setHeightForWidth(self.gpBand_5.sizePolicy().hasHeightForWidth())
        self.gpBand_5.setSizePolicy(sizePolicy8)
        self.gpBand_5.setMinimumSize(QSize(0, 0))
        self.gpBand_5.setFont(font1)
        self.verticalLayout_9 = QVBoxLayout(self.gpBand_5)
        self.verticalLayout_9.setObjectName(u"verticalLayout_9")
        self.horizontalLayout_78 = QHBoxLayout()
        self.horizontalLayout_78.setObjectName(u"horizontalLayout_78")
        self.horizontalLayout_78.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_79 = QHBoxLayout()
        self.horizontalLayout_79.setObjectName(u"horizontalLayout_79")
        self.label_69 = QLabel(self.gpBand_5)
        self.label_69.setObjectName(u"label_69")
        sizePolicy1.setHeightForWidth(self.label_69.sizePolicy().hasHeightForWidth())
        self.label_69.setSizePolicy(sizePolicy1)
        self.label_69.setMinimumSize(QSize(0, 0))
        self.label_69.setMaximumSize(QSize(16777215, 16777215))
        self.label_69.setFont(font1)

        self.horizontalLayout_79.addWidget(self.label_69)

        self.spinBoxAttackBand_5 = QSpinBox(self.gpBand_5)
        self.spinBoxAttackBand_5.setObjectName(u"spinBoxAttackBand_5")
        self.spinBoxAttackBand_5.setMaximum(65535)

        self.horizontalLayout_79.addWidget(self.spinBoxAttackBand_5)


        self.horizontalLayout_78.addLayout(self.horizontalLayout_79)

        self.horizontalLayout_80 = QHBoxLayout()
        self.horizontalLayout_80.setObjectName(u"horizontalLayout_80")
        self.label_70 = QLabel(self.gpBand_5)
        self.label_70.setObjectName(u"label_70")
        sizePolicy1.setHeightForWidth(self.label_70.sizePolicy().hasHeightForWidth())
        self.label_70.setSizePolicy(sizePolicy1)
        self.label_70.setMinimumSize(QSize(0, 0))
        self.label_70.setMaximumSize(QSize(16777215, 16777215))
        self.label_70.setFont(font1)

        self.horizontalLayout_80.addWidget(self.label_70)

        self.spinBoxReleaseBand_5 = QSpinBox(self.gpBand_5)
        self.spinBoxReleaseBand_5.setObjectName(u"spinBoxReleaseBand_5")
        self.spinBoxReleaseBand_5.setMaximum(65535)

        self.horizontalLayout_80.addWidget(self.spinBoxReleaseBand_5)


        self.horizontalLayout_78.addLayout(self.horizontalLayout_80)


        self.verticalLayout_9.addLayout(self.horizontalLayout_78)

        self.gpLimitBand_5 = QGroupBox(self.gpBand_5)
        self.gpLimitBand_5.setObjectName(u"gpLimitBand_5")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_5.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_5.setSizePolicy(sizePolicy6)
        self.gpLimitBand_5.setFont(font1)
        self.gridLayout_13 = QGridLayout(self.gpLimitBand_5)
        self.gridLayout_13.setObjectName(u"gridLayout_13")
        self.gridLayout_13.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_81 = QHBoxLayout()
        self.horizontalLayout_81.setObjectName(u"horizontalLayout_81")
        self.label_71 = QLabel(self.gpLimitBand_5)
        self.label_71.setObjectName(u"label_71")
        sizePolicy1.setHeightForWidth(self.label_71.sizePolicy().hasHeightForWidth())
        self.label_71.setSizePolicy(sizePolicy1)
        self.label_71.setMinimumSize(QSize(0, 0))
        self.label_71.setMaximumSize(QSize(16777215, 16777215))
        self.label_71.setFont(font1)

        self.horizontalLayout_81.addWidget(self.label_71)

        self.dSBoxLimitThresholdBand_5 = QDoubleSpinBox(self.gpLimitBand_5)
        self.dSBoxLimitThresholdBand_5.setObjectName(u"dSBoxLimitThresholdBand_5")
        self.dSBoxLimitThresholdBand_5.setDecimals(1)
        self.dSBoxLimitThresholdBand_5.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_5.setMaximum(65535.500000000000000)

        self.horizontalLayout_81.addWidget(self.dSBoxLimitThresholdBand_5)


        self.gridLayout_13.addLayout(self.horizontalLayout_81, 0, 0, 1, 1)

        self.horizontalLayout_82 = QHBoxLayout()
        self.horizontalLayout_82.setObjectName(u"horizontalLayout_82")
        self.label_72 = QLabel(self.gpLimitBand_5)
        self.label_72.setObjectName(u"label_72")
        sizePolicy1.setHeightForWidth(self.label_72.sizePolicy().hasHeightForWidth())
        self.label_72.setSizePolicy(sizePolicy1)
        self.label_72.setMinimumSize(QSize(0, 0))
        self.label_72.setMaximumSize(QSize(16777215, 16777215))
        self.label_72.setFont(font1)

        self.horizontalLayout_82.addWidget(self.label_72)

        self.dSBoxLimitKneeBand_5 = QDoubleSpinBox(self.gpLimitBand_5)
        self.dSBoxLimitKneeBand_5.setObjectName(u"dSBoxLimitKneeBand_5")
        self.dSBoxLimitKneeBand_5.setDecimals(1)
        self.dSBoxLimitKneeBand_5.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_5.setMaximum(30.000000000000000)

        self.horizontalLayout_82.addWidget(self.dSBoxLimitKneeBand_5)


        self.gridLayout_13.addLayout(self.horizontalLayout_82, 0, 1, 1, 1)


        self.verticalLayout_9.addWidget(self.gpLimitBand_5)

        self.groupBox_13 = QGroupBox(self.gpBand_5)
        self.groupBox_13.setObjectName(u"groupBox_13")
        sizePolicy6.setHeightForWidth(self.groupBox_13.sizePolicy().hasHeightForWidth())
        self.groupBox_13.setSizePolicy(sizePolicy6)
        self.gridLayout_14 = QGridLayout(self.groupBox_13)
        self.gridLayout_14.setObjectName(u"gridLayout_14")
        self.gridLayout_14.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_83 = QHBoxLayout()
        self.horizontalLayout_83.setObjectName(u"horizontalLayout_83")
        self.label_73 = QLabel(self.groupBox_13)
        self.label_73.setObjectName(u"label_73")
        sizePolicy1.setHeightForWidth(self.label_73.sizePolicy().hasHeightForWidth())
        self.label_73.setSizePolicy(sizePolicy1)
        self.label_73.setMinimumSize(QSize(30, 0))
        self.label_73.setMaximumSize(QSize(16777215, 16777215))
        self.label_73.setFont(font1)

        self.horizontalLayout_83.addWidget(self.label_73)

        self.dSBoxSmallSPLBand_5 = QDoubleSpinBox(self.groupBox_13)
        self.dSBoxSmallSPLBand_5.setObjectName(u"dSBoxSmallSPLBand_5")
        self.dSBoxSmallSPLBand_5.setDecimals(1)
        self.dSBoxSmallSPLBand_5.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_5.setMaximum(65535.500000000000000)

        self.horizontalLayout_83.addWidget(self.dSBoxSmallSPLBand_5)


        self.gridLayout_14.addLayout(self.horizontalLayout_83, 0, 0, 1, 1)

        self.horizontalLayout_84 = QHBoxLayout()
        self.horizontalLayout_84.setObjectName(u"horizontalLayout_84")
        self.label_74 = QLabel(self.groupBox_13)
        self.label_74.setObjectName(u"label_74")
        sizePolicy1.setHeightForWidth(self.label_74.sizePolicy().hasHeightForWidth())
        self.label_74.setSizePolicy(sizePolicy1)
        self.label_74.setMinimumSize(QSize(0, 0))
        self.label_74.setMaximumSize(QSize(16777215, 16777215))
        self.label_74.setFont(font1)

        self.horizontalLayout_84.addWidget(self.label_74)

        self.dSBoxSmallSPLGainBand_5 = QDoubleSpinBox(self.groupBox_13)
        self.dSBoxSmallSPLGainBand_5.setObjectName(u"dSBoxSmallSPLGainBand_5")
        self.dSBoxSmallSPLGainBand_5.setDecimals(1)
        self.dSBoxSmallSPLGainBand_5.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_5.setMaximum(65535.500000000000000)

        self.horizontalLayout_84.addWidget(self.dSBoxSmallSPLGainBand_5)


        self.gridLayout_14.addLayout(self.horizontalLayout_84, 0, 1, 1, 1)

        self.horizontalLayout_85 = QHBoxLayout()
        self.horizontalLayout_85.setObjectName(u"horizontalLayout_85")
        self.label_75 = QLabel(self.groupBox_13)
        self.label_75.setObjectName(u"label_75")
        sizePolicy1.setHeightForWidth(self.label_75.sizePolicy().hasHeightForWidth())
        self.label_75.setSizePolicy(sizePolicy1)
        self.label_75.setMinimumSize(QSize(30, 0))
        self.label_75.setMaximumSize(QSize(16777215, 16777215))
        self.label_75.setFont(font1)

        self.horizontalLayout_85.addWidget(self.label_75)

        self.dSBoxMediumSPLBand_5 = QDoubleSpinBox(self.groupBox_13)
        self.dSBoxMediumSPLBand_5.setObjectName(u"dSBoxMediumSPLBand_5")
        self.dSBoxMediumSPLBand_5.setDecimals(1)
        self.dSBoxMediumSPLBand_5.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_5.setMaximum(65535.500000000000000)

        self.horizontalLayout_85.addWidget(self.dSBoxMediumSPLBand_5)


        self.gridLayout_14.addLayout(self.horizontalLayout_85, 1, 0, 1, 1)

        self.horizontalLayout_86 = QHBoxLayout()
        self.horizontalLayout_86.setObjectName(u"horizontalLayout_86")
        self.label_76 = QLabel(self.groupBox_13)
        self.label_76.setObjectName(u"label_76")
        sizePolicy1.setHeightForWidth(self.label_76.sizePolicy().hasHeightForWidth())
        self.label_76.setSizePolicy(sizePolicy1)
        self.label_76.setMinimumSize(QSize(0, 0))
        self.label_76.setMaximumSize(QSize(16777215, 16777215))
        self.label_76.setFont(font1)

        self.horizontalLayout_86.addWidget(self.label_76)

        self.dSBoxMediumSPLGainBand_5 = QDoubleSpinBox(self.groupBox_13)
        self.dSBoxMediumSPLGainBand_5.setObjectName(u"dSBoxMediumSPLGainBand_5")
        self.dSBoxMediumSPLGainBand_5.setDecimals(1)
        self.dSBoxMediumSPLGainBand_5.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_5.setMaximum(65535.500000000000000)

        self.horizontalLayout_86.addWidget(self.dSBoxMediumSPLGainBand_5)


        self.gridLayout_14.addLayout(self.horizontalLayout_86, 1, 1, 1, 1)

        self.horizontalLayout_87 = QHBoxLayout()
        self.horizontalLayout_87.setObjectName(u"horizontalLayout_87")
        self.label_77 = QLabel(self.groupBox_13)
        self.label_77.setObjectName(u"label_77")
        sizePolicy1.setHeightForWidth(self.label_77.sizePolicy().hasHeightForWidth())
        self.label_77.setSizePolicy(sizePolicy1)
        self.label_77.setMinimumSize(QSize(30, 0))
        self.label_77.setMaximumSize(QSize(16777215, 16777215))
        self.label_77.setFont(font1)

        self.horizontalLayout_87.addWidget(self.label_77)

        self.dSBoxLargeSPLBand_5 = QDoubleSpinBox(self.groupBox_13)
        self.dSBoxLargeSPLBand_5.setObjectName(u"dSBoxLargeSPLBand_5")
        self.dSBoxLargeSPLBand_5.setDecimals(1)
        self.dSBoxLargeSPLBand_5.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_5.setMaximum(65535.500000000000000)

        self.horizontalLayout_87.addWidget(self.dSBoxLargeSPLBand_5)


        self.gridLayout_14.addLayout(self.horizontalLayout_87, 2, 0, 1, 1)

        self.horizontalLayout_88 = QHBoxLayout()
        self.horizontalLayout_88.setObjectName(u"horizontalLayout_88")
        self.label_78 = QLabel(self.groupBox_13)
        self.label_78.setObjectName(u"label_78")
        sizePolicy1.setHeightForWidth(self.label_78.sizePolicy().hasHeightForWidth())
        self.label_78.setSizePolicy(sizePolicy1)
        self.label_78.setMinimumSize(QSize(0, 0))
        self.label_78.setMaximumSize(QSize(16777215, 16777215))
        self.label_78.setFont(font1)

        self.horizontalLayout_88.addWidget(self.label_78)

        self.dSBoxLargeSPLGainBand_5 = QDoubleSpinBox(self.groupBox_13)
        self.dSBoxLargeSPLGainBand_5.setObjectName(u"dSBoxLargeSPLGainBand_5")
        self.dSBoxLargeSPLGainBand_5.setDecimals(1)
        self.dSBoxLargeSPLGainBand_5.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_5.setMaximum(65535.500000000000000)

        self.horizontalLayout_88.addWidget(self.dSBoxLargeSPLGainBand_5)


        self.gridLayout_14.addLayout(self.horizontalLayout_88, 2, 1, 1, 1)


        self.verticalLayout_9.addWidget(self.groupBox_13)


        self.gridLayout_15.addWidget(self.gpBand_5, 2, 1, 1, 1)

        self.gpBand_18 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_18.setObjectName(u"gpBand_18")
        sizePolicy8.setHeightForWidth(self.gpBand_18.sizePolicy().hasHeightForWidth())
        self.gpBand_18.setSizePolicy(sizePolicy8)
        self.gpBand_18.setMinimumSize(QSize(0, 0))
        self.gpBand_18.setFont(font1)
        self.verticalLayout_22 = QVBoxLayout(self.gpBand_18)
        self.verticalLayout_22.setObjectName(u"verticalLayout_22")
        self.horizontalLayout_224 = QHBoxLayout()
        self.horizontalLayout_224.setObjectName(u"horizontalLayout_224")
        self.horizontalLayout_224.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_225 = QHBoxLayout()
        self.horizontalLayout_225.setObjectName(u"horizontalLayout_225")
        self.label_201 = QLabel(self.gpBand_18)
        self.label_201.setObjectName(u"label_201")
        sizePolicy1.setHeightForWidth(self.label_201.sizePolicy().hasHeightForWidth())
        self.label_201.setSizePolicy(sizePolicy1)
        self.label_201.setMinimumSize(QSize(0, 0))
        self.label_201.setMaximumSize(QSize(16777215, 16777215))
        self.label_201.setFont(font1)

        self.horizontalLayout_225.addWidget(self.label_201)

        self.spinBoxAttackBand_18 = QSpinBox(self.gpBand_18)
        self.spinBoxAttackBand_18.setObjectName(u"spinBoxAttackBand_18")
        self.spinBoxAttackBand_18.setMaximum(65535)

        self.horizontalLayout_225.addWidget(self.spinBoxAttackBand_18)


        self.horizontalLayout_224.addLayout(self.horizontalLayout_225)

        self.horizontalLayout_226 = QHBoxLayout()
        self.horizontalLayout_226.setObjectName(u"horizontalLayout_226")
        self.label_202 = QLabel(self.gpBand_18)
        self.label_202.setObjectName(u"label_202")
        sizePolicy1.setHeightForWidth(self.label_202.sizePolicy().hasHeightForWidth())
        self.label_202.setSizePolicy(sizePolicy1)
        self.label_202.setMinimumSize(QSize(0, 0))
        self.label_202.setMaximumSize(QSize(16777215, 16777215))
        self.label_202.setFont(font1)

        self.horizontalLayout_226.addWidget(self.label_202)

        self.spinBoxReleaseBand_18 = QSpinBox(self.gpBand_18)
        self.spinBoxReleaseBand_18.setObjectName(u"spinBoxReleaseBand_18")
        self.spinBoxReleaseBand_18.setMaximum(65535)

        self.horizontalLayout_226.addWidget(self.spinBoxReleaseBand_18)


        self.horizontalLayout_224.addLayout(self.horizontalLayout_226)


        self.verticalLayout_22.addLayout(self.horizontalLayout_224)

        self.gpLimitBand_18 = QGroupBox(self.gpBand_18)
        self.gpLimitBand_18.setObjectName(u"gpLimitBand_18")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_18.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_18.setSizePolicy(sizePolicy6)
        self.gpLimitBand_18.setFont(font1)
        self.gridLayout_40 = QGridLayout(self.gpLimitBand_18)
        self.gridLayout_40.setObjectName(u"gridLayout_40")
        self.gridLayout_40.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_227 = QHBoxLayout()
        self.horizontalLayout_227.setObjectName(u"horizontalLayout_227")
        self.label_203 = QLabel(self.gpLimitBand_18)
        self.label_203.setObjectName(u"label_203")
        sizePolicy1.setHeightForWidth(self.label_203.sizePolicy().hasHeightForWidth())
        self.label_203.setSizePolicy(sizePolicy1)
        self.label_203.setMinimumSize(QSize(0, 0))
        self.label_203.setMaximumSize(QSize(16777215, 16777215))
        self.label_203.setFont(font1)

        self.horizontalLayout_227.addWidget(self.label_203)

        self.dSBoxLimitThresholdBand_18 = QDoubleSpinBox(self.gpLimitBand_18)
        self.dSBoxLimitThresholdBand_18.setObjectName(u"dSBoxLimitThresholdBand_18")
        self.dSBoxLimitThresholdBand_18.setDecimals(1)
        self.dSBoxLimitThresholdBand_18.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_18.setMaximum(65535.500000000000000)

        self.horizontalLayout_227.addWidget(self.dSBoxLimitThresholdBand_18)


        self.gridLayout_40.addLayout(self.horizontalLayout_227, 0, 0, 1, 1)

        self.horizontalLayout_228 = QHBoxLayout()
        self.horizontalLayout_228.setObjectName(u"horizontalLayout_228")
        self.label_204 = QLabel(self.gpLimitBand_18)
        self.label_204.setObjectName(u"label_204")
        sizePolicy1.setHeightForWidth(self.label_204.sizePolicy().hasHeightForWidth())
        self.label_204.setSizePolicy(sizePolicy1)
        self.label_204.setMinimumSize(QSize(0, 0))
        self.label_204.setMaximumSize(QSize(16777215, 16777215))
        self.label_204.setFont(font1)

        self.horizontalLayout_228.addWidget(self.label_204)

        self.dSBoxLimitKneeBand_18 = QDoubleSpinBox(self.gpLimitBand_18)
        self.dSBoxLimitKneeBand_18.setObjectName(u"dSBoxLimitKneeBand_18")
        self.dSBoxLimitKneeBand_18.setDecimals(1)
        self.dSBoxLimitKneeBand_18.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_18.setMaximum(30.000000000000000)

        self.horizontalLayout_228.addWidget(self.dSBoxLimitKneeBand_18)


        self.gridLayout_40.addLayout(self.horizontalLayout_228, 0, 1, 1, 1)


        self.verticalLayout_22.addWidget(self.gpLimitBand_18)

        self.groupBox_26 = QGroupBox(self.gpBand_18)
        self.groupBox_26.setObjectName(u"groupBox_26")
        sizePolicy6.setHeightForWidth(self.groupBox_26.sizePolicy().hasHeightForWidth())
        self.groupBox_26.setSizePolicy(sizePolicy6)
        self.gridLayout_41 = QGridLayout(self.groupBox_26)
        self.gridLayout_41.setObjectName(u"gridLayout_41")
        self.gridLayout_41.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_229 = QHBoxLayout()
        self.horizontalLayout_229.setObjectName(u"horizontalLayout_229")
        self.label_205 = QLabel(self.groupBox_26)
        self.label_205.setObjectName(u"label_205")
        sizePolicy1.setHeightForWidth(self.label_205.sizePolicy().hasHeightForWidth())
        self.label_205.setSizePolicy(sizePolicy1)
        self.label_205.setMinimumSize(QSize(30, 0))
        self.label_205.setMaximumSize(QSize(16777215, 16777215))
        self.label_205.setFont(font1)

        self.horizontalLayout_229.addWidget(self.label_205)

        self.dSBoxSmallSPLBand_18 = QDoubleSpinBox(self.groupBox_26)
        self.dSBoxSmallSPLBand_18.setObjectName(u"dSBoxSmallSPLBand_18")
        self.dSBoxSmallSPLBand_18.setDecimals(1)
        self.dSBoxSmallSPLBand_18.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_18.setMaximum(65535.500000000000000)

        self.horizontalLayout_229.addWidget(self.dSBoxSmallSPLBand_18)


        self.gridLayout_41.addLayout(self.horizontalLayout_229, 0, 0, 1, 1)

        self.horizontalLayout_230 = QHBoxLayout()
        self.horizontalLayout_230.setObjectName(u"horizontalLayout_230")
        self.label_206 = QLabel(self.groupBox_26)
        self.label_206.setObjectName(u"label_206")
        sizePolicy1.setHeightForWidth(self.label_206.sizePolicy().hasHeightForWidth())
        self.label_206.setSizePolicy(sizePolicy1)
        self.label_206.setMinimumSize(QSize(0, 0))
        self.label_206.setMaximumSize(QSize(16777215, 16777215))
        self.label_206.setFont(font1)

        self.horizontalLayout_230.addWidget(self.label_206)

        self.dSBoxSmallSPLGainBand_18 = QDoubleSpinBox(self.groupBox_26)
        self.dSBoxSmallSPLGainBand_18.setObjectName(u"dSBoxSmallSPLGainBand_18")
        self.dSBoxSmallSPLGainBand_18.setDecimals(1)
        self.dSBoxSmallSPLGainBand_18.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_18.setMaximum(65535.500000000000000)

        self.horizontalLayout_230.addWidget(self.dSBoxSmallSPLGainBand_18)


        self.gridLayout_41.addLayout(self.horizontalLayout_230, 0, 1, 1, 1)

        self.horizontalLayout_231 = QHBoxLayout()
        self.horizontalLayout_231.setObjectName(u"horizontalLayout_231")
        self.label_207 = QLabel(self.groupBox_26)
        self.label_207.setObjectName(u"label_207")
        sizePolicy1.setHeightForWidth(self.label_207.sizePolicy().hasHeightForWidth())
        self.label_207.setSizePolicy(sizePolicy1)
        self.label_207.setMinimumSize(QSize(30, 0))
        self.label_207.setMaximumSize(QSize(16777215, 16777215))
        self.label_207.setFont(font1)

        self.horizontalLayout_231.addWidget(self.label_207)

        self.dSBoxMediumSPLBand_18 = QDoubleSpinBox(self.groupBox_26)
        self.dSBoxMediumSPLBand_18.setObjectName(u"dSBoxMediumSPLBand_18")
        self.dSBoxMediumSPLBand_18.setDecimals(1)
        self.dSBoxMediumSPLBand_18.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_18.setMaximum(65535.500000000000000)

        self.horizontalLayout_231.addWidget(self.dSBoxMediumSPLBand_18)


        self.gridLayout_41.addLayout(self.horizontalLayout_231, 1, 0, 1, 1)

        self.horizontalLayout_232 = QHBoxLayout()
        self.horizontalLayout_232.setObjectName(u"horizontalLayout_232")
        self.label_208 = QLabel(self.groupBox_26)
        self.label_208.setObjectName(u"label_208")
        sizePolicy1.setHeightForWidth(self.label_208.sizePolicy().hasHeightForWidth())
        self.label_208.setSizePolicy(sizePolicy1)
        self.label_208.setMinimumSize(QSize(0, 0))
        self.label_208.setMaximumSize(QSize(16777215, 16777215))
        self.label_208.setFont(font1)

        self.horizontalLayout_232.addWidget(self.label_208)

        self.dSBoxMediumSPLGainBand_18 = QDoubleSpinBox(self.groupBox_26)
        self.dSBoxMediumSPLGainBand_18.setObjectName(u"dSBoxMediumSPLGainBand_18")
        self.dSBoxMediumSPLGainBand_18.setDecimals(1)
        self.dSBoxMediumSPLGainBand_18.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_18.setMaximum(65535.500000000000000)

        self.horizontalLayout_232.addWidget(self.dSBoxMediumSPLGainBand_18)


        self.gridLayout_41.addLayout(self.horizontalLayout_232, 1, 1, 1, 1)

        self.horizontalLayout_233 = QHBoxLayout()
        self.horizontalLayout_233.setObjectName(u"horizontalLayout_233")
        self.label_209 = QLabel(self.groupBox_26)
        self.label_209.setObjectName(u"label_209")
        sizePolicy1.setHeightForWidth(self.label_209.sizePolicy().hasHeightForWidth())
        self.label_209.setSizePolicy(sizePolicy1)
        self.label_209.setMinimumSize(QSize(30, 0))
        self.label_209.setMaximumSize(QSize(16777215, 16777215))
        self.label_209.setFont(font1)

        self.horizontalLayout_233.addWidget(self.label_209)

        self.dSBoxLargeSPLBand_18 = QDoubleSpinBox(self.groupBox_26)
        self.dSBoxLargeSPLBand_18.setObjectName(u"dSBoxLargeSPLBand_18")
        self.dSBoxLargeSPLBand_18.setDecimals(1)
        self.dSBoxLargeSPLBand_18.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_18.setMaximum(65535.500000000000000)

        self.horizontalLayout_233.addWidget(self.dSBoxLargeSPLBand_18)


        self.gridLayout_41.addLayout(self.horizontalLayout_233, 2, 0, 1, 1)

        self.horizontalLayout_234 = QHBoxLayout()
        self.horizontalLayout_234.setObjectName(u"horizontalLayout_234")
        self.label_210 = QLabel(self.groupBox_26)
        self.label_210.setObjectName(u"label_210")
        sizePolicy1.setHeightForWidth(self.label_210.sizePolicy().hasHeightForWidth())
        self.label_210.setSizePolicy(sizePolicy1)
        self.label_210.setMinimumSize(QSize(0, 0))
        self.label_210.setMaximumSize(QSize(16777215, 16777215))
        self.label_210.setFont(font1)

        self.horizontalLayout_234.addWidget(self.label_210)

        self.dSBoxLargeSPLGainBand_18 = QDoubleSpinBox(self.groupBox_26)
        self.dSBoxLargeSPLGainBand_18.setObjectName(u"dSBoxLargeSPLGainBand_18")
        self.dSBoxLargeSPLGainBand_18.setDecimals(1)
        self.dSBoxLargeSPLGainBand_18.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_18.setMaximum(65535.500000000000000)

        self.horizontalLayout_234.addWidget(self.dSBoxLargeSPLGainBand_18)


        self.gridLayout_41.addLayout(self.horizontalLayout_234, 2, 1, 1, 1)


        self.verticalLayout_22.addWidget(self.groupBox_26)


        self.gridLayout_15.addWidget(self.gpBand_18, 10, 0, 1, 1)

        self.gpBand_19 = QGroupBox(self.scrollAreaWidgetContents)
        self.gpBand_19.setObjectName(u"gpBand_19")
        sizePolicy8.setHeightForWidth(self.gpBand_19.sizePolicy().hasHeightForWidth())
        self.gpBand_19.setSizePolicy(sizePolicy8)
        self.gpBand_19.setMinimumSize(QSize(0, 0))
        self.gpBand_19.setFont(font1)
        self.verticalLayout_23 = QVBoxLayout(self.gpBand_19)
        self.verticalLayout_23.setObjectName(u"verticalLayout_23")
        self.horizontalLayout_235 = QHBoxLayout()
        self.horizontalLayout_235.setObjectName(u"horizontalLayout_235")
        self.horizontalLayout_235.setContentsMargins(3, -1, 3, -1)
        self.horizontalLayout_236 = QHBoxLayout()
        self.horizontalLayout_236.setObjectName(u"horizontalLayout_236")
        self.label_211 = QLabel(self.gpBand_19)
        self.label_211.setObjectName(u"label_211")
        sizePolicy1.setHeightForWidth(self.label_211.sizePolicy().hasHeightForWidth())
        self.label_211.setSizePolicy(sizePolicy1)
        self.label_211.setMinimumSize(QSize(0, 0))
        self.label_211.setMaximumSize(QSize(16777215, 16777215))
        self.label_211.setFont(font1)

        self.horizontalLayout_236.addWidget(self.label_211)

        self.spinBoxAttackBand_19 = QSpinBox(self.gpBand_19)
        self.spinBoxAttackBand_19.setObjectName(u"spinBoxAttackBand_19")
        self.spinBoxAttackBand_19.setMaximum(65535)

        self.horizontalLayout_236.addWidget(self.spinBoxAttackBand_19)


        self.horizontalLayout_235.addLayout(self.horizontalLayout_236)

        self.horizontalLayout_237 = QHBoxLayout()
        self.horizontalLayout_237.setObjectName(u"horizontalLayout_237")
        self.label_212 = QLabel(self.gpBand_19)
        self.label_212.setObjectName(u"label_212")
        sizePolicy1.setHeightForWidth(self.label_212.sizePolicy().hasHeightForWidth())
        self.label_212.setSizePolicy(sizePolicy1)
        self.label_212.setMinimumSize(QSize(0, 0))
        self.label_212.setMaximumSize(QSize(16777215, 16777215))
        self.label_212.setFont(font1)

        self.horizontalLayout_237.addWidget(self.label_212)

        self.spinBoxReleaseBand_19 = QSpinBox(self.gpBand_19)
        self.spinBoxReleaseBand_19.setObjectName(u"spinBoxReleaseBand_19")
        self.spinBoxReleaseBand_19.setMaximum(65535)

        self.horizontalLayout_237.addWidget(self.spinBoxReleaseBand_19)


        self.horizontalLayout_235.addLayout(self.horizontalLayout_237)


        self.verticalLayout_23.addLayout(self.horizontalLayout_235)

        self.gpLimitBand_19 = QGroupBox(self.gpBand_19)
        self.gpLimitBand_19.setObjectName(u"gpLimitBand_19")
        sizePolicy6.setHeightForWidth(self.gpLimitBand_19.sizePolicy().hasHeightForWidth())
        self.gpLimitBand_19.setSizePolicy(sizePolicy6)
        self.gpLimitBand_19.setFont(font1)
        self.gridLayout_42 = QGridLayout(self.gpLimitBand_19)
        self.gridLayout_42.setObjectName(u"gridLayout_42")
        self.gridLayout_42.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_238 = QHBoxLayout()
        self.horizontalLayout_238.setObjectName(u"horizontalLayout_238")
        self.label_213 = QLabel(self.gpLimitBand_19)
        self.label_213.setObjectName(u"label_213")
        sizePolicy1.setHeightForWidth(self.label_213.sizePolicy().hasHeightForWidth())
        self.label_213.setSizePolicy(sizePolicy1)
        self.label_213.setMinimumSize(QSize(0, 0))
        self.label_213.setMaximumSize(QSize(16777215, 16777215))
        self.label_213.setFont(font1)

        self.horizontalLayout_238.addWidget(self.label_213)

        self.dSBoxLimitThresholdBand_19 = QDoubleSpinBox(self.gpLimitBand_19)
        self.dSBoxLimitThresholdBand_19.setObjectName(u"dSBoxLimitThresholdBand_19")
        self.dSBoxLimitThresholdBand_19.setDecimals(1)
        self.dSBoxLimitThresholdBand_19.setMinimum(-65535.500000000000000)
        self.dSBoxLimitThresholdBand_19.setMaximum(65535.500000000000000)

        self.horizontalLayout_238.addWidget(self.dSBoxLimitThresholdBand_19)


        self.gridLayout_42.addLayout(self.horizontalLayout_238, 0, 0, 1, 1)

        self.horizontalLayout_239 = QHBoxLayout()
        self.horizontalLayout_239.setObjectName(u"horizontalLayout_239")
        self.label_214 = QLabel(self.gpLimitBand_19)
        self.label_214.setObjectName(u"label_214")
        sizePolicy1.setHeightForWidth(self.label_214.sizePolicy().hasHeightForWidth())
        self.label_214.setSizePolicy(sizePolicy1)
        self.label_214.setMinimumSize(QSize(0, 0))
        self.label_214.setMaximumSize(QSize(16777215, 16777215))
        self.label_214.setFont(font1)

        self.horizontalLayout_239.addWidget(self.label_214)

        self.dSBoxLimitKneeBand_19 = QDoubleSpinBox(self.gpLimitBand_19)
        self.dSBoxLimitKneeBand_19.setObjectName(u"dSBoxLimitKneeBand_19")
        self.dSBoxLimitKneeBand_19.setDecimals(1)
        self.dSBoxLimitKneeBand_19.setMinimum(0.000000000000000)
        self.dSBoxLimitKneeBand_19.setMaximum(30.000000000000000)

        self.horizontalLayout_239.addWidget(self.dSBoxLimitKneeBand_19)


        self.gridLayout_42.addLayout(self.horizontalLayout_239, 0, 1, 1, 1)


        self.verticalLayout_23.addWidget(self.gpLimitBand_19)

        self.groupBox_27 = QGroupBox(self.gpBand_19)
        self.groupBox_27.setObjectName(u"groupBox_27")
        sizePolicy6.setHeightForWidth(self.groupBox_27.sizePolicy().hasHeightForWidth())
        self.groupBox_27.setSizePolicy(sizePolicy6)
        self.gridLayout_43 = QGridLayout(self.groupBox_27)
        self.gridLayout_43.setObjectName(u"gridLayout_43")
        self.gridLayout_43.setContentsMargins(3, 3, 3, 3)
        self.horizontalLayout_240 = QHBoxLayout()
        self.horizontalLayout_240.setObjectName(u"horizontalLayout_240")
        self.label_215 = QLabel(self.groupBox_27)
        self.label_215.setObjectName(u"label_215")
        sizePolicy1.setHeightForWidth(self.label_215.sizePolicy().hasHeightForWidth())
        self.label_215.setSizePolicy(sizePolicy1)
        self.label_215.setMinimumSize(QSize(30, 0))
        self.label_215.setMaximumSize(QSize(16777215, 16777215))
        self.label_215.setFont(font1)

        self.horizontalLayout_240.addWidget(self.label_215)

        self.dSBoxSmallSPLBand_19 = QDoubleSpinBox(self.groupBox_27)
        self.dSBoxSmallSPLBand_19.setObjectName(u"dSBoxSmallSPLBand_19")
        self.dSBoxSmallSPLBand_19.setDecimals(1)
        self.dSBoxSmallSPLBand_19.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLBand_19.setMaximum(65535.500000000000000)

        self.horizontalLayout_240.addWidget(self.dSBoxSmallSPLBand_19)


        self.gridLayout_43.addLayout(self.horizontalLayout_240, 0, 0, 1, 1)

        self.horizontalLayout_241 = QHBoxLayout()
        self.horizontalLayout_241.setObjectName(u"horizontalLayout_241")
        self.label_216 = QLabel(self.groupBox_27)
        self.label_216.setObjectName(u"label_216")
        sizePolicy1.setHeightForWidth(self.label_216.sizePolicy().hasHeightForWidth())
        self.label_216.setSizePolicy(sizePolicy1)
        self.label_216.setMinimumSize(QSize(0, 0))
        self.label_216.setMaximumSize(QSize(16777215, 16777215))
        self.label_216.setFont(font1)

        self.horizontalLayout_241.addWidget(self.label_216)

        self.dSBoxSmallSPLGainBand_19 = QDoubleSpinBox(self.groupBox_27)
        self.dSBoxSmallSPLGainBand_19.setObjectName(u"dSBoxSmallSPLGainBand_19")
        self.dSBoxSmallSPLGainBand_19.setDecimals(1)
        self.dSBoxSmallSPLGainBand_19.setMinimum(-65535.500000000000000)
        self.dSBoxSmallSPLGainBand_19.setMaximum(65535.500000000000000)

        self.horizontalLayout_241.addWidget(self.dSBoxSmallSPLGainBand_19)


        self.gridLayout_43.addLayout(self.horizontalLayout_241, 0, 1, 1, 1)

        self.horizontalLayout_242 = QHBoxLayout()
        self.horizontalLayout_242.setObjectName(u"horizontalLayout_242")
        self.label_217 = QLabel(self.groupBox_27)
        self.label_217.setObjectName(u"label_217")
        sizePolicy1.setHeightForWidth(self.label_217.sizePolicy().hasHeightForWidth())
        self.label_217.setSizePolicy(sizePolicy1)
        self.label_217.setMinimumSize(QSize(30, 0))
        self.label_217.setMaximumSize(QSize(16777215, 16777215))
        self.label_217.setFont(font1)

        self.horizontalLayout_242.addWidget(self.label_217)

        self.dSBoxMediumSPLBand_19 = QDoubleSpinBox(self.groupBox_27)
        self.dSBoxMediumSPLBand_19.setObjectName(u"dSBoxMediumSPLBand_19")
        self.dSBoxMediumSPLBand_19.setDecimals(1)
        self.dSBoxMediumSPLBand_19.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLBand_19.setMaximum(65535.500000000000000)

        self.horizontalLayout_242.addWidget(self.dSBoxMediumSPLBand_19)


        self.gridLayout_43.addLayout(self.horizontalLayout_242, 1, 0, 1, 1)

        self.horizontalLayout_243 = QHBoxLayout()
        self.horizontalLayout_243.setObjectName(u"horizontalLayout_243")
        self.label_218 = QLabel(self.groupBox_27)
        self.label_218.setObjectName(u"label_218")
        sizePolicy1.setHeightForWidth(self.label_218.sizePolicy().hasHeightForWidth())
        self.label_218.setSizePolicy(sizePolicy1)
        self.label_218.setMinimumSize(QSize(0, 0))
        self.label_218.setMaximumSize(QSize(16777215, 16777215))
        self.label_218.setFont(font1)

        self.horizontalLayout_243.addWidget(self.label_218)

        self.dSBoxMediumSPLGainBand_19 = QDoubleSpinBox(self.groupBox_27)
        self.dSBoxMediumSPLGainBand_19.setObjectName(u"dSBoxMediumSPLGainBand_19")
        self.dSBoxMediumSPLGainBand_19.setDecimals(1)
        self.dSBoxMediumSPLGainBand_19.setMinimum(-65535.500000000000000)
        self.dSBoxMediumSPLGainBand_19.setMaximum(65535.500000000000000)

        self.horizontalLayout_243.addWidget(self.dSBoxMediumSPLGainBand_19)


        self.gridLayout_43.addLayout(self.horizontalLayout_243, 1, 1, 1, 1)

        self.horizontalLayout_244 = QHBoxLayout()
        self.horizontalLayout_244.setObjectName(u"horizontalLayout_244")
        self.label_219 = QLabel(self.groupBox_27)
        self.label_219.setObjectName(u"label_219")
        sizePolicy1.setHeightForWidth(self.label_219.sizePolicy().hasHeightForWidth())
        self.label_219.setSizePolicy(sizePolicy1)
        self.label_219.setMinimumSize(QSize(30, 0))
        self.label_219.setMaximumSize(QSize(16777215, 16777215))
        self.label_219.setFont(font1)

        self.horizontalLayout_244.addWidget(self.label_219)

        self.dSBoxLargeSPLBand_19 = QDoubleSpinBox(self.groupBox_27)
        self.dSBoxLargeSPLBand_19.setObjectName(u"dSBoxLargeSPLBand_19")
        self.dSBoxLargeSPLBand_19.setDecimals(1)
        self.dSBoxLargeSPLBand_19.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLBand_19.setMaximum(65535.500000000000000)

        self.horizontalLayout_244.addWidget(self.dSBoxLargeSPLBand_19)


        self.gridLayout_43.addLayout(self.horizontalLayout_244, 2, 0, 1, 1)

        self.horizontalLayout_245 = QHBoxLayout()
        self.horizontalLayout_245.setObjectName(u"horizontalLayout_245")
        self.label_220 = QLabel(self.groupBox_27)
        self.label_220.setObjectName(u"label_220")
        sizePolicy1.setHeightForWidth(self.label_220.sizePolicy().hasHeightForWidth())
        self.label_220.setSizePolicy(sizePolicy1)
        self.label_220.setMinimumSize(QSize(0, 0))
        self.label_220.setMaximumSize(QSize(16777215, 16777215))
        self.label_220.setFont(font1)

        self.horizontalLayout_245.addWidget(self.label_220)

        self.dSBoxLargeSPLGainBand_19 = QDoubleSpinBox(self.groupBox_27)
        self.dSBoxLargeSPLGainBand_19.setObjectName(u"dSBoxLargeSPLGainBand_19")
        self.dSBoxLargeSPLGainBand_19.setDecimals(1)
        self.dSBoxLargeSPLGainBand_19.setMinimum(-65535.500000000000000)
        self.dSBoxLargeSPLGainBand_19.setMaximum(65535.500000000000000)

        self.horizontalLayout_245.addWidget(self.dSBoxLargeSPLGainBand_19)


        self.gridLayout_43.addLayout(self.horizontalLayout_245, 2, 1, 1, 1)


        self.verticalLayout_23.addWidget(self.groupBox_27)


        self.gridLayout_15.addWidget(self.gpBand_19, 10, 1, 1, 1)

        self.scrollArea.setWidget(self.scrollAreaWidgetContents)

        self.verticalLayout_3.addWidget(self.scrollArea)


        self.gridLayout_11.addWidget(self.groupBoxWDRC, 1, 0, 1, 1)

        self.tabWidgetCtrl.addTab(self.tabWDRC, "")

        self.horizontalLayout_263.addWidget(self.tabWidgetCtrl)

        self.groupLog = QGroupBox(self.centralwidget)
        self.groupLog.setObjectName(u"groupLog")
        sizePolicy9 = QSizePolicy(QSizePolicy.Policy.Maximum, QSizePolicy.Policy.Expanding)
        sizePolicy9.setHorizontalStretch(0)
        sizePolicy9.setVerticalStretch(0)
        sizePolicy9.setHeightForWidth(self.groupLog.sizePolicy().hasHeightForWidth())
        self.groupLog.setSizePolicy(sizePolicy9)
        self.groupLog.setMinimumSize(QSize(300, 0))
        self.groupLog.setMaximumSize(QSize(16777215, 16777215))
        self.groupLog.setFont(font1)
        self.verticalLayout = QVBoxLayout(self.groupLog)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(-1, 9, -1, -1)
        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.logText = QTextBrowser(self.groupLog)
        self.logText.setObjectName(u"logText")
        self.logText.setMaximumSize(QSize(16777215, 16777215))
        font2 = QFont()
        font2.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        font2.setPointSize(9)
        self.logText.setFont(font2)

        self.horizontalLayout_7.addWidget(self.logText)


        self.verticalLayout.addLayout(self.horizontalLayout_7)


        self.horizontalLayout_263.addWidget(self.groupLog)

        CaseCtrlTool.setCentralWidget(self.centralwidget)
        self.menuBar = QMenuBar(CaseCtrlTool)
        self.menuBar.setObjectName(u"menuBar")
        self.menuBar.setGeometry(QRect(0, 0, 998, 23))
        self.menuBar.setFont(font1)
        self.menu = QMenu(self.menuBar)
        self.menu.setObjectName(u"menu")
        CaseCtrlTool.setMenuBar(self.menuBar)
        self.statusBar = QStatusBar(CaseCtrlTool)
        self.statusBar.setObjectName(u"statusBar")
        CaseCtrlTool.setStatusBar(self.statusBar)

        self.menuBar.addAction(self.menu.menuAction())
        self.menu.addAction(self.actionExport)
        self.menu.addAction(self.actionImport)
        self.menu.addSeparator()
        self.menu.addAction(self.actionAbout)

        self.retranslateUi(CaseCtrlTool)

        self.tabWidgetCtrl.setCurrentIndex(0)
        self.comboBoxLeAudioFmtL.setCurrentIndex(0)
        self.comboBoxLeAudioFmtR.setCurrentIndex(0)
        self.comboBoxDebugLevel.setCurrentIndex(4)


        QMetaObject.connectSlotsByName(CaseCtrlTool)
    # setupUi

    def retranslateUi(self, CaseCtrlTool):
        CaseCtrlTool.setWindowTitle(QCoreApplication.translate("CaseCtrlTool", u"Case\u63a7\u5236\u5de5\u5177", None))
        self.actionMenu.setText(QCoreApplication.translate("CaseCtrlTool", u"\u53c2\u6570\u914d\u7f6e", None))
        self.actionAbout.setText(QCoreApplication.translate("CaseCtrlTool", u"\u7248\u672c\u4fe1\u606f", None))
        self.actionUploadRecords.setText(QCoreApplication.translate("CaseCtrlTool", u"\u4e0a\u4f20\u6d4b\u8bd5\u6570\u636e", None))
        self.actionClearLocalDb.setText(QCoreApplication.translate("CaseCtrlTool", u"\u6e05\u9664\u6d4b\u8bd5\u6570\u636e", None))
        self.actionExportRecords.setText(QCoreApplication.translate("CaseCtrlTool", u"\u5bfc\u51fa\u672c\u5730\u6d4b\u8bd5\u6570\u636e", None))
        self.actionStyleAuto.setText(QCoreApplication.translate("CaseCtrlTool", u"\u7cfb\u7edf", None))
        self.actionStyleDark.setText(QCoreApplication.translate("CaseCtrlTool", u"\u6697\u9ed1", None))
        self.actionStyleLight.setText(QCoreApplication.translate("CaseCtrlTool", u"\u6d45\u8272", None))
        self.actionDebugTool.setText(QCoreApplication.translate("CaseCtrlTool", u"BLE\u8c03\u8bd5\u5de5\u5177", None))
        self.actionExport.setText(QCoreApplication.translate("CaseCtrlTool", u"\u5bfc\u51fa\u53c2\u6570", None))
        self.actionImport.setText(QCoreApplication.translate("CaseCtrlTool", u"\u5bfc\u5165\u53c2\u6570", None))
        self.label.setText(QCoreApplication.translate("CaseCtrlTool", u"\u8bbe\u5907\uff1a", None))
        self.btnScan.setText(QCoreApplication.translate("CaseCtrlTool", u"Scan", None))
        self.btnConnect.setText(QCoreApplication.translate("CaseCtrlTool", u"Connect", None))
        self.btnSync.setText(QCoreApplication.translate("CaseCtrlTool", u"Sync\u53c2\u6570", None))
        self.btnSave.setText(QCoreApplication.translate("CaseCtrlTool", u"\u4fdd\u5b58\u53c2\u6570", None))
        self.label_26.setText(QCoreApplication.translate("CaseCtrlTool", u"\u7248\u672c\u4fe1\u606f\uff1a", None))
        self.label_56.setText(QCoreApplication.translate("CaseCtrlTool", u"\u5f00\u5173(PSAP_SW)\uff1a", None))
        self.comboBoxPsapSwCtrl.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"OFF", None))
        self.comboBoxPsapSwCtrl.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"ON", None))

        self.groupBoxYmAlg.setTitle(QCoreApplication.translate("CaseCtrlTool", u"\u7b97\u6cd5\u53c2\u6570", None))
        self.groupBoxGain.setTitle("")
        self.label_10.setText(QCoreApplication.translate("CaseCtrlTool", u"Input Gain\uff1a", None))
        self.label_17.setText(QCoreApplication.translate("CaseCtrlTool", u"Output Gain\uff1a", None))
        self.groupBoxNs.setTitle(QCoreApplication.translate("CaseCtrlTool", u"\u964d\u566a", None))
        self.label_3.setText(QCoreApplication.translate("CaseCtrlTool", u"\u6a21\u5f0f\uff1a", None))
        self.comboBoxNsMode.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"0", None))
        self.comboBoxNsMode.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"1", None))
        self.comboBoxNsMode.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"2", None))
        self.comboBoxNsMode.setItemText(3, QCoreApplication.translate("CaseCtrlTool", u"3", None))
        self.comboBoxNsMode.setItemText(4, QCoreApplication.translate("CaseCtrlTool", u"4", None))
        self.comboBoxNsMode.setItemText(5, QCoreApplication.translate("CaseCtrlTool", u"5", None))
        self.comboBoxNsMode.setItemText(6, QCoreApplication.translate("CaseCtrlTool", u"6", None))
        self.comboBoxNsMode.setItemText(7, QCoreApplication.translate("CaseCtrlTool", u"7", None))
        self.comboBoxNsMode.setItemText(8, QCoreApplication.translate("CaseCtrlTool", u"8", None))
        self.comboBoxNsMode.setItemText(9, QCoreApplication.translate("CaseCtrlTool", u"9", None))

        self.groupBoxSweepFreq.setTitle(QCoreApplication.translate("CaseCtrlTool", u"SweepFreq", None))
        self.label_79.setText(QCoreApplication.translate("CaseCtrlTool", u"Type\uff1a", None))
        self.comboBoxSweepType.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"0", None))
        self.comboBoxSweepType.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"1", None))
        self.comboBoxSweepType.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"2", None))
        self.comboBoxSweepType.setItemText(3, QCoreApplication.translate("CaseCtrlTool", u"3", None))

        self.label_19.setText(QCoreApplication.translate("CaseCtrlTool", u"Freq\uff1a", None))
        self.groupBoxLimit.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_222.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold\uff1a", None))
        self.label_223.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee\uff1a", None))
        self.groupBoxFrameRms.setTitle(QCoreApplication.translate("CaseCtrlTool", u"RMS", None))
        self.label_224.setText(QCoreApplication.translate("CaseCtrlTool", u"\u5168\u9891RMS\uff1a", None))
        self.label_225.setText(QCoreApplication.translate("CaseCtrlTool", u"\u4f4e\u9891RMS\uff1a", None))
        self.groupBoxSsl.setTitle(QCoreApplication.translate("CaseCtrlTool", u"SSL", None))
        self.checkBoxSslAngle.setText(QCoreApplication.translate("CaseCtrlTool", u"SSL Angle\uff1a", None))
        self.label_228.setText(QCoreApplication.translate("CaseCtrlTool", u"Area_ID\uff1a", None))
        self.comboBoxSSLAreaID.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"0", None))
        self.comboBoxSSLAreaID.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"1", None))
        self.comboBoxSSLAreaID.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"2", None))
        self.comboBoxSSLAreaID.setItemText(3, QCoreApplication.translate("CaseCtrlTool", u"3", None))
        self.comboBoxSSLAreaID.setItemText(4, QCoreApplication.translate("CaseCtrlTool", u"4", None))
        self.comboBoxSSLAreaID.setItemText(5, QCoreApplication.translate("CaseCtrlTool", u"5", None))
        self.comboBoxSSLAreaID.setItemText(6, QCoreApplication.translate("CaseCtrlTool", u"6", None))
        self.comboBoxSSLAreaID.setItemText(7, QCoreApplication.translate("CaseCtrlTool", u"7", None))
        self.comboBoxSSLAreaID.setItemText(8, QCoreApplication.translate("CaseCtrlTool", u"8", None))
        self.comboBoxSSLAreaID.setItemText(9, QCoreApplication.translate("CaseCtrlTool", u"9", None))
        self.comboBoxSSLAreaID.setItemText(10, QCoreApplication.translate("CaseCtrlTool", u"10", None))
        self.comboBoxSSLAreaID.setItemText(11, QCoreApplication.translate("CaseCtrlTool", u"11", None))
        self.comboBoxSSLAreaID.setItemText(12, QCoreApplication.translate("CaseCtrlTool", u"12", None))
        self.comboBoxSSLAreaID.setItemText(13, QCoreApplication.translate("CaseCtrlTool", u"13", None))
        self.comboBoxSSLAreaID.setItemText(14, QCoreApplication.translate("CaseCtrlTool", u"14", None))
        self.comboBoxSSLAreaID.setItemText(15, QCoreApplication.translate("CaseCtrlTool", u"15", None))

        self.label_229.setText(QCoreApplication.translate("CaseCtrlTool", u"Enable\uff1a", None))
        self.comboBoxSSLAreaEnable.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"0", None))
        self.comboBoxSSLAreaEnable.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"1", None))

        self.label_6.setText(QCoreApplication.translate("CaseCtrlTool", u"V_MIN\uff1a", None))
        self.label_7.setText(QCoreApplication.translate("CaseCtrlTool", u"V_MAX\uff1a", None))
        self.label_8.setText(QCoreApplication.translate("CaseCtrlTool", u"H_MIN\uff1a", None))
        self.label_9.setText(QCoreApplication.translate("CaseCtrlTool", u"H_MAX\uff1a", None))
        self.groupBoxBeam.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Beam", None))
        self.label_5.setText(QCoreApplication.translate("CaseCtrlTool", u"H_Angle\uff1a", None))
        self.label_90.setText(QCoreApplication.translate("CaseCtrlTool", u"Out_ID\uff1a", None))
        self.comboBoxBeamOutID.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"0", None))
        self.comboBoxBeamOutID.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"1", None))
        self.comboBoxBeamOutID.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"2", None))
        self.comboBoxBeamOutID.setItemText(3, QCoreApplication.translate("CaseCtrlTool", u"3", None))
        self.comboBoxBeamOutID.setItemText(4, QCoreApplication.translate("CaseCtrlTool", u"4", None))

        self.label_68.setText(QCoreApplication.translate("CaseCtrlTool", u"Mode\uff1a", None))
        self.comboBoxBeamMode.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"Fixed", None))
        self.comboBoxBeamMode.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"Adaptive", None))

        self.label_4.setText(QCoreApplication.translate("CaseCtrlTool", u"V_Angle\uff1a", None))
        self.label_227.setText(QCoreApplication.translate("CaseCtrlTool", u"Width\uff1a", None))
        self.comboBoxBeamWidth.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"0", None))
        self.comboBoxBeamWidth.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"1", None))
        self.comboBoxBeamWidth.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"2", None))
        self.comboBoxBeamWidth.setItemText(3, QCoreApplication.translate("CaseCtrlTool", u"3", None))

        self.groupBoxLeAudio.setTitle(QCoreApplication.translate("CaseCtrlTool", u"LE Audio", None))
        self.label_221.setText(QCoreApplication.translate("CaseCtrlTool", u"Left\uff1a", None))
        self.comboBoxLeAudioFmtL.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"BEAM", None))
        self.comboBoxLeAudioFmtL.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"MIC_1", None))
        self.comboBoxLeAudioFmtL.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"MIC_2", None))
        self.comboBoxLeAudioFmtL.setItemText(3, QCoreApplication.translate("CaseCtrlTool", u"MIC_3", None))
        self.comboBoxLeAudioFmtL.setItemText(4, QCoreApplication.translate("CaseCtrlTool", u"MIC_4", None))

        self.label_226.setText(QCoreApplication.translate("CaseCtrlTool", u"Right\uff1a", None))
        self.comboBoxLeAudioFmtR.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"BEAM", None))
        self.comboBoxLeAudioFmtR.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"MIC_1", None))
        self.comboBoxLeAudioFmtR.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"MIC_2", None))
        self.comboBoxLeAudioFmtR.setItemText(3, QCoreApplication.translate("CaseCtrlTool", u"MIC_3", None))
        self.comboBoxLeAudioFmtR.setItemText(4, QCoreApplication.translate("CaseCtrlTool", u"MIC_4", None))

        self.label_36.setText(QCoreApplication.translate("CaseCtrlTool", u"Debug Level\uff1a", None))
        self.comboBoxDebugLevel.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"CRITICAL", None))
        self.comboBoxDebugLevel.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"ERROR", None))
        self.comboBoxDebugLevel.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"WARN", None))
        self.comboBoxDebugLevel.setItemText(3, QCoreApplication.translate("CaseCtrlTool", u"NOTIF", None))
        self.comboBoxDebugLevel.setItemText(4, QCoreApplication.translate("CaseCtrlTool", u"INFO", None))
        self.comboBoxDebugLevel.setItemText(5, QCoreApplication.translate("CaseCtrlTool", u"DEBUG", None))
        self.comboBoxDebugLevel.setItemText(6, QCoreApplication.translate("CaseCtrlTool", u"VERBOSE", None))

        self.label_46.setText(QCoreApplication.translate("CaseCtrlTool", u"Audio Dump\uff1a", None))
        self.comboBoxAudioDumpFormat.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"Disable", None))
        self.comboBoxAudioDumpFormat.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"Mic1 + Mic2 + Mic3 + Mic4", None))
        self.comboBoxAudioDumpFormat.setItemText(2, QCoreApplication.translate("CaseCtrlTool", u"LEA0 + LEA1 + Mic0 + Mic1", None))

        self.comboBoxAudioDumpBits.setItemText(0, QCoreApplication.translate("CaseCtrlTool", u"16bits", None))
        self.comboBoxAudioDumpBits.setItemText(1, QCoreApplication.translate("CaseCtrlTool", u"32bits", None))

        self.label_2.setText(QCoreApplication.translate("CaseCtrlTool", u"\u7b97\u6cd5\u6388\u6743\uff1a", None))
        self.btnAlgActivate.setText(QCoreApplication.translate("CaseCtrlTool", u"\u6388\u6743", None))
        self.tabWidgetCtrl.setTabText(self.tabWidgetCtrl.indexOf(self.tabControl), QCoreApplication.translate("CaseCtrlTool", u"Control", None))
        self.groupBoxWDRC.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Enable", None))
        self.groupBox_7.setTitle("")
        self.label_59.setText(QCoreApplication.translate("CaseCtrlTool", u"All Gain", None))
        self.gpBand_1.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 1", None))
        self.label_27.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_28.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_1.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_12.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_29.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_9.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_30.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_31.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_32.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_33.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_34.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_35.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_12.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 12", None))
        self.label_141.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_142.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_12.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_143.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_144.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_20.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_145.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_146.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_147.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_148.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_149.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_150.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_13.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 13", None))
        self.label_151.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_152.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_13.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_153.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_154.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_21.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_155.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_156.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_157.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_158.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_159.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_160.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_11.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 11", None))
        self.label_131.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_132.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_11.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_133.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_134.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_19.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_135.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_136.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_137.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_138.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_139.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_140.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_6.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 6", None))
        self.label_80.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_81.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_6.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_82.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_83.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_14.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_84.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_85.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_86.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_87.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_88.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_89.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_9.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 9", None))
        self.label_111.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_112.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_9.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_113.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_114.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_17.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_115.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_116.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_117.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_118.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_119.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_120.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_17.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 17", None))
        self.label_191.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_192.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_17.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_193.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_194.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_25.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_195.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_196.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_197.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_198.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_199.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_200.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_15.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 15", None))
        self.label_171.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_172.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_15.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_173.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_174.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_23.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_175.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_176.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_177.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_178.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_179.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_180.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_14.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 14", None))
        self.label_161.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_162.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_14.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_163.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_164.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_22.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_165.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_166.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_167.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_168.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_169.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_170.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_3.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 3", None))
        self.label_47.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_48.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_3.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_14.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_49.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_11.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_50.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_51.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_52.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_53.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_54.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_55.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_2.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 2", None))
        self.label_37.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_38.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_2.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_13.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_39.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_10.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_40.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_41.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_42.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_43.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_44.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_45.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_0.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 0", None))
        self.label_15.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_16.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_0.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_11.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_18.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_8.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_20.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_23.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_21.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_24.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_22.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_25.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_7.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 7", None))
        self.label_91.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_92.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_7.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_93.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_94.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_15.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_95.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_96.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_97.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_98.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_99.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_100.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_8.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 8", None))
        self.label_101.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_102.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_8.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_103.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_104.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_16.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_105.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_106.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_107.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_108.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_109.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_110.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_4.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 4", None))
        self.label_57.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_58.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_4.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_60.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_61.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_12.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_62.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_63.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_64.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_65.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_66.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_67.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_10.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 10", None))
        self.label_121.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_122.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_10.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_123.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_124.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_18.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_125.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_126.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_127.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_128.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_129.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_130.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_16.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 16", None))
        self.label_181.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_182.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_16.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_183.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_184.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_24.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_185.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_186.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_187.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_188.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_189.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_190.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_5.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 5", None))
        self.label_69.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_70.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_5.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_71.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_72.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_13.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_73.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_74.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_75.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_76.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_77.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_78.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_18.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 18", None))
        self.label_201.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_202.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_18.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_203.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_204.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_26.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_205.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_206.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_207.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_208.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_209.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_210.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.gpBand_19.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Band 19", None))
        self.label_211.setText(QCoreApplication.translate("CaseCtrlTool", u"Attack", None))
        self.label_212.setText(QCoreApplication.translate("CaseCtrlTool", u"Release", None))
        self.gpLimitBand_19.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Limit", None))
        self.label_213.setText(QCoreApplication.translate("CaseCtrlTool", u"Threshold", None))
        self.label_214.setText(QCoreApplication.translate("CaseCtrlTool", u"Knee", None))
        self.groupBox_27.setTitle(QCoreApplication.translate("CaseCtrlTool", u"dBSPL", None))
        self.label_215.setText(QCoreApplication.translate("CaseCtrlTool", u"S\uff1a", None))
        self.label_216.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_217.setText(QCoreApplication.translate("CaseCtrlTool", u"M\uff1a", None))
        self.label_218.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.label_219.setText(QCoreApplication.translate("CaseCtrlTool", u"L\uff1a", None))
        self.label_220.setText(QCoreApplication.translate("CaseCtrlTool", u"Gain\uff1a", None))
        self.tabWidgetCtrl.setTabText(self.tabWidgetCtrl.indexOf(self.tabWDRC), QCoreApplication.translate("CaseCtrlTool", u"WDRC", None))
        self.groupLog.setTitle(QCoreApplication.translate("CaseCtrlTool", u"Log", None))
        self.menu.setTitle(QCoreApplication.translate("CaseCtrlTool", u"\u83dc\u5355", None))
    # retranslateUi

