import numpy as np
import struct

def generate_sine_wave_audio():
    """
    生成1KHz正弦波音频信号
    采样率: 16KHz, 16bit, 双声道, 一个周期
    """
    # 参数设置
    frequency = 1000        # 1KHz
    sample_rate = 16000     # 16KHz采样率
    bit_depth = 16          # 16位
    channels = 2            # 双声道
    
    # 计算一个周期需要的采样点数
    samples_per_cycle = sample_rate // frequency  # 16000//1000 = 16个采样点
    
    # 生成时间轴（一个周期）
    t = np.linspace(0, 1/frequency, samples_per_cycle, endpoint=False)
    
    # 生成正弦波（浮点数，范围-1到1）
    sine_wave = np.sin(2 * np.pi * frequency * t)
    
    # 转换为16bit整数（-32767 到 32767）
    max_amplitude = 2**(bit_depth-1) - 1  # 32767
    
    # 应用-6dB增益衰减
    # -6dB = 20*log10(gain) => gain = 10^(-6/20) ≈ 0.5012
    gain_6db = 10**(-6/20)  # 约等于 0.5012
    
    sine_wave_int16 = np.round(sine_wave * max_amplitude * gain_6db).astype(np.int16)
    
    # 创建双声道数据：左右声道交替排列 [L, R, L, R, ...]
    stereo_data = np.zeros(samples_per_cycle * channels, dtype=np.int16)
    stereo_data[0::2] = sine_wave_int16  # 左声道（偶数索引）
    stereo_data[1::2] = sine_wave_int16  # 右声道（奇数索引）
    
    return {
        'audio_data': stereo_data,
        'mono_data': sine_wave_int16,
        'float_data': sine_wave,
        'frequency': frequency,
        'sample_rate': sample_rate,
        'bit_depth': bit_depth,
        'channels': channels,
        'gain_db': -6,
        'gain_linear': gain_6db,
        'samples_per_cycle': samples_per_cycle
    }

# 生成音频数据
result = generate_sine_wave_audio()
audio_array = result['audio_data']
mono_array = result['mono_data']

# 显示结果信息
print("=== 1KHz正弦波音频数据 (-6dB增益) ===")
print(f"频率: {result['frequency']} Hz")
print(f"采样率: {result['sample_rate']} Hz")
print(f"位深: {result['bit_depth']} bit")
print(f"声道数: {result['channels']}")
print(f"增益: {result['gain_db']} dB (线性倍数: {result['gain_linear']:.4f})")
print(f"一个周期采样点数: {result['samples_per_cycle']}")
print(f"双声道数组长度: {len(audio_array)} (包含双声道数据)")
print(f"单声道数组长度: {len(mono_array)}")
print(f"数据类型: {audio_array.dtype}")
print(f"取值范围: {audio_array.min()} 到 {audio_array.max()}")
print(f"理论最大值: {int(32767 * result['gain_linear'])}")

# 显示前几个采样点的值
print("\n前8个采样点的值 (双声道交替):")
for i in range(min(8, len(audio_array))):
    channel = "L" if i % 2 == 0 else "R"
    sample_num = i // 2
    print(f"样本 {sample_num} {channel}声道: {audio_array[i]}")

# 显示单声道原始数据
print(f"\n单声道原始数据 (16个采样点):")
print(mono_array)

print(f"\n双声道完整数据 (32个值, 左右声道交替):")
print(audio_array)

# 转换为Python列表格式（如果需要）
audio_list = audio_array.tolist()
mono_list = mono_array.tolist()

print(f"\n作为Python列表:")
print(f"双声道列表: {audio_list}")
print(f"单声道列表: {mono_list}")

# 可选：保存为WAV文件的字节数据
def to_wav_bytes(audio_data, sample_rate=16000, channels=2):
    """将音频数据转换为WAV文件字节格式"""
    # 这里只返回PCM数据部分，不包含WAV文件头
    return audio_data.tobytes()

wav_bytes = to_wav_bytes(audio_array)
print(f"\nWAV字节数据长度: {len(wav_bytes)} bytes")

# C语言格式输出WAV字节数据
print(f"\n// WAV PCM字节数据 (小端序)")
print(f"uint8_t wav_pcm_data[{len(wav_bytes)}] = {{")
# 每行16个字节值，以十六进制显示
bytes_per_line = 16
for i in range(0, len(wav_bytes), bytes_per_line):
    line_bytes = wav_bytes[i:i+bytes_per_line]
    formatted_bytes = [f"0x{byte:02X}" for byte in line_bytes]
    if i + bytes_per_line >= len(wav_bytes):
        print("    " + ", ".join(formatted_bytes))
    else:
        print("    " + ", ".join(formatted_bytes) + ",")
print("};")

# 也提供十进制格式
print(f"\n// WAV PCM字节数据 (十进制格式)")
print(f"uint8_t wav_pcm_data_decimal[{len(wav_bytes)}] = {{")
for i in range(0, len(wav_bytes), bytes_per_line):
    line_bytes = wav_bytes[i:i+bytes_per_line]
    formatted_bytes = [f"{byte:3d}" for byte in line_bytes]
    if i + bytes_per_line >= len(wav_bytes):
        print("    " + ", ".join(formatted_bytes))
    else:
        print("    " + ", ".join(formatted_bytes) + ",")
print("};")

# 字节数据说明
print(f"\n// 字节数据说明:")
print(f"// - 每个16bit采样由2个字节组成 (小端序)")
print(f"// - 双声道数据交替排列: L_low, L_high, R_low, R_high, ...")
print(f"// - 总共 {result['samples_per_cycle']} 个采样点 × 2 声道 × 2 字节 = {len(wav_bytes)} 字节")

# 验证数据完整性
print(f"\n=== 数据验证 ===")
print(f"理论周期长度: {1000/result['frequency']:.1f} ms")
print(f"实际采样点数: {result['samples_per_cycle']}")
print(f"采样间隔: {1000/result['sample_rate']:.3f} ms")
print(f"周期完整性: {'✓' if result['samples_per_cycle'] == result['sample_rate']//result['frequency'] else '✗'}")
print(f"增益验证: -6dB = {20*np.log10(result['gain_linear']):.1f} dB ✓")
print(f"峰值衰减: {32767} → {int(32767 * result['gain_linear'])} (衰减 {100*(1-result['gain_linear']):.1f}%)")