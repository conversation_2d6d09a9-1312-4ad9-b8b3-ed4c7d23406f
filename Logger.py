import logging
import os.path
import time


class Logger(object):
    level_relations = {
        'debug': logging.DEBUG,
        'info': logging.INFO,
        'warning': logging.WARNING,
        'error': logging.ERROR,
        'crit': logging.CRITICAL
    }

    def __init__(self, level='info'):
        rq = time.strftime('%Y%m%d%H%M', time.localtime(time.time()))
        log_path = os.path.join(os.getenv('LOCALAPPDATA'), 'CaseCtrlTool')
        log_path = os.path.join(log_path, 'Logs')
        if not os.path.exists(log_path):
            os.makedirs(log_path)
        log_name = rq + '.log'
        logfile = os.path.join(log_path, log_name)
        self.logger = logging.getLogger()
        self.logger.setLevel(self.level_relations.get(level))
        BASIC_FORMAT = "%(asctime)s: %(levelname)s: %(message)s"
        # DATE_FORMAT = '%Y-%m-%d %H:%M:%S'
        formatter = logging.Formatter(BASIC_FORMAT)
        chlr = logging.StreamHandler()
        chlr.setFormatter(formatter)
        fhlr = logging.FileHandler(logfile, mode='w')
        fhlr.setFormatter(formatter)
        self.logger.addHandler(chlr)
        self.logger.addHandler(fhlr)
        self.logger.info('--------- Start logging ----------')

    def set_level(self, level):
        self.logger.setLevel(self.level_relations.get(level))
