import sys
import asyncio
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
from bleak import BleakScanner, BleakClient
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QPushButton, QTextEdit, QListWidget,
                               QLabel, QMessageBox, QProgressBar, QListWidgetItem,
                               QCheckBox, QComboBox, QFrame, QToolBar)
from PySide6.QtCore import Qt, QThread, Signal, QTimer, QSize
from PySide6.QtGui import QPalette, QColor, QFont, QPainter, QPainterPath
from typing import Dict, Optional, Any
from ThemeManager import ThemeManager, FluentCard
from BleWorker import BleWorker
from BaseSubMainWindow import BaseResource, BaseSubWindow


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler('ble_manager.log', maxBytes=5*1024*1024, backupCount=2),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MainWindow(BaseSubWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()
        self.theme_manager = ThemeManager()
        self.setWindowTitle("BLE设备管理器")
        self.setMinimumSize(1000, 700)

        # 应用当前主题
        self.theme_manager.set_current_theme('fluent')
        self.apply_theme()

        # 状态变量
        self.current_char_handle: Optional[int] = None
        self.known_devices: Dict[str, dict] = {}
        self.connected_device: Optional[dict] = None
        self._last_data: Optional[bytes] = None
        self._is_subscribed: bool = False

        # 初始化BLE工作线程
        self.ble_worker = BleWorker()
        self._setup_worker_connections()

        self.setup_ui()
        self.closeEvent = self.handle_close
        self.ble_worker.start()

    def _setup_worker_connections(self):
        """设置工作线程信号连接"""
        self.ble_worker.eventLoopStarted.connect(self.on_event_loop_started)
        self.ble_worker.deviceFound.connect(self.add_device)
        self.ble_worker.connectionStatus.connect(self.update_connection_status)
        self.ble_worker.characteristicUpdated.connect(self.update_characteristic)
        self.ble_worker.servicesDiscovered.connect(self.update_services)
        self.ble_worker.scanComplete.connect(self.scan_complete)
        self.ble_worker.writeComplete.connect(self.handle_write_complete)
        self.ble_worker.readComplete.connect(self.handle_read_complete)
        self.ble_worker.disconnectComplete.connect(self.handle_disconnect_complete)

    def apply_theme(self):
        """应用当前主题"""
        self.setStyleSheet(self.theme_manager.get_current_theme())

    def toggle_theme(self):
        """切换主题"""
        self.theme_manager.toggle_theme()
        self.apply_theme()

    def setup_ui(self):
        """设置UI界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        '''
        # 添加主题切换按钮到工具栏
        toolbar = QToolBar()
        self.addToolBar(toolbar)

        theme_button = QPushButton()
        # theme_button.setIcon(QIcon(":/icons/theme.png"))  # 需要添加主题图标
        theme_button.setToolTip("切换主题")
        theme_button.clicked.connect(self.toggle_theme)
        toolbar.addWidget(theme_button)
        '''

        # 主内容布局
        content_layout = QHBoxLayout()
        content_layout.setSpacing(16)
        content_layout.setContentsMargins(16, 16, 16, 16)

        # 左侧面板
        left_card = FluentCard()
        left_layout = QVBoxLayout()
        left_layout.setSpacing(12)

        # 扫描控制区
        scan_container = QWidget()
        scan_layout = QHBoxLayout(scan_container)
        scan_layout.setContentsMargins(0, 0, 0, 0)

        self.scan_button = QPushButton("扫描设备")
        self.scan_button.setMinimumHeight(32)
        self.scan_button.clicked.connect(self.start_scan)

        self.progress_bar = QProgressBar()
        self.progress_bar.hide()

        scan_layout.addWidget(self.scan_button)
        scan_layout.addWidget(self.progress_bar)

        left_layout.addWidget(scan_container)

        self.auto_refresh = QCheckBox("自动刷新信号强度")
        self.auto_refresh.setChecked(False)
        self.auto_refresh.stateChanged.connect(self.toggle_auto_refresh)
        left_layout.addWidget(self.auto_refresh)

        devices_label = QLabel("发现的设备")
        devices_label.setProperty("title", True)
        left_layout.addWidget(devices_label)

        self.device_list = QListWidget()
        self.device_list.setSpacing(2)
        self.device_list.itemDoubleClicked.connect(self.connect_device)
        left_layout.addWidget(self.device_list)

        left_card.layout.addLayout(left_layout)

        # 右侧面板
        right_card = FluentCard()
        right_layout = QVBoxLayout()
        right_layout.setSpacing(12)

        # 状态栏
        status_layout = QHBoxLayout()
        self.status_label = QLabel("未连接")
        self.status_label.setStyleSheet("color: #666666;")

        self.disconnect_button = QPushButton("断开连接")
        self.disconnect_button.clicked.connect(self.disconnect_device)
        self.disconnect_button.setEnabled(False)
        self.disconnect_button.setStyleSheet("""
            QPushButton {
                color: #d13438;
                border-color: #d13438;
            }
            QPushButton:hover {
                background-color: #fdf3f4;
            }
            QPushButton:pressed {
                background-color: #fde7e9;
            }
        """)

        status_layout.addWidget(self.status_label)
        status_layout.addWidget(self.disconnect_button)
        right_layout.addLayout(status_layout)

        services_label = QLabel("服务和特征值")
        services_label.setProperty("title", True)
        right_layout.addWidget(services_label)

        self.characteristic_list = QListWidget()
        self.characteristic_list.itemClicked.connect(self.select_characteristic)
        right_layout.addWidget(self.characteristic_list)

        # 数据显示区域
        data_container = QWidget()
        data_layout = QVBoxLayout(data_container)
        data_layout.setContentsMargins(0, 0, 0, 0)
        data_layout.setSpacing(6)

        data_header = QHBoxLayout()
        data_label = QLabel("接收的数据")
        data_label.setProperty("title", True)
        data_header.addWidget(data_label)

        self.data_format = QComboBox()
        self.data_format.addItems(["HEX", "ASCII"])
        self.data_format.currentTextChanged.connect(self.update_data_display)

        format_container = QWidget()
        format_layout = QHBoxLayout(format_container)
        format_layout.setContentsMargins(0, 0, 0, 0)
        format_layout.addWidget(QLabel("数据格式:"))
        format_layout.addWidget(self.data_format)

        data_header.addWidget(format_container)
        data_header.addStretch()

        self.clear_button = QPushButton("清除")
        self.clear_button.clicked.connect(self.clear_data_display)
        data_header.addWidget(self.clear_button)

        data_layout.addLayout(data_header)

        self.data_display = QTextEdit()
        self.data_display.setReadOnly(True)
        self.data_display.setMinimumHeight(100)
        data_layout.addWidget(self.data_display)

        right_layout.addWidget(data_container)

        # 数据发送区域
        send_label = QLabel("发送数据 (十六进制)")
        send_label.setProperty("title", True)
        right_layout.addWidget(send_label)

        self.data_input = QTextEdit()
        self.data_input.setMaximumHeight(80)
        right_layout.addWidget(self.data_input)

        # 操作按钮
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(8)

        self.read_button = QPushButton("读取数据")
        self.read_button.clicked.connect(self.read_data)
        self.read_button.setEnabled(False)

        self.send_button = QPushButton("发送数据")
        self.send_button.clicked.connect(self.send_data)
        self.send_button.setEnabled(False)

        self.subscribe_button = QPushButton("订阅通知")
        self.subscribe_button.clicked.connect(self.subscribe_characteristic)
        self.subscribe_button.setEnabled(False)

        button_layout.addWidget(self.read_button)
        button_layout.addWidget(self.send_button)
        button_layout.addWidget(self.subscribe_button)

        right_layout.addWidget(button_container)

        # 日志显示
        log_label = QLabel("日志")
        log_label.setProperty("title", True)
        right_layout.addWidget(log_label)

        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #f9f9f9;
                font-family: "Cascadia Code", Consolas, monospace;
                font-size: 12px;
                padding: 6px;
            }
        """)
        right_layout.addWidget(self.log_display)

        right_card.layout.addLayout(right_layout)

        content_layout.addWidget(left_card)
        content_layout.addWidget(right_card, stretch=2)
        main_layout.addLayout(content_layout)


    def on_event_loop_started(self):
        pass

    def add_device(self, device):
        """添加或更新设备到列表"""
        device_info = {
            'name': device.name,
            'address': device.address,
            'rssi': device.rssi
        }

        self.known_devices[device.address] = device_info
        self.update_device_list()

    def update_device_list(self):
        """更新设备列表显示"""
        current_item = self.device_list.currentItem()
        current_address = current_item.data(Qt.UserRole)['address'] if current_item else None

        self.device_list.clear()

        sorted_devices = sorted(
            self.known_devices.values(),
            key=lambda x: (x['rssi'] or -100),
            reverse=True
        )

        for device_info in sorted_devices:
            rssi = device_info['rssi'] or 0
            item_text = (f"{device_info['name']} ({device_info['address']})\n"
                         f"信号强度: {rssi} dBm")
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, device_info)
            item.setSizeHint(QSize(item.sizeHint().width(), 45))
            self.device_list.addItem(item)

            if device_info['address'] == current_address:
                self.device_list.setCurrentItem(item)

    def start_scan(self):
        """开始扫描设备"""
        self.device_list.clear()
        self.known_devices.clear()
        self.scan_button.setEnabled(False)
        self.progress_bar.setRange(0, 0)
        self.progress_bar.show()
        self.log_display.append("开始扫描设备...")

        self.ble_worker.enqueue_command("scan", None)

    def scan_complete(self):
        """扫描完成"""
        self.scan_button.setEnabled(True)
        self.progress_bar.hide()
        self.log_display.append(f"扫描完成，发现 {self.device_list.count()} 个设备")

    def toggle_auto_refresh(self, state):
        """切换自动刷新状态"""
        if state:
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()

    def start_auto_refresh(self):
        """开始自动刷新"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_devices)
        self.refresh_timer.start(2000)

    def stop_auto_refresh(self):
        """停止自动刷新"""
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()

    def refresh_devices(self):
        """刷新设备信号强度"""
        if not self.scan_button.isEnabled():
            return

        self.ble_worker.enqueue_command("scan", None)

    def connect_device(self, item):
        """连接选中的设备"""
        device = item.data(Qt.UserRole)
        self.connected_device = device
        self.ble_worker.enqueue_command("connect", device)

    def disconnect_device(self):
        """断开设备连接"""
        self.ble_worker.enqueue_command("disconnect", None)
        self.disconnect_button.setEnabled(False)
        self.read_button.setEnabled(False)
        self.send_button.setEnabled(False)
        self.subscribe_button.setEnabled(False)

    def handle_disconnect_complete(self, success, message):
        """处理断开连接完成信号"""
        if success:
            self.status_label.setText("未连接")
            self.characteristic_list.clear()
            self.connected_device = None
            self.current_char_handle = None
            self.scan_button.setEnabled(True)
            self.clear_data_display()
        else:
            self.status_label.setText(f"错误: {message}")
            self.disconnect_button.setEnabled(True)
            QMessageBox.warning(self, "断开连接失败", message)

    def update_connection_status(self, connected, message):
        """更新连接状态"""
        if connected:
            device_name = self.connected_device['name']
            self.status_label.setText(f"已连接到 {device_name}")
            self.disconnect_button.setEnabled(True)
            self.scan_button.setEnabled(False)
        else:
            self.status_label.setText(f"连接失败: {message}")
            self.disconnect_button.setEnabled(False)
            self.send_button.setEnabled(False)
            self.subscribe_button.setEnabled(False)
            self.read_button.setEnabled(False)
            self.connected_device = None

    def update_services(self, services):
        """更新服务和特征值列表"""
        self.characteristic_list.clear()
        for service in services:
            service_item = QListWidgetItem(f"服务: {service['uuid']}")
            service_item.setFlags(Qt.ItemIsEnabled)
            self.characteristic_list.addItem(service_item)

            for char in service['characteristics']:
                props = []
                if "read" in char['properties']:
                    props.append("读")
                if "write" in char['properties']:
                    props.append("写")
                if "notify" in char['properties']:
                    props.append("通知")

                char_text = f"  特征值: {char['uuid']} [{', '.join(props)}]"
                char_item = QListWidgetItem(char_text)
                char_item.setData(Qt.UserRole, char)
                self.characteristic_list.addItem(char_item)

        self.read_button.setEnabled(True)
        self.send_button.setEnabled(True)
        self.subscribe_button.setEnabled(True)

    def select_characteristic(self, item):
        """选择特征值"""
        char_data = item.data(Qt.UserRole)
        if char_data:
            self.current_char_handle = char_data['handle']
            self.send_button.setEnabled("write" in char_data['properties'])
            self.read_button.setEnabled("read" in char_data['properties'])
            self.subscribe_button.setEnabled("notify" in char_data['properties'])

    def update_characteristic(self, char_uuid, data):
        """更新特征值数据"""
        self.log_display.append(f"收到数据 ({char_uuid}): {data.hex()}")
        self._last_data = data
        self.update_data_display()

    def clear_data_display(self):
        """清除数据显示区域"""
        self.data_display.clear()
        self._last_data = None

    def update_data_display(self, format_type=None):
        """更新数据显示格式"""
        if not hasattr(self, '_last_data') or self._last_data is None:
            return

        data = self._last_data
        if format_type is None:
            format_type = self.data_format.currentText()

        try:
            if format_type == "HEX":
                display_text = data.hex(' ')
            else:
                display_text = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data)

            self.data_display.append(display_text)

        except Exception as e:
            logger.error(f"格式化数据时出错: {str(e)}")

    def read_data(self):
        """读取特征值数据"""
        if not self.current_char_handle:
            QMessageBox.warning(self, "错误", "请先选择一个特征值")
            return

        self.read_button.setEnabled(False)
        self.status_label.setText("正在读取数据...")

        self.ble_worker.enqueue_command("read", self.current_char_handle)

    def handle_read_complete(self, success, message, data):
        """处理读取完成信号"""
        self.read_button.setEnabled(True)
        if success:
            self.status_label.setText(message)
            self.log_display.append(f"↓ 读取数据: {data.hex()}")
            self._last_data = data
            self.update_data_display()
        else:
            self.status_label.setText(f"错误: {message}")
            self.log_display.append(f"✗ {message}")
            QMessageBox.warning(self, "读取失败", message)

    def send_data(self):
        """发送数据到设备"""
        if not self.current_char_handle:
            QMessageBox.warning(self, "错误", "请先选择一个特征值")
            return

        try:
            self.send_button.setEnabled(False)
            self.status_label.setText("正在发送数据...")

            data = bytes.fromhex(self.data_input.toPlainText().strip())
            logger.info(f"发送数据: {data.hex()}")
            self.ble_worker.enqueue_command("write", (self.current_char_handle, data))

        except ValueError:
            QMessageBox.warning(self, "错误", "请输入有效的十六进制数据")
            self.send_button.setEnabled(True)

    def handle_write_complete(self, success, message):
        """处理写入完成信号"""
        self.send_button.setEnabled(True)
        if success:
            self.status_label.setText(message)
            self.log_display.append(f"✓ {message}")
        else:
            self.status_label.setText(f"错误: {message}")
            self.log_display.append(f"✗ {message}")
            QMessageBox.warning(self, "写入失败", message)

    def subscribe_characteristic(self):
        """订阅特征值通知"""
        if not self.current_char_handle:
            QMessageBox.warning(self, "错误", "请先选择一个特征值")
            return

        if not self._is_subscribed:
            self.ble_worker.enqueue_command("subscribe", self.current_char_handle)
            self.subscribe_button.setText("取消订阅")
            self._is_subscribed = True
        else:
            self.ble_worker.enqueue_command("unsubscribe", self.current_char_handle)
            self.subscribe_button.setText("订阅通知")
            self._is_subscribed = False

    def handle_close(self, event):
        """处理窗口关闭事件"""
        try:
            if self.ble_worker:
                if self.ble_worker.client and self.ble_worker.client.is_connected:
                    # 先断开连接
                    self.ble_worker.enqueue_command("disconnect", None)
                    # 等待断开操作完成，可以增加一个事件来通知断开完成
                    self.ble_worker.wait(2000)
                # 先尝试正常关闭
                self.ble_worker.handle_close()
                # 等待线程结束
                if not self.ble_worker.wait(3000):  # 增加等待时间到3秒
                    logger.warning("BLE工作线程未能在规定时间内正常退出，准备强制终止")
                    # 如果等待超时，强制终止线程
                    self.ble_worker.terminate()
                    # 再次等待一小段时间确保线程被终止
                    self.ble_worker.wait(1000)
        except Exception as e:
            logger.error(f"关闭窗口时出错: {str(e)}")
        finally:
            event.accept()  # 确保窗口始终能够关闭


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())