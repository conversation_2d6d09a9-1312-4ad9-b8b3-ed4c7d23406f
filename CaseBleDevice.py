import os
import logging
import re
from typing import Optional, List
from PySide6.QtCore import QTime, QThread, QTimer
from PySide6.QtWidgets import QApplication
from BleWorker import BleWorker
import pyDes
import binascii
from Crypto.Cipher import AES

YM_HIFI_RAW_MSG_CMD_GET_FLASH_UID = 0x01
YM_HIFI_RAW_MSG_CMD_SET_CRYPT_KEY = 0x02
YM_HIFI_RAW_MSG_CMD_GET_CRYPT_STATE = 0x03
YM_HIFI_RAW_MSG_CMD_GET_VERSION = 0x04

YM_M55_RAW_MSG_CMD_GET_VERSION = 0x01
YM_M55_RAW_MSG_CMD_GET_SWEEP_FREQ_ENABLE = 0x24
YM_M55_RAW_MSG_CMD_SET_SWEEP_FREQ = 0x25


class CaseBleDevice(BleWorker):
    # Device service and characteristic UUIDs
    DEV_SERVICE_UUID = "0000fc83-0000-1000-8000-00805f9b34fb"
    DEV_CHAR_WRITE_UUID = "636f6d2e-6a69-7561-6e2e-************"
    DEV_CHAR_NOTIFY_UUID = "636f6d2e-6a69-7561-6e2e-************"

    def __init__(self):
        super().__init__()
        self.devCharacteristicWrite = None
        self.devCharacteristicNotify = None
        self._command_response = None
        self._command_completed = False
        self._current_cmd = None
        self.leftOrRight = 0x01

    @staticmethod
    def convert_params(byte_list: List[int]) -> List[int]:
        """Convert parameters to the required format."""
        groups = [byte_list[i:i + 4] for i in range(0, len(byte_list), 4)]

        result = []
        for group in groups:
            reversed_group = group[::-1]  # Reverse byte order (little endian)
            hex_str = "".join(f"{b:02x}" for b in reversed_group)
            result.append(f"0x{hex_str}")

        str_result = ",".join(result)
        return list(str_result.encode("utf-8"))

    def convert_cmd_to_bytes(self, cmd_str: str, params: Optional[List[int]] = None) -> bytes:
        """Convert a command string and parameters to the required byte format."""
        if params is None:
            params = []

        cmd_bytes = cmd_str.encode("utf-8")
        cmd_length = len(cmd_bytes) + 2  # 2 fixed bytes (0xD0 and 0x01)

        if params:
            cmd_length += 1 + len(params)

        result = [
            0xA0,  # Command code
            cmd_length,  # Length (excluding first two bytes and checksum)
            0xD0,  # Fixed byte 1
            self.leftOrRight,  # Fixed byte 2
        ]

        result.extend(cmd_bytes)

        if params:
            result.append(0x7C)
            result.extend(params)

        # Calculate checksum (from third byte to end)
        checksum = sum(result[2:]) & 0xFF
        result.append(checksum)

        return bytes(result)

    def parse_bytes(self, data_bytes: bytes) -> Optional[List[int]]:
        """Parse received byte data according to the protocol."""
        byte_list = list(data_bytes)

        if (len(byte_list) < 4 or
                byte_list[0] != 0xB0 or
                byte_list[2] != 0xD0 or
                byte_list[3] != 0x11):
            return None

        data_length = byte_list[1]
        if data_length + 3 > len(byte_list):
            return None

        checksum_received = byte_list[-1]
        checksum_calculated = sum(byte_list[2:-1]) % 256
        if checksum_calculated != checksum_received:
            return None

        data_start = 4
        data_end = data_start + data_length - 2
        return byte_list[data_start:data_end]

    @staticmethod
    def extract_uuid(s: str) -> Optional[str]:
        """Extract UUID from a string."""
        pattern = r"[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"
        match = re.search(pattern, s)
        return match.group() if match else None

    def update_services(self, services):
        """Update services and characteristics after connection."""
        self.devCharacteristicWrite = None
        self.devCharacteristicNotify = None
        self._is_subscribed = False

        for service in services:
            # logging.info('service: %s', service["uuid"])
            if service["uuid"] != self.DEV_SERVICE_UUID:
                continue

            for char in service["characteristics"]:
                # logging.info('char: %s, %s', char["uuid"], char["properties"])
                if (char["uuid"] == self.DEV_CHAR_WRITE_UUID and
                        ("write" in char["properties"] or 'write-without-response' in char["properties"])):
                    self.devCharacteristicWrite = char
                    self.current_char_handle = char["handle"]

                if (char["uuid"] == self.DEV_CHAR_NOTIFY_UUID and
                        "notify" in char["properties"]):
                    self.devCharacteristicNotify = char
                    self.enqueue_command("subscribe", char["handle"])
                    self._is_subscribed = True

        return bool(self.devCharacteristicWrite and self.devCharacteristicNotify)

    def handle_characteristic_update(self, char_uuid: str, data: bytes):
        """Handle characteristic update (notification)."""
        logging.debug(f"Received data ({char_uuid}): {data.hex()}")

        uuid = self.extract_uuid(char_uuid)
        if not uuid or uuid != self.DEV_CHAR_NOTIFY_UUID:
            logging.error("No valid UUID found in the string")
            return

        result = self.parse_bytes(data)
        if result is not None:
            logging.debug(f"Parsed notification data: {result}")
            if self._current_cmd:
                self._command_response = result
                self._command_completed = True
        else:
            logging.error("Invalid notification data format")

    def send_command(self, cmd: str, params: Optional[List[int]] = None,
                     max_retries: int = 1, retry_interval: float = 0.5,
                     callback=None, blocking: bool = False) -> Optional[tuple[bool, Optional[List[int]]]]:
        """
        Send BLE command with support for both blocking and non-blocking execution.

        Args:
            cmd: Command string to send
            params: Optional parameters for the command
            max_retries: Number of retries on failure
            retry_interval: Interval between retries in seconds
            callback: Callback function to call with (success: bool, response: Optional[List[int]])
            blocking: If True, blocks until command completes. If False, executes asynchronously.

        Returns:
            If blocking is True, returns tuple (success: bool, response: Optional[List[int]])
            If blocking is False, returns None and uses callback for result
        """
        if not self.devCharacteristicWrite:
            logging.error("BLE device not connected!")
            if callback:
                callback(False, None)
            return (False, None) if blocking else None

        def attempt_send(attempt: int):
            cmd_bytes = self.convert_cmd_to_bytes(cmd, params)
            logging.info("Sending BLE command: %s, params: %s", cmd, params)
            logging.debug("Command bytes: %s", " ".join([f"0x{b:02X}" for b in cmd_bytes]))

            result = {"success": False, "response": None}
            completed = {"value": False}

            def check_completion():
                nonlocal attempt
                if self._command_completed:
                    if self._command_response is not None:
                        logging.debug("Command executed successfully")
                        result["success"] = True
                        result["response"] = self._command_response
                        if callback:
                            callback(True, self._command_response)
                        completed["value"] = True
                    else:
                        handle_failure()

                elif start_time.msecsTo(QTime.currentTime()) > timeout_ms:
                    logging.warning("Command execution timed out")
                    handle_failure()
                else:
                    # Continue checking
                    QThread.msleep(10)
                    if not completed["value"]:
                        QTimer.singleShot(0, check_completion)

            def handle_failure():
                nonlocal attempt
                logging.warning(f"No valid response received, attempt {attempt + 1}/{max_retries}")
                if attempt < max_retries - 1:
                    logging.info(f"Waiting {retry_interval} seconds before retry...")
                    # Schedule next retry
                    QTimer.singleShot(int(retry_interval * 1000),
                                      lambda: attempt_send(attempt + 1))
                else:
                    logging.error("Command execution failed after all retries")
                    if callback:
                        callback(False, None)
                    result["success"] = False
                    result["response"] = None
                    completed["value"] = True

            try:
                self._command_completed = False
                self._command_response = None
                self._current_cmd = cmd

                logging.debug(f"Attempting to send command ({attempt + 1}/{max_retries})")
                self.enqueue_command(
                    "write",
                    (self.devCharacteristicWrite["handle"], cmd_bytes)
                )

                # Start completion check timer
                timeout_ms = 2000  # 2 second timeout
                start_time = QTime.currentTime()
                QTimer.singleShot(0, check_completion)

                if blocking:
                    # Process events until command completes
                    while not completed["value"]:
                        QApplication.processEvents()
                        QThread.msleep(10)
                    return result["success"], result["response"]

            except Exception as e:
                logging.error(f"Command execution error (attempt {attempt + 1}/{max_retries}): {str(e)}")
                handle_failure()
                if blocking:
                    return False, None

        # Start first attempt
        if blocking:
            return attempt_send(0)
        else:
            attempt_send(0)
            return None

    def aidsXferHifiRaw(self, rawXferData: List[int]) -> Optional[List[int]]:
        cmdStr = "xfer_hifi_raw"
        # params = self.convert_params(rawXferData)
        params = rawXferData
        state, response = self.send_command(cmdStr, params=params, blocking=True)
        if state and response:
            return response
        else:
            return None

    def aidsXferM55Raw(self, rawXferData: List[int]) -> Optional[List[int]]:
        cmdStr = "xfer_m55_raw"
        # params = self.convert_params(rawXferData)
        params = rawXferData
        state, response = self.send_command(cmdStr, params=params, blocking=True)
        if state and response:
            return response
        else:
            return None

    def getFlashUID(self) -> Optional[list]:
        cmd = YM_HIFI_RAW_MSG_CMD_GET_FLASH_UID
        rawXferData = [cmd]
        resData = self.aidsXferHifiRaw(rawXferData)
        if resData is None:
            logging.error("Failed to retrieve Flash UID!")
            return None
        if resData[0] != cmd and resData[1] != 0x00:
            logging.error("Failed to paras Flash UID!")
            return None
        flash_uid_len = resData[2]
        flash_uid = resData[3:3 + flash_uid_len]
        # str_flash_uid = "".join(f"{b:02x}" for b in flash_uid)
        # logging.info(f"Flash UID: {str_flash_uid}")
        return flash_uid

    def gencCryptData(self, flash_uid):
        flashUIDBytes = bytes(flash_uid)
        aesKey = os.urandom(16)

        # logging.info('Flash UID: %s, AES Key: %s', binascii.b2a_hex(flashUIDBytes), binascii.b2a_hex(aesKey))

        desKey = aesKey[:8][::-1]
        des = pyDes.des(desKey, pyDes.ECB, desKey, pad=None, padmode=pyDes.PAD_NORMAL)
        desData0 = des.encrypt(flashUIDBytes[:8][::-1])
        desKey = aesKey[8:][::-1]
        des = pyDes.des(desKey, pyDes.ECB, desKey, pad=None, padmode=pyDes.PAD_NORMAL)
        desData1 = des.encrypt(flashUIDBytes[8:][::-1])
        desData = desData0[::-1] + desData1[::-1]

        aes = AES.new(aesKey, AES.MODE_ECB)
        aesData = aes.encrypt(flashUIDBytes)

        '''
        logging.info('FlashID[0]: %s, [1]: %s', binascii.b2a_hex(flashUIDBytes[:8][::-1]),
                     binascii.b2a_hex(flashUIDBytes[8:][::-1]))
        logging.info('DES: key[0] %s, data[0]: %s, key[1] %s, data[1]: %s',
                     binascii.b2a_hex(aesKey[:8][::-1]), binascii.b2a_hex(desData0),
                     binascii.b2a_hex(aesKey[8:][::-1]), binascii.b2a_hex(desData1))
        logging.info('DES Data: %s, AES Data: %s', binascii.b2a_hex(desData), binascii.b2a_hex(aesData))
        '''

        cryptData = aesData[8:] + aesKey[:8] + aesData[:8] + desData[8:] + aesKey[8:] + desData[:8]
        return cryptData

    def setCryptoKey(self, flash_uid=None, cryptData=None):
        cmd = YM_HIFI_RAW_MSG_CMD_SET_CRYPT_KEY
        if cryptData is None:
            if flash_uid is None:
                logging.error("Failed to set Crypto Key!")
                return False
            cryptData = self.gencCryptData(flash_uid)
        rawXferData = [cmd, len(cryptData)] + list(cryptData)
        resData = self.aidsXferHifiRaw(rawXferData)
        if resData is None:
            logging.error("Failed to set Crypto Key!")
            return False
        if resData[0] != cmd and resData[1] != 0x00:
            logging.error("Failed to set Crypto Key!")
            return False
        return True

    def getCryptState(self):
        cmd = YM_HIFI_RAW_MSG_CMD_GET_CRYPT_STATE
        rawXferData = [cmd]
        resData = self.aidsXferHifiRaw(rawXferData)
        if resData is None:
            logging.error("Failed to get Crypto State!")
            return None
        if resData[0] != cmd and resData[1] != 0x00:
            logging.error("Failed to paras Crypto State!")
            return None
        cryptState = resData[3]
        return cryptState

    def getHifiVersionInfo(self):
        cmd = YM_HIFI_RAW_MSG_CMD_GET_VERSION
        rawXferData = [cmd]
        resData = self.aidsXferHifiRaw(rawXferData)
        if resData is None:
            logging.error("Failed to get Hifi Version info!")
            return None
        if resData[0] != cmd and resData[1] != 0x00:
            logging.error("Failed to paras Hifi Version info!")
            return None
        infoLen = resData[2]
        infoData = resData[3:3+infoLen]
        return infoData

    def getM55VersionInfo(self):
        cmd = YM_M55_RAW_MSG_CMD_GET_VERSION
        rawXferData = [cmd]
        resData = self.aidsXferM55Raw(rawXferData)
        if resData is None:
            logging.error("Failed to get M55 Version info!")
            return None
        if resData[0] != cmd and resData[1] != 0x00:
            logging.error("Failed to paras M55 Version info!")
            return None
        infoLen = resData[2]
        infoData = resData[3:3+infoLen]
        return infoData
    
    def getSweepFreqState(self):
        cmd = YM_M55_RAW_MSG_CMD_GET_SWEEP_FREQ_ENABLE
        rawXferData = [cmd]
        resData = self.aidsXferM55Raw(rawXferData)
        if resData is None:
            logging.error("Failed to get SweepFreq State!")
            return None
        if resData[0] != cmd and resData[1] != 0x00:
            logging.error("Failed to paras SweepFreq State!")
            return None
        return resData[3:]
    
    def setSweepFreqState(self, enable: list, freq: list):
        cmd = YM_M55_RAW_MSG_CMD_SET_SWEEP_FREQ
        rawXferData = [cmd, len(enable) + len(freq)] + enable + freq
        resData = self.aidsXferM55Raw(rawXferData)
        if resData is None:
            logging.error("setSweepFreqState: Failed to set SweepFreq!")
            return False
        if resData[0] != cmd and resData[1] != 0x00:
            logging.error("setSweepFreqState: Failed to paras SweepFreq!")
            return False
        return True

