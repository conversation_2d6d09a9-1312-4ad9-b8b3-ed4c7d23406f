# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'SettingDialog.ui'
##
## Created by: Qt User Interface Compiler version 6.6.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON>rush, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QDialog,
    QDoubleSpinBox, QGridLayout, QGroupBox, QHBoxLayout,
    QHeaderView, QLabel, QLineEdit, QPushButton,
    QSizePolicy, QSpacerItem, QTableWidget, QTableWidgetItem,
    QVBoxLayout, QWidget)

class Ui_DialogSetting(object):
    def setupUi(self, DialogSetting):
        if not DialogSetting.objectName():
            DialogSetting.setObjectName(u"DialogSetting")
        DialogSetting.resize(578, 795)
        font = QFont()
        font.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        DialogSetting.setFont(font)
        DialogSetting.setModal(True)
        self.verticalLayout_3 = QVBoxLayout(DialogSetting)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.gpTestBoxConfig = QGroupBox(DialogSetting)
        self.gpTestBoxConfig.setObjectName(u"gpTestBoxConfig")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.gpTestBoxConfig.sizePolicy().hasHeightForWidth())
        self.gpTestBoxConfig.setSizePolicy(sizePolicy)
        self.gpTestBoxConfig.setMinimumSize(QSize(400, 0))
        font1 = QFont()
        font1.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        font1.setPointSize(10)
        self.gpTestBoxConfig.setFont(font1)
        self.gpTestBoxConfig.setCheckable(False)
        self.horizontalLayout_10 = QHBoxLayout(self.gpTestBoxConfig)
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.gridLayout_3 = QGridLayout()
        self.gridLayout_3.setObjectName(u"gridLayout_3")
        self.horizontalLayout_11 = QHBoxLayout()
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.label_7 = QLabel(self.gpTestBoxConfig)
        self.label_7.setObjectName(u"label_7")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.label_7.sizePolicy().hasHeightForWidth())
        self.label_7.setSizePolicy(sizePolicy1)
        self.label_7.setMinimumSize(QSize(80, 0))
        self.label_7.setFont(font1)

        self.horizontalLayout_11.addWidget(self.label_7)

        self.comboComportBox = QComboBox(self.gpTestBoxConfig)
        self.comboComportBox.setObjectName(u"comboComportBox")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.comboComportBox.sizePolicy().hasHeightForWidth())
        self.comboComportBox.setSizePolicy(sizePolicy2)
        self.comboComportBox.setFont(font1)
        self.comboComportBox.setMaxVisibleItems(16)

        self.horizontalLayout_11.addWidget(self.comboComportBox)


        self.gridLayout_3.addLayout(self.horizontalLayout_11, 0, 0, 1, 1)

        self.horizontalLayout_13 = QHBoxLayout()
        self.horizontalLayout_13.setObjectName(u"horizontalLayout_13")
        self.label_9 = QLabel(self.gpTestBoxConfig)
        self.label_9.setObjectName(u"label_9")
        sizePolicy1.setHeightForWidth(self.label_9.sizePolicy().hasHeightForWidth())
        self.label_9.setSizePolicy(sizePolicy1)
        self.label_9.setMinimumSize(QSize(80, 0))
        self.label_9.setFont(font1)

        self.horizontalLayout_13.addWidget(self.label_9)

        self.comboBaudrateBox = QComboBox(self.gpTestBoxConfig)
        self.comboBaudrateBox.addItem("")
        self.comboBaudrateBox.setObjectName(u"comboBaudrateBox")
        sizePolicy2.setHeightForWidth(self.comboBaudrateBox.sizePolicy().hasHeightForWidth())
        self.comboBaudrateBox.setSizePolicy(sizePolicy2)
        self.comboBaudrateBox.setFont(font1)
        self.comboBaudrateBox.setMaxVisibleItems(16)

        self.horizontalLayout_13.addWidget(self.comboBaudrateBox)


        self.gridLayout_3.addLayout(self.horizontalLayout_13, 0, 1, 1, 1)

        self.checkBoxAutoTest = QCheckBox(self.gpTestBoxConfig)
        self.checkBoxAutoTest.setObjectName(u"checkBoxAutoTest")
        self.checkBoxAutoTest.setFont(font1)
        self.checkBoxAutoTest.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxAutoTest.setChecked(False)

        self.gridLayout_3.addWidget(self.checkBoxAutoTest, 1, 0, 1, 1)


        self.horizontalLayout_10.addLayout(self.gridLayout_3)


        self.verticalLayout_3.addWidget(self.gpTestBoxConfig)

        self.gpCodecFreqParams = QGroupBox(DialogSetting)
        self.gpCodecFreqParams.setObjectName(u"gpCodecFreqParams")
        sizePolicy.setHeightForWidth(self.gpCodecFreqParams.sizePolicy().hasHeightForWidth())
        self.gpCodecFreqParams.setSizePolicy(sizePolicy)
        self.gpCodecFreqParams.setMinimumSize(QSize(400, 0))
        self.gpCodecFreqParams.setFont(font1)
        self.gpCodecFreqParams.setCheckable(True)
        self.verticalLayout_5 = QVBoxLayout(self.gpCodecFreqParams)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.gridLayout = QGridLayout()
        self.gridLayout.setObjectName(u"gridLayout")
        self.btnFreqLimitParams = QPushButton(self.gpCodecFreqParams)
        self.btnFreqLimitParams.setObjectName(u"btnFreqLimitParams")

        self.gridLayout.addWidget(self.btnFreqLimitParams, 3, 0, 1, 1)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.label = QLabel(self.gpCodecFreqParams)
        self.label.setObjectName(u"label")
        sizePolicy1.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy1)
        self.label.setMinimumSize(QSize(80, 0))
        self.label.setFont(font1)

        self.horizontalLayout.addWidget(self.label)

        self.comboBoxFFTSize = QComboBox(self.gpCodecFreqParams)
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.addItem("")
        self.comboBoxFFTSize.setObjectName(u"comboBoxFFTSize")
        sizePolicy2.setHeightForWidth(self.comboBoxFFTSize.sizePolicy().hasHeightForWidth())
        self.comboBoxFFTSize.setSizePolicy(sizePolicy2)
        self.comboBoxFFTSize.setFont(font1)
        self.comboBoxFFTSize.setMaxVisibleItems(16)

        self.horizontalLayout.addWidget(self.comboBoxFFTSize)


        self.gridLayout.addLayout(self.horizontalLayout, 0, 1, 1, 1)

        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.label_5 = QLabel(self.gpCodecFreqParams)
        self.label_5.setObjectName(u"label_5")
        sizePolicy1.setHeightForWidth(self.label_5.sizePolicy().hasHeightForWidth())
        self.label_5.setSizePolicy(sizePolicy1)
        self.label_5.setMinimumSize(QSize(80, 0))
        self.label_5.setFont(font1)

        self.horizontalLayout_8.addWidget(self.label_5)

        self.comboBoxSamplerate = QComboBox(self.gpCodecFreqParams)
        self.comboBoxSamplerate.addItem("")
        self.comboBoxSamplerate.setObjectName(u"comboBoxSamplerate")
        sizePolicy2.setHeightForWidth(self.comboBoxSamplerate.sizePolicy().hasHeightForWidth())
        self.comboBoxSamplerate.setSizePolicy(sizePolicy2)
        self.comboBoxSamplerate.setFont(font1)
        self.comboBoxSamplerate.setMaxVisibleItems(16)

        self.horizontalLayout_8.addWidget(self.comboBoxSamplerate)


        self.gridLayout.addLayout(self.horizontalLayout_8, 0, 0, 1, 1)

        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.checkBoxPlayDevice = QCheckBox(self.gpCodecFreqParams)
        self.checkBoxPlayDevice.setObjectName(u"checkBoxPlayDevice")
        self.checkBoxPlayDevice.setMinimumSize(QSize(80, 0))
        self.checkBoxPlayDevice.setFont(font1)
        self.checkBoxPlayDevice.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxPlayDevice.setChecked(False)

        self.horizontalLayout_9.addWidget(self.checkBoxPlayDevice)

        self.leditPlayDeviceName = QLineEdit(self.gpCodecFreqParams)
        self.leditPlayDeviceName.setObjectName(u"leditPlayDeviceName")
        sizePolicy2.setHeightForWidth(self.leditPlayDeviceName.sizePolicy().hasHeightForWidth())
        self.leditPlayDeviceName.setSizePolicy(sizePolicy2)
        self.leditPlayDeviceName.setMinimumSize(QSize(0, 0))
        self.leditPlayDeviceName.setMaximumSize(QSize(16777215, 16777215))
        self.leditPlayDeviceName.setFont(font1)
        self.leditPlayDeviceName.setReadOnly(False)

        self.horizontalLayout_9.addWidget(self.leditPlayDeviceName)


        self.gridLayout.addLayout(self.horizontalLayout_9, 2, 1, 1, 1)

        self.horizontalLayout_12 = QHBoxLayout()
        self.horizontalLayout_12.setObjectName(u"horizontalLayout_12")
        self.checkBoxRecordDevice = QCheckBox(self.gpCodecFreqParams)
        self.checkBoxRecordDevice.setObjectName(u"checkBoxRecordDevice")
        self.checkBoxRecordDevice.setMinimumSize(QSize(80, 0))
        self.checkBoxRecordDevice.setFont(font1)
        self.checkBoxRecordDevice.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxRecordDevice.setChecked(False)

        self.horizontalLayout_12.addWidget(self.checkBoxRecordDevice)

        self.leditRecordDeviceName = QLineEdit(self.gpCodecFreqParams)
        self.leditRecordDeviceName.setObjectName(u"leditRecordDeviceName")
        sizePolicy2.setHeightForWidth(self.leditRecordDeviceName.sizePolicy().hasHeightForWidth())
        self.leditRecordDeviceName.setSizePolicy(sizePolicy2)
        self.leditRecordDeviceName.setMinimumSize(QSize(0, 0))
        self.leditRecordDeviceName.setMaximumSize(QSize(16777215, 16777215))
        self.leditRecordDeviceName.setFont(font1)
        self.leditRecordDeviceName.setReadOnly(False)

        self.horizontalLayout_12.addWidget(self.leditRecordDeviceName)


        self.gridLayout.addLayout(self.horizontalLayout_12, 2, 0, 1, 1)

        self.checkBoxSaveAudio = QCheckBox(self.gpCodecFreqParams)
        self.checkBoxSaveAudio.setObjectName(u"checkBoxSaveAudio")
        self.checkBoxSaveAudio.setFont(font1)
        self.checkBoxSaveAudio.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxSaveAudio.setChecked(False)

        self.gridLayout.addWidget(self.checkBoxSaveAudio, 5, 1, 1, 1)

        self.checkBoxUpdateSndFirst = QCheckBox(self.gpCodecFreqParams)
        self.checkBoxUpdateSndFirst.setObjectName(u"checkBoxUpdateSndFirst")
        self.checkBoxUpdateSndFirst.setFont(font1)
        self.checkBoxUpdateSndFirst.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxUpdateSndFirst.setChecked(False)

        self.gridLayout.addWidget(self.checkBoxUpdateSndFirst, 3, 1, 1, 1)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.label_3 = QLabel(self.gpCodecFreqParams)
        self.label_3.setObjectName(u"label_3")
        sizePolicy1.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy1)
        self.label_3.setMinimumSize(QSize(80, 0))
        self.label_3.setFont(font1)

        self.horizontalLayout_3.addWidget(self.label_3)

        self.comboBoxAudioType = QComboBox(self.gpCodecFreqParams)
        self.comboBoxAudioType.addItem("")
        self.comboBoxAudioType.addItem("")
        self.comboBoxAudioType.setObjectName(u"comboBoxAudioType")
        sizePolicy2.setHeightForWidth(self.comboBoxAudioType.sizePolicy().hasHeightForWidth())
        self.comboBoxAudioType.setSizePolicy(sizePolicy2)
        self.comboBoxAudioType.setFont(font1)
        self.comboBoxAudioType.setMaxVisibleItems(16)

        self.horizontalLayout_3.addWidget(self.comboBoxAudioType)


        self.gridLayout.addLayout(self.horizontalLayout_3, 1, 1, 1, 1)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label_2 = QLabel(self.gpCodecFreqParams)
        self.label_2.setObjectName(u"label_2")
        sizePolicy1.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy1)
        self.label_2.setMinimumSize(QSize(80, 0))
        self.label_2.setFont(font1)

        self.horizontalLayout_2.addWidget(self.label_2)

        self.spinBoxTimeLen = QDoubleSpinBox(self.gpCodecFreqParams)
        self.spinBoxTimeLen.setObjectName(u"spinBoxTimeLen")
        self.spinBoxTimeLen.setFont(font1)
        self.spinBoxTimeLen.setDecimals(1)
        self.spinBoxTimeLen.setMinimum(1.000000000000000)
        self.spinBoxTimeLen.setMaximum(20.000000000000000)
        self.spinBoxTimeLen.setSingleStep(0.100000000000000)
        self.spinBoxTimeLen.setValue(1.000000000000000)

        self.horizontalLayout_2.addWidget(self.spinBoxTimeLen)


        self.gridLayout.addLayout(self.horizontalLayout_2, 1, 0, 1, 1)

        self.checkBoxShowLimitConfig = QCheckBox(self.gpCodecFreqParams)
        self.checkBoxShowLimitConfig.setObjectName(u"checkBoxShowLimitConfig")
        self.checkBoxShowLimitConfig.setFont(font1)
        self.checkBoxShowLimitConfig.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxShowLimitConfig.setChecked(False)

        self.gridLayout.addWidget(self.checkBoxShowLimitConfig, 4, 0, 1, 1)

        self.checkBoxERBBand = QCheckBox(self.gpCodecFreqParams)
        self.checkBoxERBBand.setObjectName(u"checkBoxERBBand")
        self.checkBoxERBBand.setFont(font1)
        self.checkBoxERBBand.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxERBBand.setChecked(False)

        self.gridLayout.addWidget(self.checkBoxERBBand, 4, 1, 1, 1)

        self.checkBoxSaveFFT = QCheckBox(self.gpCodecFreqParams)
        self.checkBoxSaveFFT.setObjectName(u"checkBoxSaveFFT")
        self.checkBoxSaveFFT.setFont(font1)
        self.checkBoxSaveFFT.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxSaveFFT.setChecked(False)

        self.gridLayout.addWidget(self.checkBoxSaveFFT, 5, 0, 1, 1)


        self.verticalLayout_5.addLayout(self.gridLayout)


        self.verticalLayout_3.addWidget(self.gpCodecFreqParams)

        self.gpTHDTestConfig = QGroupBox(DialogSetting)
        self.gpTHDTestConfig.setObjectName(u"gpTHDTestConfig")
        sizePolicy.setHeightForWidth(self.gpTHDTestConfig.sizePolicy().hasHeightForWidth())
        self.gpTHDTestConfig.setSizePolicy(sizePolicy)
        self.gpTHDTestConfig.setMinimumSize(QSize(0, 0))
        self.gpTHDTestConfig.setFont(font1)
        self.gpTHDTestConfig.setCheckable(True)
        self.verticalLayout = QVBoxLayout(self.gpTHDTestConfig)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.btnAdd = QPushButton(self.gpTHDTestConfig)
        self.btnAdd.setObjectName(u"btnAdd")
        sizePolicy3 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.btnAdd.sizePolicy().hasHeightForWidth())
        self.btnAdd.setSizePolicy(sizePolicy3)
        self.btnAdd.setMaximumSize(QSize(40, 16777215))
        self.btnAdd.setFont(font1)

        self.horizontalLayout_6.addWidget(self.btnAdd)

        self.btnDelete = QPushButton(self.gpTHDTestConfig)
        self.btnDelete.setObjectName(u"btnDelete")
        sizePolicy3.setHeightForWidth(self.btnDelete.sizePolicy().hasHeightForWidth())
        self.btnDelete.setSizePolicy(sizePolicy3)
        self.btnDelete.setMaximumSize(QSize(40, 16777215))
        self.btnDelete.setFont(font1)

        self.horizontalLayout_6.addWidget(self.btnDelete)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_6.addItem(self.horizontalSpacer)

        self.btnClear = QPushButton(self.gpTHDTestConfig)
        self.btnClear.setObjectName(u"btnClear")
        self.btnClear.setMaximumSize(QSize(50, 16777215))
        self.btnClear.setFont(font1)

        self.horizontalLayout_6.addWidget(self.btnClear)


        self.verticalLayout.addLayout(self.horizontalLayout_6)

        self.tableWidget = QTableWidget(self.gpTHDTestConfig)
        if (self.tableWidget.columnCount() < 4):
            self.tableWidget.setColumnCount(4)
        font2 = QFont()
        font2.setBold(True)
        __qtablewidgetitem = QTableWidgetItem()
        __qtablewidgetitem.setFont(font2);
        self.tableWidget.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        __qtablewidgetitem1.setFont(font2);
        self.tableWidget.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        __qtablewidgetitem2.setFont(font2);
        self.tableWidget.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        __qtablewidgetitem3.setFont(font2);
        self.tableWidget.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        self.tableWidget.setObjectName(u"tableWidget")
        self.tableWidget.setFont(font1)
        self.tableWidget.setSortingEnabled(True)
        self.tableWidget.setRowCount(0)
        self.tableWidget.horizontalHeader().setVisible(True)
        self.tableWidget.horizontalHeader().setCascadingSectionResizes(True)
        self.tableWidget.horizontalHeader().setMinimumSectionSize(25)
        self.tableWidget.horizontalHeader().setDefaultSectionSize(130)
        self.tableWidget.horizontalHeader().setProperty("showSortIndicator", True)
        self.tableWidget.verticalHeader().setVisible(False)
        self.tableWidget.verticalHeader().setCascadingSectionResizes(False)
        self.tableWidget.verticalHeader().setProperty("showSortIndicator", False)

        self.verticalLayout.addWidget(self.tableWidget)


        self.verticalLayout_3.addWidget(self.gpTHDTestConfig)

        self.gpMicGradeConfig = QGroupBox(DialogSetting)
        self.gpMicGradeConfig.setObjectName(u"gpMicGradeConfig")
        sizePolicy.setHeightForWidth(self.gpMicGradeConfig.sizePolicy().hasHeightForWidth())
        self.gpMicGradeConfig.setSizePolicy(sizePolicy)
        self.gpMicGradeConfig.setMinimumSize(QSize(0, 0))
        self.gpMicGradeConfig.setFont(font1)
        self.gpMicGradeConfig.setCheckable(True)
        self.verticalLayout_2 = QVBoxLayout(self.gpMicGradeConfig)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.btnMicGradeAdd = QPushButton(self.gpMicGradeConfig)
        self.btnMicGradeAdd.setObjectName(u"btnMicGradeAdd")
        sizePolicy3.setHeightForWidth(self.btnMicGradeAdd.sizePolicy().hasHeightForWidth())
        self.btnMicGradeAdd.setSizePolicy(sizePolicy3)
        self.btnMicGradeAdd.setMaximumSize(QSize(40, 16777215))
        self.btnMicGradeAdd.setFont(font1)

        self.horizontalLayout_7.addWidget(self.btnMicGradeAdd)

        self.btnMicGradeDelete = QPushButton(self.gpMicGradeConfig)
        self.btnMicGradeDelete.setObjectName(u"btnMicGradeDelete")
        sizePolicy3.setHeightForWidth(self.btnMicGradeDelete.sizePolicy().hasHeightForWidth())
        self.btnMicGradeDelete.setSizePolicy(sizePolicy3)
        self.btnMicGradeDelete.setMaximumSize(QSize(40, 16777215))
        self.btnMicGradeDelete.setFont(font1)

        self.horizontalLayout_7.addWidget(self.btnMicGradeDelete)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_7.addItem(self.horizontalSpacer_2)

        self.btnMicGradeClear = QPushButton(self.gpMicGradeConfig)
        self.btnMicGradeClear.setObjectName(u"btnMicGradeClear")
        self.btnMicGradeClear.setMaximumSize(QSize(50, 16777215))
        self.btnMicGradeClear.setFont(font1)

        self.horizontalLayout_7.addWidget(self.btnMicGradeClear)


        self.verticalLayout_2.addLayout(self.horizontalLayout_7)

        self.tableWidgetMicGrade = QTableWidget(self.gpMicGradeConfig)
        if (self.tableWidgetMicGrade.columnCount() < 6):
            self.tableWidgetMicGrade.setColumnCount(6)
        __qtablewidgetitem4 = QTableWidgetItem()
        __qtablewidgetitem4.setFont(font2);
        self.tableWidgetMicGrade.setHorizontalHeaderItem(0, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        __qtablewidgetitem5.setFont(font2);
        self.tableWidgetMicGrade.setHorizontalHeaderItem(1, __qtablewidgetitem5)
        __qtablewidgetitem6 = QTableWidgetItem()
        __qtablewidgetitem6.setFont(font2);
        self.tableWidgetMicGrade.setHorizontalHeaderItem(2, __qtablewidgetitem6)
        __qtablewidgetitem7 = QTableWidgetItem()
        __qtablewidgetitem7.setFont(font2);
        self.tableWidgetMicGrade.setHorizontalHeaderItem(3, __qtablewidgetitem7)
        __qtablewidgetitem8 = QTableWidgetItem()
        __qtablewidgetitem8.setFont(font2);
        self.tableWidgetMicGrade.setHorizontalHeaderItem(4, __qtablewidgetitem8)
        __qtablewidgetitem9 = QTableWidgetItem()
        __qtablewidgetitem9.setFont(font2);
        self.tableWidgetMicGrade.setHorizontalHeaderItem(5, __qtablewidgetitem9)
        self.tableWidgetMicGrade.setObjectName(u"tableWidgetMicGrade")
        self.tableWidgetMicGrade.setFont(font1)
        self.tableWidgetMicGrade.setSortingEnabled(True)
        self.tableWidgetMicGrade.setRowCount(0)
        self.tableWidgetMicGrade.horizontalHeader().setVisible(True)
        self.tableWidgetMicGrade.horizontalHeader().setCascadingSectionResizes(True)
        self.tableWidgetMicGrade.horizontalHeader().setMinimumSectionSize(25)
        self.tableWidgetMicGrade.horizontalHeader().setDefaultSectionSize(130)
        self.tableWidgetMicGrade.horizontalHeader().setProperty("showSortIndicator", True)
        self.tableWidgetMicGrade.verticalHeader().setVisible(False)
        self.tableWidgetMicGrade.verticalHeader().setCascadingSectionResizes(False)
        self.tableWidgetMicGrade.verticalHeader().setProperty("showSortIndicator", False)

        self.verticalLayout_2.addWidget(self.tableWidgetMicGrade)


        self.verticalLayout_3.addWidget(self.gpMicGradeConfig)

        self.gpDevRingConfig = QGroupBox(DialogSetting)
        self.gpDevRingConfig.setObjectName(u"gpDevRingConfig")
        sizePolicy.setHeightForWidth(self.gpDevRingConfig.sizePolicy().hasHeightForWidth())
        self.gpDevRingConfig.setSizePolicy(sizePolicy)
        self.gpDevRingConfig.setMinimumSize(QSize(400, 0))
        self.gpDevRingConfig.setFont(font1)
        self.gpDevRingConfig.setCheckable(True)
        self.verticalLayout_4 = QVBoxLayout(self.gpDevRingConfig)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.checkBoxRingFreqTestEnable = QCheckBox(self.gpDevRingConfig)
        self.checkBoxRingFreqTestEnable.setObjectName(u"checkBoxRingFreqTestEnable")
        self.checkBoxRingFreqTestEnable.setFont(font1)
        self.checkBoxRingFreqTestEnable.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxRingFreqTestEnable.setChecked(False)

        self.horizontalLayout_4.addWidget(self.checkBoxRingFreqTestEnable)

        self.checkBoxRingThdTestEnable = QCheckBox(self.gpDevRingConfig)
        self.checkBoxRingThdTestEnable.setObjectName(u"checkBoxRingThdTestEnable")
        self.checkBoxRingThdTestEnable.setFont(font1)
        self.checkBoxRingThdTestEnable.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxRingThdTestEnable.setChecked(False)

        self.horizontalLayout_4.addWidget(self.checkBoxRingThdTestEnable)

        self.checkBoxRingFailStop = QCheckBox(self.gpDevRingConfig)
        self.checkBoxRingFailStop.setObjectName(u"checkBoxRingFailStop")
        self.checkBoxRingFailStop.setFont(font1)
        self.checkBoxRingFailStop.setLayoutDirection(Qt.LeftToRight)
        self.checkBoxRingFailStop.setChecked(False)

        self.horizontalLayout_4.addWidget(self.checkBoxRingFailStop)


        self.verticalLayout_4.addLayout(self.horizontalLayout_4)

        self.tableWidgetRingConfig = QTableWidget(self.gpDevRingConfig)
        if (self.tableWidgetRingConfig.columnCount() < 3):
            self.tableWidgetRingConfig.setColumnCount(3)
        __qtablewidgetitem10 = QTableWidgetItem()
        __qtablewidgetitem10.setFont(font2);
        self.tableWidgetRingConfig.setHorizontalHeaderItem(0, __qtablewidgetitem10)
        __qtablewidgetitem11 = QTableWidgetItem()
        __qtablewidgetitem11.setFont(font2);
        self.tableWidgetRingConfig.setHorizontalHeaderItem(1, __qtablewidgetitem11)
        __qtablewidgetitem12 = QTableWidgetItem()
        __qtablewidgetitem12.setFont(font2);
        self.tableWidgetRingConfig.setHorizontalHeaderItem(2, __qtablewidgetitem12)
        self.tableWidgetRingConfig.setObjectName(u"tableWidgetRingConfig")
        self.tableWidgetRingConfig.setFont(font1)
        self.tableWidgetRingConfig.setSortingEnabled(True)
        self.tableWidgetRingConfig.setRowCount(0)
        self.tableWidgetRingConfig.horizontalHeader().setVisible(True)
        self.tableWidgetRingConfig.horizontalHeader().setCascadingSectionResizes(True)
        self.tableWidgetRingConfig.horizontalHeader().setMinimumSectionSize(25)
        self.tableWidgetRingConfig.horizontalHeader().setDefaultSectionSize(130)
        self.tableWidgetRingConfig.horizontalHeader().setProperty("showSortIndicator", True)
        self.tableWidgetRingConfig.verticalHeader().setVisible(False)
        self.tableWidgetRingConfig.verticalHeader().setCascadingSectionResizes(False)
        self.tableWidgetRingConfig.verticalHeader().setProperty("showSortIndicator", False)

        self.verticalLayout_4.addWidget(self.tableWidgetRingConfig)


        self.verticalLayout_3.addWidget(self.gpDevRingConfig)


        self.retranslateUi(DialogSetting)

        self.comboComportBox.setCurrentIndex(-1)
        self.comboBaudrateBox.setCurrentIndex(0)
        self.comboBoxFFTSize.setCurrentIndex(7)
        self.comboBoxSamplerate.setCurrentIndex(0)
        self.comboBoxAudioType.setCurrentIndex(1)


        QMetaObject.connectSlotsByName(DialogSetting)
    # setupUi

    def retranslateUi(self, DialogSetting):
        DialogSetting.setWindowTitle(QCoreApplication.translate("DialogSetting", u"\u9ad8\u7ea7\u8bbe\u7f6e", None))
        self.gpTestBoxConfig.setTitle(QCoreApplication.translate("DialogSetting", u"\u6d4b\u8bd5\u7bb1\u53c2\u6570", None))
        self.label_7.setText(QCoreApplication.translate("DialogSetting", u"\u63a7\u5236\u4e32\u53e3\uff1a", None))
        self.label_9.setText(QCoreApplication.translate("DialogSetting", u"\u6ce2\u7279\u7387\uff1a", None))
        self.comboBaudrateBox.setItemText(0, QCoreApplication.translate("DialogSetting", u"9600", None))

        self.checkBoxAutoTest.setText(QCoreApplication.translate("DialogSetting", u"\u5173\u7bb1\u81ea\u52a8\u6d4b\u8bd5", None))
        self.gpCodecFreqParams.setTitle(QCoreApplication.translate("DialogSetting", u"\u9891\u54cd\u6d4b\u8bd5\u53c2\u6570", None))
        self.btnFreqLimitParams.setText(QCoreApplication.translate("DialogSetting", u"\u9891\u54cd\u6d4b\u8bd5\u9608\u503c\u914d\u7f6e", None))
        self.label.setText(QCoreApplication.translate("DialogSetting", u"FFT Size\uff1a", None))
        self.comboBoxFFTSize.setItemText(0, QCoreApplication.translate("DialogSetting", u"128", None))
        self.comboBoxFFTSize.setItemText(1, QCoreApplication.translate("DialogSetting", u"256", None))
        self.comboBoxFFTSize.setItemText(2, QCoreApplication.translate("DialogSetting", u"512", None))
        self.comboBoxFFTSize.setItemText(3, QCoreApplication.translate("DialogSetting", u"1024", None))
        self.comboBoxFFTSize.setItemText(4, QCoreApplication.translate("DialogSetting", u"2048", None))
        self.comboBoxFFTSize.setItemText(5, QCoreApplication.translate("DialogSetting", u"4096", None))
        self.comboBoxFFTSize.setItemText(6, QCoreApplication.translate("DialogSetting", u"8192", None))
        self.comboBoxFFTSize.setItemText(7, QCoreApplication.translate("DialogSetting", u"16384", None))
        self.comboBoxFFTSize.setItemText(8, QCoreApplication.translate("DialogSetting", u"32768", None))
        self.comboBoxFFTSize.setItemText(9, QCoreApplication.translate("DialogSetting", u"65536", None))

        self.label_5.setText(QCoreApplication.translate("DialogSetting", u"\u91c7\u6837\u9891\u7387\uff1a", None))
        self.comboBoxSamplerate.setItemText(0, QCoreApplication.translate("DialogSetting", u"48000", None))

        self.checkBoxPlayDevice.setText(QCoreApplication.translate("DialogSetting", u"\u64ad\u653e\u8bbe\u5907", None))
        self.leditPlayDeviceName.setText("")
        self.checkBoxRecordDevice.setText(QCoreApplication.translate("DialogSetting", u"\u5f55\u97f3\u8bbe\u5907", None))
        self.leditRecordDeviceName.setText("")
        self.checkBoxSaveAudio.setText(QCoreApplication.translate("DialogSetting", u"\u4fdd\u5b58\u5f55\u97f3\u6587\u4ef6", None))
        self.checkBoxUpdateSndFirst.setText(QCoreApplication.translate("DialogSetting", u"\u6d4b\u8bd5\u524d\u66f4\u65b0\u58f0\u5361\u8bbe\u5907", None))
        self.label_3.setText(QCoreApplication.translate("DialogSetting", u"\u566a\u58f0\u7c7b\u578b\uff1a", None))
        self.comboBoxAudioType.setItemText(0, QCoreApplication.translate("DialogSetting", u"\u767d\u566a\u58f0", None))
        self.comboBoxAudioType.setItemText(1, QCoreApplication.translate("DialogSetting", u"20~20K\u626b\u9891", None))

        self.label_2.setText(QCoreApplication.translate("DialogSetting", u"\u91c7\u6837\u65f6\u957f(s)\uff1a", None))
        self.checkBoxShowLimitConfig.setText(QCoreApplication.translate("DialogSetting", u"\u663e\u793a\u9608\u503c\u6807\u5b9a", None))
        self.checkBoxERBBand.setText(QCoreApplication.translate("DialogSetting", u"Enable ERBBand", None))
        self.checkBoxSaveFFT.setText(QCoreApplication.translate("DialogSetting", u"\u4fdd\u5b58\u9891\u54cd\u6587\u4ef6", None))
        self.gpTHDTestConfig.setTitle(QCoreApplication.translate("DialogSetting", u"THD+N\u6d4b\u8bd5\u53c2\u6570", None))
        self.btnAdd.setText(QCoreApplication.translate("DialogSetting", u"+", None))
        self.btnDelete.setText(QCoreApplication.translate("DialogSetting", u"-", None))
        self.btnClear.setText(QCoreApplication.translate("DialogSetting", u"\u6e05\u7a7a", None))
        ___qtablewidgetitem = self.tableWidget.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("DialogSetting", u"\u9891\u70b9(Hz)", None));
        ___qtablewidgetitem1 = self.tableWidget.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("DialogSetting", u"\u91c7\u6837\u65f6\u957f(s)", None));
        ___qtablewidgetitem2 = self.tableWidget.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("DialogSetting", u"\u9650\u503c(dB)", None));
        ___qtablewidgetitem3 = self.tableWidget.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("DialogSetting", u"\u53c2\u6570\u5e8f\u53f7", None));
        self.gpMicGradeConfig.setTitle(QCoreApplication.translate("DialogSetting", u"MIC\u5206\u6863\u53c2\u6570", None))
        self.btnMicGradeAdd.setText(QCoreApplication.translate("DialogSetting", u"+", None))
        self.btnMicGradeDelete.setText(QCoreApplication.translate("DialogSetting", u"-", None))
        self.btnMicGradeClear.setText(QCoreApplication.translate("DialogSetting", u"\u6e05\u7a7a", None))
        ___qtablewidgetitem4 = self.tableWidgetMicGrade.horizontalHeaderItem(0)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("DialogSetting", u"\u9891\u70b9(Hz)", None));
        ___qtablewidgetitem5 = self.tableWidgetMicGrade.horizontalHeaderItem(1)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("DialogSetting", u"\u5e45\u503cL(dB)", None));
        ___qtablewidgetitem6 = self.tableWidgetMicGrade.horizontalHeaderItem(2)
        ___qtablewidgetitem6.setText(QCoreApplication.translate("DialogSetting", u"\u9650\u503cL", None));
        ___qtablewidgetitem7 = self.tableWidgetMicGrade.horizontalHeaderItem(3)
        ___qtablewidgetitem7.setText(QCoreApplication.translate("DialogSetting", u"\u5e45\u503cR(dB)", None));
        ___qtablewidgetitem8 = self.tableWidgetMicGrade.horizontalHeaderItem(4)
        ___qtablewidgetitem8.setText(QCoreApplication.translate("DialogSetting", u"\u9650\u503cR", None));
        ___qtablewidgetitem9 = self.tableWidgetMicGrade.horizontalHeaderItem(5)
        ___qtablewidgetitem9.setText(QCoreApplication.translate("DialogSetting", u"\u7b49\u7ea7", None));
        self.gpDevRingConfig.setTitle(QCoreApplication.translate("DialogSetting", u"\u5706\u76d8\u9ea6\u6d4b\u8bd5", None))
        self.checkBoxRingFreqTestEnable.setText(QCoreApplication.translate("DialogSetting", u"\u9891\u54cd\u6d4b\u8bd5", None))
        self.checkBoxRingThdTestEnable.setText(QCoreApplication.translate("DialogSetting", u"THDN\u6d4b\u8bd5", None))
        self.checkBoxRingFailStop.setText(QCoreApplication.translate("DialogSetting", u"\u5931\u8d25\u7acb\u5373\u505c\u6b62", None))
        ___qtablewidgetitem10 = self.tableWidgetRingConfig.horizontalHeaderItem(0)
        ___qtablewidgetitem10.setText(QCoreApplication.translate("DialogSetting", u"MIC\u5e8f\u53f7", None));
        ___qtablewidgetitem11 = self.tableWidgetRingConfig.horizontalHeaderItem(1)
        ___qtablewidgetitem11.setText(QCoreApplication.translate("DialogSetting", u"\u9891\u54cd\u9608\u503c\u5e8f\u53f7", None));
        ___qtablewidgetitem12 = self.tableWidgetRingConfig.horizontalHeaderItem(2)
        ___qtablewidgetitem12.setText(QCoreApplication.translate("DialogSetting", u"THD\u53c2\u6570\u5e8f\u53f7", None));
    # retranslateUi

