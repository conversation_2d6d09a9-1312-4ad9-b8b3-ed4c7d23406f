<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UserLoginDialog</class>
 <widget class="QDialog" name="UserLoginDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>365</width>
    <height>197</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>微软雅黑</family>
   </font>
  </property>
  <property name="windowTitle">
   <string>用户登录</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_6">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>11</pointsize>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tabLogin">
      <attribute name="title">
       <string>Login</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <item>
          <widget class="QLabel" name="label">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>0</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>10</pointsize>
             <weight>50</weight>
             <bold>false</bold>
             <underline>false</underline>
            </font>
           </property>
           <property name="text">
            <string>User Name:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditUserName">
           <property name="font">
            <font>
             <family>微软雅黑</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit {
padding: 1px;
border-style: solid;
border: 1px solid gray;
border-radius: 4px;
}</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <item>
          <widget class="QLabel" name="label_2">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>0</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>10</pointsize>
             <weight>50</weight>
             <bold>false</bold>
             <underline>false</underline>
            </font>
           </property>
           <property name="text">
            <string>Password:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditPassword">
           <property name="font">
            <font>
             <family>微软雅黑</family>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit {
padding: 1px;
border-style: solid;
border: 1px solid gray;
border-radius: 4px;
}</string>
           </property>
           <property name="echoMode">
            <enum>QLineEdit::PasswordEchoOnEdit</enum>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="font">
          <font>
           <family>微软雅黑</family>
           <pointsize>11</pointsize>
          </font>
         </property>
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeType">
          <enum>QSizePolicy::Fixed</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>30</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <item>
          <widget class="QPushButton" name="btnLogin">
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>Login</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>11</pointsize>
            </font>
           </property>
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="btnClose">
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="text">
            <string>Close</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tabServerConfig">
      <attribute name="title">
       <string>Server</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <property name="sizeConstraint">
          <enum>QLayout::SetDefaultConstraint</enum>
         </property>
         <item>
          <widget class="QLabel" name="label_3">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>0</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="text">
            <string>Server:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditServer">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>23</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit {
padding: 1px;
border-style: solid;
border: 1px solid gray;
border-radius: 4px;
}</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_4">
         <item>
          <widget class="QLabel" name="label_4">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>0</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="text">
            <string>Port:</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLineEdit" name="lineEditPort">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>23</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QLineEdit {
padding: 1px;
border-style: solid;
border: 1px solid gray;
border-radius: 4px;
}</string>
           </property>
           <property name="text">
            <string>80</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer_2">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
