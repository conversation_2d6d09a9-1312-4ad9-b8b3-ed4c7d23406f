import asyncio
import logging
import threading
from bleak import BleakScanner, BleakClient
from typing import Dict, Optional, Any, Callable
from dataclasses import dataclass
from queue import Queue
from threading import Event


@dataclass
class BleCallback:
    """回调函数容器"""
    event_loop_started: Callable[[], None] = lambda: None
    device_found: Callable[..., None] = lambda x: None
    connection_status: Callable[..., None] = lambda x, y: None
    characteristic_updated: Callable[..., None] = lambda x, y: None
    services_discovered: Callable[..., None] = lambda x: None
    scan_complete: Callable[[], None] = lambda: None
    write_complete: Callable[..., None] = lambda x, y: None
    read_complete: Callable[..., None] = lambda x, y, z: None
    disconnect_complete: Callable[..., None] = lambda x, y: None


class BleWorker(threading.Thread):
    def __init__(self):
        super().__init__()
        self.daemon = True  # 设置为守护线程
        
        # 初始化回调容器
        self.callbacks = BleCallback()
        
        # 队列和事件
        self.command_queue: Queue = Queue()
        self.notification_queue: asyncio.Queue = asyncio.Queue()
        self.cleanup_event = Event()
        
        # BLE相关变量
        self.client: Optional[BleakClient] = None
        self.args: Any = None
        self.notification_callbacks: Dict[int, callable] = {}
        self.char_uuid_map: Dict[int, str] = {}
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.is_scanning: bool = False

    def set_callback(self, event_name: str, callback: Callable):
        """设置回调函数"""
        if hasattr(self.callbacks, event_name):
            setattr(self.callbacks, event_name, callback)
        else:
            raise ValueError(f"Unknown callback event: {event_name}")

    def enqueue_command(self, command: str, args: Any) -> None:
        """线程安全地将命令加入队列"""
        if self.loop and self.loop.is_running():
            future = asyncio.run_coroutine_threadsafe(
                self.command_queue.put((command, args)), 
                self.loop
            )
            try:
                future.result(timeout=1.0)  # 等待命令入队
            except Exception as e:
                logging.error(f"Failed to enqueue command: {e}")
        else:
            logging.warning("Event loop not running, cannot enqueue command")

    def run(self):
        """运行事件循环"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        
        try:
            self.loop.call_soon_threadsafe(self.callbacks.event_loop_started)
            self.loop.create_task(self.command_handler())
            self.loop.create_task(self.process_notifications())
            self.loop.run_forever()
        except Exception as e:
            logging.error(f"Event loop error: {str(e)}")
        finally:
            self.loop.close()
            self.loop = None

    async def command_handler(self):
        """处理命令队列"""
        while not self.cleanup_event.is_set():
            try:
                command, self.args = await self.command_queue.get()
                
                command_handlers = {
                    "scan": self.scan_devices,
                    "connect": self.connect_device,
                    "disconnect": self.disconnect_device,
                    "write": self.write_characteristic,
                    "read": self.read_characteristic,
                    "subscribe": self.subscribe_characteristic,
                    "unsubscribe": self.unsubscribe_characteristic
                }
                
                if command in command_handlers:
                    await command_handlers[command]()
                else:
                    logging.warning(f"Unknown command: {command}")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logging.error(f"Command handler error: {str(e)}")

    async def scan_devices(self):
        """扫描BLE设备"""
        if self.is_scanning:
            return

        self.is_scanning = True
        try:
            async with BleakScanner() as scanner:
                devices = await scanner.discover()
                for device in devices:
                    if device.name:
                        self.loop.call_soon_threadsafe(
                            self.callbacks.device_found, 
                            device
                        )
        except Exception as e:
            logging.error(f"Scan error: {str(e)}")
        finally:
            self.is_scanning = False
            self.loop.call_soon_threadsafe(self.callbacks.scan_complete)

    async def connect_device(self):
        """连接到BLE设备"""
        device = self.args
        try:
            if self.client and self.client.is_connected:
                await self.disconnect_device()

            self.client = BleakClient(
                device['address'], 
                disconnected_callback=self._handle_disconnection
            )
            await self.client.connect()
            self.loop.call_soon_threadsafe(
                self.callbacks.connection_status,
                True,
                "Connected successfully"
            )

            # 发现服务
            services = []
            for service in self.client.services:
                service_info = {
                    'uuid': service.uuid,
                    'description': service.description,
                    'characteristics': []
                }
                for char in service.characteristics:
                    char_info = {
                        'uuid': char.uuid,
                        'description': char.description,
                        'properties': char.properties,
                        'handle': char.handle
                    }
                    service_info['characteristics'].append(char_info)
                    self.char_uuid_map[char.handle] = char.uuid
                services.append(service_info)

            self.loop.call_soon_threadsafe(
                self.callbacks.services_discovered,
                services
            )

        except Exception as e:
            error_msg = str(e)
            self.loop.call_soon_threadsafe(
                self.callbacks.connection_status,
                False,
                error_msg
            )
            self.client = None

    def _handle_disconnection(self, client: BleakClient):
        """处理设备断开连接回调"""
        if self.loop and self.loop.is_running():
            asyncio.run_coroutine_threadsafe(
                self.command_queue.put(("disconnect", None)),
                self.loop
            )

    async def disconnect_device(self):
        """断开设备连接"""
        try:
            if self.client and self.client.is_connected:
                for handle in list(self.notification_callbacks.keys()):
                    await self.unsubscribe_characteristic(handle)
                await self.client.disconnect()
                self.client = None
                self.loop.call_soon_threadsafe(
                    self.callbacks.disconnect_complete,
                    True,
                    "Device disconnected"
                )
            else:
                self.loop.call_soon_threadsafe(
                    self.callbacks.disconnect_complete,
                    True,
                    "Device not connected"
                )
        except Exception as e:
            self.loop.call_soon_threadsafe(
                self.callbacks.disconnect_complete,
                False,
                str(e)
            )

    async def write_characteristic(self):
        """写入特征值"""
        char_handle, data = self.args
        max_retries = 2
        retry_count = 0

        while retry_count < max_retries:
            try:
                if not self.client or not self.client.is_connected:
                    raise Exception("设备未连接")

                char_uuid = self.char_uuid_map.get(char_handle)
                if not char_uuid:
                    raise ValueError(f"未找到handle为{char_handle}的特征值UUID")

                target_char = None
                for service in self.client.services:
                    for char in service.characteristics:
                        if char.uuid == char_uuid:
                            target_char = char
                            break
                    if target_char:
                        break

                if not target_char:
                    raise ValueError(f"未找到UUID为{char_uuid}的特征值")

                if "write" not in target_char.properties:
                    raise ValueError(f"特征值 {char_uuid} 不支持写入操作")

                # 使用 asyncio.wait_for 替代 asyncio.timeout
                try:
                    await asyncio.wait_for(
                        self.client.write_gatt_char(target_char.uuid, data, response=False),
                        timeout=2.0
                    )
                except asyncio.TimeoutError:
                    raise

                logging.info(f"写入特征值成功: UUID={char_uuid}, 数据: {data.hex()}")
                self.loop.call_soon_threadsafe(self.callbacks.write_complete, True, "写入成功")
                return

            except asyncio.TimeoutError:
                retry_count += 1
                logging.warning(f"写入超时，尝试重试 ({retry_count}/{max_retries})")
                await asyncio.sleep(0.5)
                continue
            except Exception as e:
                retry_count += 1
                error_msg = str(e)
                logging.error(f"写入特征值失败 (尝试 {retry_count}/{max_retries}): {error_msg}")

                if retry_count < max_retries:
                    await asyncio.sleep(0.5)
                    continue
                else:
                    self.loop.call_soon_threadsafe(self.callbacks.write_complete, True, f"写入失败: {error_msg}")
                    return

    async def read_characteristic(self):
        """读取特征值"""
        char_handle = self.args
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                if not self.client.is_connected:
                    await self.client.connect()
                    logging.info("重新建立连接")

                target_char = None
                for service in self.client.services:
                    for char in service.characteristics:
                        if char.handle == char_handle:
                            target_char = char
                            break
                    if target_char:
                        break

                if not target_char:
                    raise ValueError(f"未找到handle为{char_handle}的特征值")

                if "read" not in target_char.properties:
                    raise ValueError(f"特征值 {char_handle} 不支持读取操作")

                data = await self.client.read_gatt_char(target_char)
                logging.info(f"读取特征值成功: handle={char_handle}, 数据: {data.hex()}")
                self.loop.call_soon_threadsafe(self.callbacks.read_complete, True, "读取成功", data)
                return

            except Exception as e:
                retry_count += 1
                error_msg = str(e)
                logging.error(f"读取特征值失败 (尝试 {retry_count}/{max_retries}): {error_msg}")

                if retry_count < max_retries:
                    await asyncio.sleep(0.5)
                    continue
                else:
                    self.loop.call_soon_threadsafe(self.callbacks.read_complete, True, f"读取失败: {error_msg}", bytes())
                    return

    async def notification_handler(self, sender, data):
        """处理通知数据"""
        try:
            await self.notification_queue.put((sender, data))
        except Exception as e:
            logging.error(f"处理通知时出错: {str(e)}")

    async def process_notifications(self):
        """处理通知队列"""
        try:
            while True:
                try:
                    sender, data = await self.notification_queue.get()
                    logging.debug(f"收到通知: {sender}: {data.hex()}")
                    self.loop.call_soon_threadsafe(self.callbacks.characteristic_updated, str(sender), data)
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logging.error(f"处理通知队列时出错: {str(e)}")
        except Exception as e:
            logging.error(f"通知处理循环出错: {str(e)}")

    async def subscribe_characteristic(self):
        """订阅特征值通知"""
        char_handle = self.args
        logging.info(f"handle: {char_handle}, uuid: {self.char_uuid_map.get(char_handle)}")
        try:
            char_uuid = self.char_uuid_map.get(char_handle)
            if not char_uuid:
                raise ValueError(f"未找到handle为{char_handle}的特征值UUID")

            target_char = None
            for service in self.client.services:
                for char in service.characteristics:
                    if char.uuid == char_uuid:
                        target_char = char
                        break
                if target_char:
                    break

            if not target_char:
                raise ValueError(f"未找到UUID为{char_uuid}的特征值")

            if "notify" not in target_char.properties:
                raise ValueError(f"特征值 {char_uuid} 不支持通知")

            if char_handle in self.notification_callbacks:
                await self.client.stop_notify(target_char)
                del self.notification_callbacks[char_handle]

            async def notification_callback(sender, data):
                await self.notification_queue.put((sender, data))

            self.notification_callbacks[char_handle] = notification_callback

            await self.client.start_notify(target_char, notification_callback)
            logging.info(f"订阅特征值成功: UUID={char_uuid}")

        except Exception as e:
            logging.error(f"订阅特征值时出错: {str(e)}")
            if char_handle in self.notification_callbacks:
                del self.notification_callbacks[char_handle]
            raise

    async def unsubscribe_characteristic(self):
        """取消订阅特征值通知"""
        char_handle = self.args
        try:
            for service in self.client.services:
                for char in service.characteristics:
                    if char.handle == char_handle:
                        await self.client.stop_notify(char)
                        if char.handle in self.notification_callbacks:
                            del self.notification_callbacks[char.handle]
                        logging.info(f"取消订阅特征值成功: handle={char_handle}")
                        return
        except Exception as e:
            logging.error(f"取消订阅特征值时出错: {str(e)}")
    
    def handle_close(self):
        """清理资源"""
        self.cleanup_event.set()
        if self.loop and self.loop.is_running():
            asyncio.run_coroutine_threadsafe(
                self._cleanup_tasks(),
                self.loop
            )
            self.join(timeout=3.0)  # 等待线程结束

    async def _cleanup_tasks(self):
        """清理异步任务"""
        if self.client and self.client.is_connected:
            await self.disconnect_device()
        self.loop.stop()

