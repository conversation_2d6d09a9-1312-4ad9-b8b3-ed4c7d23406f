import asyncio
import logging
from bleak import BleakScanner, BleakClient
from PySide6.QtCore import QThread, Signal
from typing import Dict, Optional, Any


class BleWorker(QThread):
    eventLoopStarted = Signal()
    deviceFound = Signal(object, int)
    connectionStatus = Signal(bool, str)
    characteristicUpdated = Signal(str, bytes)
    servicesDiscovered = Signal(object)
    scanComplete = Signal()
    writeComplete = Signal(bool, str)
    readComplete = Signal(bool, str, bytes)
    disconnectComplete = Signal(bool, str)
    subscribeComplete = Signal(bool, str)

    def __init__(self):
        super().__init__()
        self.command_queue: asyncio.Queue = asyncio.Queue()
        self.client: Optional[BleakClient] = None
        self.args: Any = None
        self.notification_callbacks: Dict[int, callable] = {}
        self._notification_queue: asyncio.Queue = asyncio.Queue()
        self._char_uuid_map: Dict[int, str] = {}
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self._is_scanning: bool = False
        self._cleanup_event = asyncio.Event()
        self.detected_devices = {}  # 改为字典，存储更多设备信息
        self.scanner = None  # 延迟初始化
        self._scan_task = None  # 保存扫描任务引用

    def enqueue_command(self, command: str, args: Any) -> None:
        """线程安全地将命令加入队列"""
        if self.loop and self.loop.is_running():
            asyncio.run_coroutine_threadsafe(self.command_queue.put((command, args)), self.loop)
        else:
            logging.warning("Event loop not running, cannot enqueue command")

    def run(self):
        """运行事件循环"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
        self.eventLoopStarted.emit()

        try:
            self.loop.create_task(self.command_handler())
            self.loop.create_task(self.process_notifications())
            self.loop.run_forever()
        except Exception as e:
            logging.error(f"事件循环出错: {str(e)}")
        finally:
            self.loop.close()
            self.loop = None

    async def command_handler(self):
        """处理命令队列"""
        while not self._cleanup_event.is_set():
            try:
                command, self.args = await asyncio.wait_for(
                    self.command_queue.get(),
                    timeout=1.0
                )

                try:
                    if command == "scan":
                        await self.scan_devices()
                    if command == "scan_start":
                        await self.start_scan()
                    elif command == "scan_stop":
                        await self.stop_scan()
                    elif command == "connect":
                        await self.connect_device()
                    elif command == "disconnect":
                        await self.disconnect_device()
                    elif command == "write":
                        await self.write_characteristic()
                    elif command == "read":
                        await self.read_characteristic()
                    elif command == "subscribe":
                        await self.subscribe_characteristic()
                    elif command == "unsubscribe":
                        await self.unsubscribe_characteristic()
                except Exception as e:
                    logging.error(f"处理命令 {command} 时出错: {str(e)}")

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logging.error(f"命令处理循环出错: {str(e)}")

    async def scan_devices(self):
        """扫描BLE设备（一次性扫描）"""
        if self._is_scanning:
            logging.warning("Already scanning, ignoring scan request")
            return

        logging.info("开始扫描BLE设备")
        self._is_scanning = True
        self.detected_devices.clear()
        
        try:
            # 使用更优化的扫描参数
            scanner = BleakScanner(
                detection_callback=self.detection_callback,
                service_uuids=None,  # 不过滤服务UUID，扫描所有设备
                scanning_mode="active"  # 使用主动扫描模式
            )
            
            # 扫描更长时间以发现更多设备
            await scanner.start()
            await asyncio.sleep(10.0)  # 扫描10秒
            await scanner.stop()
            
            logging.info(f"扫描完成，发现 {len(self.detected_devices)} 个设备")
            
        except Exception as e:
            logging.error(f"扫描设备时出错: {str(e)}")
        finally:
            self._is_scanning = False
            self.scanComplete.emit()

    def detection_callback(self, device, advertisement_data):
        if not device.name:
            return

        device_key = device.address

        rssi_value = advertisement_data.rssi if advertisement_data else 0

        # 检查是否已经发现过这个设备
        if device_key in self.detected_devices:
            # 更新已存在设备的信息（RSSI可能会变化）
            stored_device = self.detected_devices[device_key]
            if rssi_value is not None:
                stored_device['rssi'] = rssi_value
            return

        # 创建设备信息字典
        device_info = {
            'address': device.address,
            'name': device.name if device.name else None,
            'rssi': rssi_value,
            'service_uuids': advertisement_data.service_uuids if advertisement_data else [],
            'tx_power': advertisement_data.tx_power if advertisement_data else None,
            'local_name': advertisement_data.local_name if advertisement_data else None
        }
        
        # 存储设备信息
        self.detected_devices[device_key] = device_info
        
        # 发射信号（为了兼容现有代码，仍然发射原始device对象）
        if device_info['name']:
            logging.info(f"发现设备: {device_info['name']} ({device_info['address']}) RSSI: {device_info['rssi']}")
            self.deviceFound.emit(device, device_info['rssi'])
                
    async def start_scan(self):
        """开始持续扫描BLE设备"""
        if self._is_scanning:
            logging.warning("Already scanning, ignoring scan request")
            return

        logging.info("开始持续扫描BLE设备")
        self._is_scanning = True
        self.detected_devices.clear()

        try:
            # 创建扫描器，使用优化参数
            self.scanner = BleakScanner(
                detection_callback=self.detection_callback,
                service_uuids=None,  # 不限制服务UUID
                scanning_mode="active"  # 主动扫描
            )
            
            await self.scanner.start()
            
            # 创建一个后台任务来定期刷新扫描
            self._scan_task = asyncio.create_task(self._periodic_scan_refresh())
            
        except Exception as e:
            logging.error(f"开始扫描设备时出错: {str(e)}")
            self._is_scanning = False
            
    async def _periodic_scan_refresh(self):
        """定期刷新扫描以发现更多设备"""
        try:
            while self._is_scanning:
                await asyncio.sleep(5.0)  # 每5秒刷新一次
                if not self._is_scanning:
                    break
                    
                # 重启扫描以发现新设备
                try:
                    if self.scanner:
                        await self.scanner.stop()
                        await asyncio.sleep(0.1)  # 短暂停顿
                        await self.scanner.start()
                        logging.debug("刷新扫描以发现更多设备")
                except Exception as e:
                    logging.warning(f"刷新扫描时出错: {str(e)}")
                    
        except asyncio.CancelledError:
            logging.info("扫描刷新任务被取消")
        except Exception as e:
            logging.error(f"定期扫描刷新出错: {str(e)}")
            
    async def stop_scan(self):
        """停止扫描BLE设备"""
        if not self._is_scanning:
            logging.warning("Not scanning, ignoring stop scan request")
            return

        logging.info("停止扫描BLE设备")
        self._is_scanning = False

        try:
            # 取消定期刷新任务
            if self._scan_task and not self._scan_task.done():
                self._scan_task.cancel()
                try:
                    await self._scan_task
                except asyncio.CancelledError:
                    pass
                    
            # 停止扫描器
            if self.scanner:
                await self.scanner.stop()
                self.scanner = None
                
            logging.debug(f"扫描停止，总共发现 {len(self.detected_devices)} 个设备")
            self.scanComplete.emit()
            
        except Exception as e:
            logging.error(f"停止扫描设备时出错: {str(e)}")

    async def connect_device(self):
        """连接到BLE设备"""
        device = self.args
        try:
            if self.client and self.client.is_connected:
                await self.disconnect_device()

            logging.info(f"正在连接设备: {device['address']}")
            self.client = BleakClient(device['address'], disconnected_callback=self._handle_disconnection, timeout=30)
            await self.client.connect()
            if not self.client.is_connected:
                logging.error(f"连接设备时出错: 未连接")
                self.connectionStatus.emit(False, "连接失败")
                self.client = None
                return
            self.connectionStatus.emit(True, "连接成功")

            # 发现服务和特征值
            services = []
            for service in self.client.services:
                service_info = {
                    'uuid': service.uuid,
                    'description': service.description,
                    'characteristics': []
                }
                for char in service.characteristics:
                    char_info = {
                        'uuid': char.uuid,
                        'description': char.description,
                        'properties': char.properties,
                        'handle': char.handle
                    }
                    service_info['characteristics'].append(char_info)
                    self._char_uuid_map[char.handle] = char.uuid
                services.append(service_info)

            self.servicesDiscovered.emit(services)

        except Exception as e:
            logging.exception(e)
            logging.error(f"连接设备时出错: {str(e)}")
            self.connectionStatus.emit(False, str(e))
            self.client = None

    def _handle_disconnection(self, client: BleakClient) -> None:
        """处理设备断开连接回调"""
        logging.info("设备意外断开连接")
        if self.loop and self.loop.is_running():
            asyncio.run_coroutine_threadsafe(
                self.command_queue.put(("disconnect", None)),
                self.loop
            )

    async def disconnect_device(self):
        """断开设备连接"""
        try:
            if self.client and self.client.is_connected:
                for service in self.client.services:
                    for char in service.characteristics:
                        if char.handle in self.notification_callbacks:
                            try:
                                await self.client.stop_notify(char)
                                if char.handle in self.notification_callbacks:
                                    del self.notification_callbacks[char.handle]
                            except:
                                pass

                await self.client.disconnect()
                self.client = None
                logging.info("设备已断开连接")
                self.disconnectComplete.emit(True, "设备已断开连接")
            else:
                logging.info("设备未连接")
                self.disconnectComplete.emit(True, "设备未连接")
        except Exception as e:
            error_msg = str(e)
            logging.error(f"断开连接时出错: {error_msg}")
            self.disconnectComplete.emit(False, f"断开连接失败: {error_msg}")

    async def write_characteristic(self):
        """写入特征值"""
        char_handle, data = self.args
        max_retries = 2
        retry_count = 0

        while retry_count < max_retries:
            try:
                if not self.client or not self.client.is_connected:
                    raise Exception("设备未连接")

                char_uuid = self._char_uuid_map.get(char_handle)
                if not char_uuid:
                    raise ValueError(f"未找到handle为{char_handle}的特征值UUID")

                target_char = None
                for service in self.client.services:
                    for char in service.characteristics:
                        if char.uuid == char_uuid:
                            target_char = char
                            break
                    if target_char:
                        break

                if not target_char:
                    raise ValueError(f"未找到UUID为{char_uuid}的特征值")

                if "write" not in target_char.properties and "write-without-response" not in target_char.properties:
                    raise ValueError(f"特征值 {char_uuid} 不支持写入操作")

                # 使用 asyncio.wait_for 替代 asyncio.timeout
                try:
                    await asyncio.wait_for(
                        self.client.write_gatt_char(target_char.uuid, data, response=False),
                        timeout=2.0
                    )
                except asyncio.TimeoutError:
                    raise

                logging.debug(f"写入特征值成功: UUID={char_uuid}, 数据: {data.hex()}")
                self.writeComplete.emit(True, "写入成功")
                return

            except asyncio.TimeoutError:
                retry_count += 1
                logging.warning(f"写入超时，尝试重试 ({retry_count}/{max_retries})")
                await asyncio.sleep(0.5)
                continue
            except Exception as e:
                retry_count += 1
                error_msg = str(e)
                logging.error(f"写入特征值失败 (尝试 {retry_count}/{max_retries}): {error_msg}")

                if retry_count < max_retries:
                    await asyncio.sleep(0.5)
                    continue
                else:
                    self.writeComplete.emit(False, f"写入失败: {error_msg}")
                    return

    async def read_characteristic(self):
        """读取特征值"""
        char_handle = self.args
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                if not self.client.is_connected:
                    await self.client.connect()
                    logging.info("重新建立连接")

                target_char = None
                for service in self.client.services:
                    for char in service.characteristics:
                        if char.handle == char_handle:
                            target_char = char
                            break
                    if target_char:
                        break

                if not target_char:
                    raise ValueError(f"未找到handle为{char_handle}的特征值")

                if "read" not in target_char.properties:
                    raise ValueError(f"特征值 {char_handle} 不支持读取操作")

                data = await self.client.read_gatt_char(target_char)
                logging.info(f"读取特征值成功: handle={char_handle}, 数据: {data.hex()}")
                self.readComplete.emit(True, "读取成功", data)
                return

            except Exception as e:
                retry_count += 1
                error_msg = str(e)
                logging.error(f"读取特征值失败 (尝试 {retry_count}/{max_retries}): {error_msg}")

                if retry_count < max_retries:
                    await asyncio.sleep(0.5)
                    continue
                else:
                    self.readComplete.emit(False, f"读取失败: {error_msg}", bytes())
                    return

    async def notification_handler(self, sender, data):
        """处理通知数据"""
        try:
            await self._notification_queue.put((sender, data))
        except Exception as e:
            logging.error(f"处理通知时出错: {str(e)}")

    async def process_notifications(self):
        """处理通知队列"""
        try:
            while True:
                try:
                    sender, data = await self._notification_queue.get()
                    logging.debug(f"收到通知: {sender}: {data.hex()}")
                    self.characteristicUpdated.emit(str(sender), data)
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    logging.error(f"处理通知队列时出错: {str(e)}")
        except Exception as e:
            logging.error(f"通知处理循环出错: {str(e)}")

    async def subscribe_characteristic(self):
        """订阅特征值通知"""
        char_handle = self.args
        logging.debug(f"handle: {char_handle}, uuid: {self._char_uuid_map.get(char_handle)}")
        try:
            if not self.client or not self.client.is_connected:
                raise Exception("设备未连接")

            char_uuid = self._char_uuid_map.get(char_handle)
            if not char_uuid:
                raise ValueError(f"未找到handle为{char_handle}的特征值UUID")

            target_char = None
            for service in self.client.services:
                for char in service.characteristics:
                    if char.uuid == char_uuid:
                        target_char = char
                        break
                if target_char:
                    break

            if not target_char:
                raise ValueError(f"未找到UUID为{char_uuid}的特征值")

            if "notify" not in target_char.properties:
                raise ValueError(f"特征值 {char_uuid} 不支持通知")

            if char_handle in self.notification_callbacks:
                await self.client.stop_notify(target_char)
                del self.notification_callbacks[char_handle]

            async def notification_callback(sender, data):
                await self._notification_queue.put((sender, data))

            self.notification_callbacks[char_handle] = notification_callback

            await self.client.start_notify(target_char, notification_callback)
            logging.debug(f"订阅特征值成功: UUID={char_uuid}")
            self.subscribeComplete.emit(True, "特征值订阅成功")

        except Exception as e:
            error_msg = str(e)
            logging.error(f"订阅特征值时出错: {error_msg}")
            if char_handle in self.notification_callbacks:
                del self.notification_callbacks[char_handle]
            self.subscribeComplete.emit(False, f"订阅失败: {error_msg}")
            raise

    async def unsubscribe_characteristic(self):
        """取消订阅特征值通知"""
        char_handle = self.args
        try:
            for service in self.client.services:
                for char in service.characteristics:
                    if char.handle == char_handle:
                        await self.client.stop_notify(char)
                        if char.handle in self.notification_callbacks:
                            del self.notification_callbacks[char.handle]
                        logging.debug(f"取消订阅特征值成功: handle={char_handle}")
                        return
        except Exception as e:
            logging.error(f"取消订阅特征值时出错: {str(e)}")

    async def _cleanup_tasks(self):
        self._cleanup_event.set()
        
        # 停止扫描
        if self._is_scanning:
            await self.stop_scan()
            
        if self.loop and self.loop.is_running():
            self.loop.stop()

    def _cleanup(self):
        try:
            if self.loop and self.loop.is_running():
                self.loop.call_soon_threadsafe(asyncio.ensure_future, self._cleanup_tasks())
                self.wait(3000)
        except Exception as e:
            logging.error(f"清理资源时出错: {str(e)}")

    def handle_close(self):
        self._cleanup()

    def get_detected_devices_info(self):
        """获取检测到的设备详细信息"""
        return self.detected_devices.copy()