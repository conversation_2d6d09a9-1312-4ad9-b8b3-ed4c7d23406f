# -*- coding: utf-8 -*-
import os

from PySide6 import QtGui, QtWidgets, QtCore


class BaseSubWindow(QtWidgets.QMainWindow):

    def __init__(self):
        super(BaseSubWindow, self).__init__()
        self.logo = QtGui.QIcon(BaseResource.resource_path("Resource\\logo.ico"))
        self.setWindowIcon(self.logo)

    def showMessage(self, msg, msgType=QtWidgets.QMessageBox.Icon.Warning, btnYes=True, btnNo=False, yesText='确认', noText='取消'):
        messageBox = QtWidgets.QMessageBox(msgType, " ", msg)
        if btnNo and btnYes:
            messageBox.setStandardButtons(QtWidgets.QMessageBox.StandardButton.Yes | QtWidgets.QMessageBox.StandardButton.No)
            messageBox.setDefaultButton(QtWidgets.QMessageBox.StandardButton.No)
        elif btnYes:
            messageBox.setStandardButtons(QtWidgets.QMessageBox.StandardButton.Yes)
        elif btnNo:
            messageBox.setStandardButtons(QtWidgets.QMessageBox.StandardButton.No)
        else:
            messageBox.setStandardButtons(QtWidgets.QMessageBox.StandardButton.Yes)
            messageBox.button(QtWidgets.QMessageBox.StandardButton.Yes).hide()
        messageBox.setWindowIcon(self.logo)
        if btnYes:
            messageBox.button(QtWidgets.QMessageBox.StandardButton.Yes).setText(yesText)
        if btnNo:
            messageBox.button(QtWidgets.QMessageBox.StandardButton.No).setText(noText)
        return messageBox.exec_()


class BaseSubDialog(QtWidgets.QDialog):

    def __init__(self):
        super(BaseSubDialog, self).__init__()
        self.logo = QtGui.QIcon(BaseResource.resource_path("Resource\\logo.ico"))
        self.setWindowIcon(self.logo)

    def showMessage(self, msg, msgType=QtWidgets.QMessageBox.Icon.Warning):
        messageBox = QtWidgets.QMessageBox(msgType, " ", msg)
        messageBox.setStandardButtons(QtWidgets.QMessageBox.StandardButton.Yes)
        messageBox.setWindowIcon(self.logo)
        button = messageBox.button(QtWidgets.QMessageBox.StandardButton.Yes)
        button.setText('确认')
        messageBox.exec_()


class UserComboBox(QtWidgets.QComboBox):
    popupAboutToBeShown = QtCore.Signal()

    def showPopup(self):
        self.popupAboutToBeShown.emit()
        super(UserComboBox, self).showPopup()


class BaseResource(object):
    @staticmethod
    def resource_path(relative_path):
        """ Get absolute path to resource, works for dev and for PyInstaller """
        try:
            base_path = os.path.dirname(os.path.abspath(__file__))
        except Exception:
            base_path = os.path.abspath(".")

        return os.path.join(base_path, relative_path)

    @staticmethod
    def getConfigFileFullName(fileName, folder=None):
        configPath = os.path.join(os.getenv('LOCALAPPDATA'), 'CaseCtrlTool')
        if folder is not None:
            configPath = os.path.join(configPath, folder)
        if not os.path.exists(configPath):
            os.makedirs(configPath)
        configFile = os.path.join(configPath, fileName)
        return configFile
