# -*- coding: utf-8 -*-
import ctypes
import ctypes.wintypes as wintypes
import logging
import os
import platform

from BaseSubMainWindow import BaseSubWindow

NULL = 0
INVALID_HANDLE_VALUE = -1
DBT_DEVTYP_DEVICEINTERFACE = 5
DEVICE_NOTIFY_WINDOW_HANDLE = 0x00000000
DBT_DEVICEREMOVECOMPLETE = 0x8004
DBT_DEVICEARRIVAL = 0x8000
WM_DEVICECHANGE = 0x0219
DBT_DEVTYP_VOLUME = 2

user32 = ctypes.windll.user32
RegisterDeviceNotification = user32.RegisterDeviceNotificationW
UnregisterDeviceNotification = user32.UnregisterDeviceNotification
UnregisterDeviceNotification.restype = wintypes.BOOL
UnregisterDeviceNotification.argtypes = [wintypes.HANDLE]


class GUID(ctypes.Structure):
    _pack_ = 1
    _fields_ = [("Data1", ctypes.c_ulong),
                ("Data2", ctypes.c_ushort),
                ("Data3", ctypes.c_ushort),
                ("Data4", ctypes.c_ubyte * 8)]


class DEV_BROADCAST_DEVICEINTERFACE(ctypes.Structure):
    _pack_ = 1
    _fields_ = [("dbcc_size", wintypes.DWORD),
                ("dbcc_devicetype", wintypes.DWORD),
                ("dbcc_reserved", wintypes.DWORD),
                ("dbcc_classguid", GUID),
                ("dbcc_name", ctypes.c_wchar * 260)]


class DEV_BROADCAST_HDR(ctypes.Structure):
    _fields_ = [("dbch_size", wintypes.DWORD),
                ("dbch_devicetype", wintypes.DWORD),
                ("dbch_reserved", wintypes.DWORD)]


class DEV_BROADCAST_VOLUME (ctypes.Structure):
    _fields_ = [("dbcv_size", wintypes.DWORD),
                ("dbcv_devicetype", wintypes.DWORD),
                ("dbcv_reserved", wintypes.DWORD),
                ("dbcv_unitmask", wintypes.DWORD),
                ("dbcv_flags", wintypes.WORD)]


GUID_DEVCLASS_PORTS = GUID(0x4D36E978, 0xE325, 0x11CE,
                           (ctypes.c_ubyte * 8)(0xBF, 0xC1, 0x08, 0x00, 0x2B, 0xE1, 0x03, 0x18))
GUID_DEVINTERFACE_USB_DEVICE = GUID(0xA5DCBF10, 0x6530, 0x11D2,
                                    (ctypes.c_ubyte * 8)(0x90, 0x1F, 0x00, 0xC0, 0x4F, 0xB9, 0x51, 0xED))
GUID_DEVINTERFACE_HID = GUID(0x4D1E55B2, 0xF16F, 0x11CF,
                             (ctypes.c_ubyte * 8)(0x88, 0xCB, 0x00, 0x11, 0x11, 0x00, 0x00, 0x30))

ERR_NO_ERR = 0x00
ERR_LEFT_EAR_NIRQ = 0x01
ERR_LEFT_EAR_I2C = 0x02
ERR_RIGHT_EAR_NIRQ = 0x04
ERR_RIGHT_EAR_I2C = 0x08
ERR_TOUCH = 0x10
ERR_CP = 0x20
ERR_GSENSOR = 0x40


class UsbDevDetect(BaseSubWindow):
    hNofity = None
    usbDevVID = 0x291a
    usbDevPID = 0x3301
    diskVID = 0x058F
    diskPID = 0x6387
    codecVID = 0x03DA
    codecPID = 0x4009
    isHpUDisk = False

    def __init__(self):
        super(UsbDevDetect, self).__init__()
        # logging.info('Usb Detect Init.')

    def setUsbID(self, vid, pid):
        self.usbDevVID = vid
        self.usbDevPID = pid

    def setupNotification(self, vid=0x291a, pid=0x3301):
        self.usbDevVID = vid
        self.usbDevPID = pid
        dbh = DEV_BROADCAST_DEVICEINTERFACE()
        dbh.dbcc_size = ctypes.sizeof(DEV_BROADCAST_DEVICEINTERFACE)
        dbh.dbcc_devicetype = DBT_DEVTYP_DEVICEINTERFACE
        dbh.dbcc_classguid = GUID_DEVINTERFACE_USB_DEVICE
        self.hNofity = RegisterDeviceNotification(int(self.winId()), ctypes.byref(dbh), DEVICE_NOTIFY_WINDOW_HANDLE)
        if self.hNofity == NULL:
            logging.error('RegisterDeviceNotification failed, %s, %s', ctypes.FormatError(), self.winId())

    def removeNotification(self):
        if self.hNofity:
            UnregisterDeviceNotification(self.hNofity)

    def nativeEvent(self, eventType, msg):
        message = ctypes.wintypes.MSG.from_address(msg.__int__())
        if eventType == "windows_generic_MSG":
            if message.message == WM_DEVICECHANGE:
                self.onDeviceChanged(message.wParam, message.lParam)

        return False, 0

    def onDeviceChanged(self, wParam, lParam):
        if DBT_DEVICEARRIVAL == wParam:
            action = 'add'
        elif DBT_DEVICEREMOVECOMPLETE == wParam:
            action = 'remove'
        else:
            return

        if DBT_DEVICEARRIVAL == wParam or DBT_DEVICEREMOVECOMPLETE == wParam:
            dbh = DEV_BROADCAST_HDR.from_address(lParam)
            if dbh.dbch_devicetype == DBT_DEVTYP_DEVICEINTERFACE:
                dbd = DEV_BROADCAST_DEVICEINTERFACE.from_address(lParam)
                # self.ui.logText.append(dbd.dbcc_name)
                logging.info(dbd.dbcc_name)
                strVID = 'VID_%04X' % self.usbDevVID
                strPID = 'PID_%04X' % self.usbDevPID
                if dbd.dbcc_name.find(strVID.upper()) >= 0 and dbd.dbcc_name.find(strPID.upper()) >= 0:
                    if action == 'add':
                        self.onDeviceArrived()
                    if action == 'remove':
                        self.onDeviceRemoved()

    def onDeviceArrived(self):
        pass

    def onDeviceRemoved(self):
        pass

    def onDiskAdd(self, drive):
        pass

    def onDiskRemove(self):
        pass

    def onCodecAdd(self):
        pass

    def onCodecRemove(self):
        pass







