from PySide6.QtWidgets import <PERSON><PERSON>lider, QWidget
from PySide6.QtCore import Qt, QTimer, Signal


class DelayedSlider(QSlider):
    valueChangedDelayed = Signal(int)

    def __init__(self, parent=None):
        super().__init__(parent)

        # 创建计时器
        self.timer = QTimer()
        self.timer.setSingleShot(True)
        self.timer.timeout.connect(self._on_timeout)

        # 设置默认延迟时间(毫秒)
        self.delay = 500

        # 存储最后的值
        self.last_value = self.value()

        # 连接信号
        self.sliderPressed.connect(self._on_slider_pressed)
        self.sliderReleased.connect(self._on_slider_released)
        self.valueChanged.connect(self._on_value_changed)

        # 滑动标记
        self.is_sliding = False

    def _on_slider_pressed(self):
        self.is_sliding = True
        self.timer.stop()

    def _on_slider_released(self):
        self.is_sliding = False
        self._trigger_update()

    def _on_value_changed(self, value):
        if not self.is_sliding:
            self._trigger_update()

    def _trigger_update(self):
        self.timer.stop()
        self.timer.start(self.delay)

    def _on_timeout(self):
        current_value = self.value()
        if current_value != self.last_value:
            self.valueChangedDelayed.emit(current_value)
            self.last_value = current_value

    def setDelay(self, ms):
        """设置延迟时间(毫秒)"""
        self.delay = ms

    def getDelay(self):
        """获取当前延迟时间"""
        return self.delay
