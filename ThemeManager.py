from PySide6.QtWidgets import (QVBoxLayout, QFrame)
from PySide6.QtCore import Qt

# 深色主题样式
# 深色主题样式
DARK_THEME = """
QMainWindow {
    background-color: #1E2128;
}

QWidget {
    color: #E8E9ED;
    font-size: 12px;  /* 添加字体大小，匹配浅色主题 */
}

QPushButton {
    background-color: #2D3748;
    border: 1px solid #4A5568;
    border-radius: 4px;  /* 减小圆角 */
    padding: 6px 12px;   /* 减小内边距 */
    color: #E8E9ED;
    font-weight: 500;
    min-width: 80px;     /* 减小最小宽度 */
    min-height: 28px;    /* 减小最小高度 */
    font-size: 12px;     /* 确保字体大小一致 */
    transition: background-color 0.2s ease;
}

QPushButton:hover {
    background-color: #3A4759;
    border-color: #63B3ED;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

QPushButton:pressed {
    background-color: #2C3444;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

QPushButton:disabled {
    color: #718096;
    background-color: #2D3748;
    border-color: #4A5568;
}

QListWidget {
    background-color: #2D3748;
    border: 1px solid #4A5568;
    border-radius: 4px;  /* 减小圆角 */
    padding: 2px;        /* 减小内边距 */
}

QListWidget::item {
    background-color: #3A4759;
    border-radius: 4px;  /* 减小圆角 */
    padding: 6px;        /* 减小内边距 */
    margin: 2px;
    transition: background-color 0.2s ease;
}

QListWidget::item:selected {
    background-color: #4A5568;
    color: #E8E9ED;
}

QListWidget::item:hover {
    background-color: #445166;
}

QTextEdit {
    background-color: #2D3748;
    border: 1px solid #4A5568;
    border-radius: 4px;  /* 减小圆角 */
    padding: 6px;        /* 减小内边距 */
    color: #E8E9ED;
    font-size: 12px;     /* 确保字体大小一致 */
}

QLabel {
    color: #E8E9ED;
    font-size: 12px;     /* 确保字体大小一致 */
}

QLabel[title="true"] {
    color: #63B3ED;
    font-weight: bold;
    font-size: 14px;     /* 减小标题字体 */
    padding: 4px 0;      /* 减小内边距 */
}

QCheckBox {
    spacing: 6px;        /* 减小间距 */
    color: #E8E9ED;
    font-size: 12px;     /* 确保字体大小一致 */
}

QCheckBox::indicator {
    width: 16px;         /* 减小指示器尺寸 */
    height: 16px;
    border: 2px solid #4A5568;
    border-radius: 3px;  /* 减小圆角 */
    background-color: #2D3748;
    transition: background-color 0.2s ease;
}

QCheckBox::indicator:checked {
    background-color: #63B3ED;
    border-color: #63B3ED;
}

QComboBox {
    background-color: #2D3748;
    border: 1px solid #4A5568;
    border-radius: 4px;  /* 减小圆角 */
    padding: 4px 8px;    /* 减小内边距 */
    color: #E8E9ED;
    min-width: 80px;     /* 减小最小宽度 */
    min-height: 28px;    /* 减小高度 */
    font-size: 12px;     /* 确保字体大小一致 */
}

QFrame#card {
    background-color: #2D3748;
    border-radius: 6px;  /* 减小圆角 */
    border: 1px solid #4A5568;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 添加滚动条样式以保持一致性 */
QScrollBar:vertical {
    border: none;
    background-color: #30343D;
    width: 8px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #4A5568;
    border-radius: 4px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #556279;
}

QScrollBar::add-line:vertical {
    height: 0px;
}

QScrollBar::sub-line:vertical {
    height: 0px;
}

QProgressBar {
    background-color: #30343D;
    border: none;
    border-radius: 2px;
    text-align: center;
    color: #E8E9ED;
    font-size: 12px;
    height: 4px;
}

QProgressBar::chunk {
    background-color: #63B3ED;
    border-radius: 2px;
}
"""

# 浅色主题样式
LIGHT_THEME = """
QMainWindow {
    background-color: #F7F9FC;
}

QWidget {
    color: #2D3748;
    font-size: 12px;  /* 调整为与 fluent 一致的字体大小 */
}

QPushButton {
    background-color: #FFFFFF;
    border: 1px solid #E2E8F0;
    border-radius: 4px;  /* 减小圆角 */
    padding: 6px 12px;   /* 减小内边距 */
    color: #2D3748;
    font-weight: 500;
    min-height: 28px;    /* 减小按钮高度 */
    min-width: 80px;     /* 减小最小宽度 */
    font-size: 12px;     /* 减小字体大小 */
    transition: background-color 0.2s ease;
}

QPushButton:hover {
    background-color: #EDF2F7;
    border-color: #4299E1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

QPushButton:pressed {
    background-color: #E2E8F0;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

QPushButton:disabled {
    color: #A0AEC0;
    background-color: #EDF2F7;
    border-color: #E2E8F0;
}

QListWidget {
    background-color: #FFFFFF;
    border: 1px solid #E2E8F0;
    border-radius: 4px;  /* 减小圆角 */
    padding: 2px;        /* 减小内边距 */
    font-size: 12px;
}

QListWidget::item {
    background-color: #F7FAFC;
    border-radius: 4px;
    padding: 6px;        /* 减小内边距 */
    margin: 2px;
    transition: background-color 0.2s ease;
}

QListWidget::item:selected {
    background-color: #EDF2F7;
    color: #2D3748;
}

QListWidget::item:hover {
    background-color: #F0F5FA;
}

QTextEdit {
    background-color: #FFFFFF;
    border: 1px solid #E2E8F0;
    border-radius: 4px;  /* 减小圆角 */
    padding: 6px;        /* 减小内边距 */
    color: #2D3748;
    font-size: 12px;
}

QLabel {
    color: #2D3748;
    font-size: 12px;
}

QLabel[title="true"] {
    color: #4299E1;
    font-weight: bold;
    font-size: 14px;     /* 减小标题字体 */
    padding: 4px 0;      /* 减小内边距 */
}

QCheckBox {
    spacing: 6px;        /* 减小间距 */
    color: #2D3748;
    font-size: 12px;
}

QCheckBox::indicator {
    width: 16px;         /* 减小指示器尺寸 */
    height: 16px;
    border: 1px solid #E2E8F0;
    border-radius: 3px;  /* 减小圆角 */
    background-color: #FFFFFF;
    transition: background-color 0.2s ease;
}

QCheckBox::indicator:checked {
    background-color: #4299E1;
    border-color: #4299E1;
}

QComboBox {
    background-color: #FFFFFF;
    border: 1px solid #E2E8F0;
    border-radius: 4px;  /* 减小圆角 */
    padding: 4px 8px;    /* 减小内边距 */
    color: #2D3748;
    min-width: 80px;     /* 减小最小宽度 */
    min-height: 28px;    /* 减小高度 */
    font-size: 12px;
}

QFrame#card {
    background-color: #FFFFFF;
    border-radius: 6px;  /* 减小圆角 */
    border: 1px solid #E2E8F0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 保持进度条样式一致 */
QProgressBar {
    background-color: #EDF2F7;
    border: none;
    border-radius: 2px;
    text-align: center;
    color: #2D3748;
    font-size: 12px;
    height: 4px;
}

QProgressBar::chunk {
    background-color: #4299E1;
    border-radius: 2px;
}

/* 添加滚动条样式以保持一致性 */
QScrollBar:vertical {
    border: none;
    background-color: #f5f5f5;
    width: 8px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #c1c1c1;
    border-radius: 4px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a1a1a1;
}

QScrollBar::add-line:vertical {
    height: 0px;
}

QScrollBar::sub-line:vertical {
    height: 0px;
}
"""

# 更新的样式常量 - 使用更柔和的配色方案
FLUENT_STYLES = """
QMainWindow {
    background-color: #fafafa;
}

QPushButton {
    background-color: #ffffff;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 6px 12px;
    color: #0067c0;
    font-weight: 500;
    min-height: 28px;
    transition: background-color 0.2s ease;
}

QPushButton:hover {
    background-color: #f5f9fd;
    border-color: #0067c0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

QPushButton:pressed {
    background-color: #e5f1fb;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

QPushButton:disabled {
    color: #999999;
    background-color: #f5f5f5;
    border-color: #e1e1e1;
}

QListWidget {
    background-color: white;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 2px;
}

QListWidget::item {
    border-radius: 4px;
    padding: 6px;
    margin: 2px;
    transition: background-color 0.2s ease;
}

QListWidget::item:selected {
    background-color: #e5f1fb;
    color: #000000;
}

QListWidget::item:hover {
    background-color: #f5f9fd;
}

QTextEdit {
    background-color: white;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 6px;
    font-size: 12px;
}

QLabel {
    color: #242424;
    font-size: 12px;
}

QLabel[title="true"] {
    color: #242424;
    font-weight: bold;
    font-size: 14px;
    padding-top: 4px;
    padding-bottom: 2px;
}

QCheckBox {
    spacing: 6px;
    font-size: 12px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #e1e1e1;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

QCheckBox::indicator:checked {
    background-color: #0067c0;
    border-color: #0067c0;
}

QComboBox {
    background-color: white;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 80px;
    min-height: 28px;
    font-size: 12px;
}

QComboBox::drop-down {
    border: none;
    padding-right: 2px;
}

QProgressBar {
    border: none;
    border-radius: 2px;
    background-color: #f0f0f0;
    height: 4px;
    text-align: center;
}

QProgressBar::chunk {
    background-color: #0067c0;
    border-radius: 2px;
}

QScrollBar:vertical {
    border: none;
    background-color: #f5f5f5;
    width: 8px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #c1c1c1;
    border-radius: 4px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a1a1a1;
}

QScrollBar::add-line:vertical {
    height: 0px;
}

QScrollBar::sub-line:vertical {
    height: 0px;
}
"""


class FluentCard(QFrame):
    """更紧凑的Fluent Design风格卡片容器"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("fluentCard")
        self.setStyleSheet("""
            #fluentCard {
                background-color: white;
                border-radius: 6px;
                border: 1px solid #e8e8e8;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
        """)

        self.setAttribute(Qt.WA_TranslucentBackground)

        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(8, 8, 8, 8)
        self.layout.setSpacing(6)


class ThemeManager:
    """主题管理器"""

    def __init__(self):
        self.current_theme = "fluent"
        self.themes = {
            "light": LIGHT_THEME,
            "dark": DARK_THEME,
            "fluent": FLUENT_STYLES
        }

    def get_current_theme(self):
        return self.themes[self.current_theme]

    def set_current_theme(self, theme_name):
        if theme_name in self.themes.keys():
            self.current_theme = theme_name

    def toggle_theme(self):
        if self.current_theme == "light":
            self.current_theme = "dark"
        elif self.current_theme == "dark":
            self.current_theme = "fluent"
        else:
            self.current_theme = "light"
        return self.get_current_theme()

