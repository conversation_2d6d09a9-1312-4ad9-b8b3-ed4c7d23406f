import requests
import json
import logging
import configparser


class ServerRequest(object):

    token = None
    client = None
    header = None
    device = None

    def __init__(self, IP='127.0.0.1', Port='8000'):
        self.baseUrl = 'http://' + IP + ':' + Port
        self.client = requests.session()
        self.header = {'Content-Type': 'application/json'}
        self.token = None
        self.device = None
        self.user = ''

    def userLogin(self, user, password):
        url = self.baseUrl + '/api/users/login/'
        data = {
            'username': user,
            'password': password,
        }
        try:
            res = self.client.post(url, data=json.dumps(data), headers=self.header, timeout=3)

            if not res.status_code == 200:
                logging.error('user: %s login failed, error: %s' % (user, res.json()))
                return False, res.json()

            self.token = "Token " + res.json()['auth_token']
            action = {'type': 'login', 'msg': 'User %s Login' % user}
            self.addRequestRecord(action)
            self.user = user
            return True, self.token
        except:
            logging.error("error IP addr")
            return False, 0xff

    def userLogout(self):
        url = self.baseUrl + '/api/users/logout/'
        if not self.token:
            return False, 'Please Login first.'

        action = {'type': 'logout', 'msg': 'User %s Logout' % self.user}
        self.addRequestRecord(action)

        self.header['Authorization'] = self.token
        try:
            res = self.client.post(url, headers=self.header)
        except Exception as e:
            logging.exception(e)
            return False, 'Connection to server error.'

        if not res.status_code == 200:
            logging.error('User logout failed, error: %s' % (res.json()))
            return False, res.json()

        self.token = None
        self.device = None
        self.user = ''
        return True, res.json()['detail']

    def getDeviceList(self, user):
        url = self.baseUrl + '/api/devices/list/'
        if not self.token:
            return False, 'Please Login first.'

        self.header['Authorization'] = self.token

        try:
            res = self.client.get(url, headers=self.header)
        except Exception as e:
            logging.exception(e)
            return False, 'Connection to server error.'

        if not res.status_code == 200:
            logging.error('Get device information failed, error: %s' % (res.json()))
            return False, res.json()

        devList = res.json()
        userDevList = []
        if len(devList) == 0:
            return False, 'No Device: %s found.'
        for dev in devList:
            if dev['user']['username'] == user:
                userDevList.append(dev)
        if len(userDevList) == 0:
            return False, 'No Device: %s found.'

        return True, userDevList

    def getDeviceInfo(self, device):
        url = self.baseUrl + '/api/devices/list/'
        if not self.token:
            return False, 'Please Login first.'

        self.header['Authorization'] = self.token

        try:
            res = self.client.get(url, headers=self.header)
        except Exception as e:
            logging.exception(e)
            return False, 'Connection to server error.'

        if not res.status_code == 200:
            logging.error('Get device information failed, error: %s' % (res.json()))
            return False, res.json()

        devList = res.json()
        if len(devList) == 0:
            return False, 'No Device: %s found.' % device
        for dev in devList:
            if dev['product'] == device:
                self.device = dev
        if not self.device:
            return False, 'No Device: %s found.' % device

        return True, self.device

    # Record: {'gains': 'FF_L: 6, FF_R: 6', 'device': 'SM733', 'date_created': '2020-03-21 10:13:15', 'devId': 'None'}
    def addTuningRecord(self, record):
        url = self.baseUrl + '/api/devices/record/'
        if not self.token:
            return False, 'Please Login first.'
        if not self.device or self.device['product'] != record['device']:
            self.getDeviceInfo(record['device'])

        if not self.device or self.device['product'] != record['device']:
            return False, 'Please Get Device Info first.'

        self.header['Authorization'] = self.token
        data = {
            'dev_id': record['devId'],
            'gains': record['gains'],
            'product': self.device,
            'date_created': record['date_created']
        }

        try:
            res = self.client.post(url, data=json.dumps(data), headers=self.header)
        except Exception as e:
            logging.exception(e)
            return False, 'Connection to server error.'

        if not res.status_code == 201 and not res.status_code == 208:
            logging.error('Adding tuning Record failed, error: %s' % (res.json()))
            return False, res.json()

        record = res.json()
        logging.info('Add Record: %s', record)

        return True, str(record)

    def addRequestRecord(self, action):
        url = self.baseUrl + '/api/devices/request/'
        if not self.token:
            return False, 'Please Login first.'

        if 'type' not in action:
            logging.error('Invalid action params.')
            return False, 'Invalid action params.'

        self.header['Authorization'] = self.token
        data = {
            'action': action
        }

        try:
            res = self.client.post(url, data=json.dumps(data), headers=self.header)
        except Exception as e:
            logging.exception(e)
            return False, 'Connection to server error.'

        if not res.status_code == 201:
            logging.error('Adding Request Record failed')
            return False, 'Adding Request Record failed.'
        return True, res.json()

    def getRawDataFromUrl(self, url, ftype='json'):
        if not self.token:
            return None
        self.header['Authorization'] = self.token

        try:
            res = self.client.get(url, headers=self.header)
        except Exception as e:
            logging.exception(e)
            return None

        if not res.status_code == 200:
            return None
        if ftype == 'json':
            return res.json()
        elif ftype == 'text':
            return res.text
        else:
            return res.content


def server_test():
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logging.info('ANC Server Test:')

    ip = '************'
    port = '9000'

    server = ServerRequest(ip, port)

    # user = 'sm01'
    # passwd = 'Yinkman2015'
    user = 'bqin'
    passwd = 'yinkman2015'

    ret, returnData = server.userLogin(user, passwd)
    if not ret:
        logging.error('%s login error: %s' % (user, returnData))
    else:
        logging.info('user: %s login succeed.' % user)

    '''
    url = 'http://127.0.0.1:8000/upload/params/params_sm739_i6olQp9.json'
    url2 = 'http://127.0.0.1:8000/upload/params/EvalueParams_zfbDleO.ini'
    server.getRawDataFromUrl(url, ftype='json')
    configText = server.getRawDataFromUrl(url2, ftype='text')
    config = configparser.ConfigParser()
    config.read_string(configText)
    sections = config.sections()
    for sec in sections:
        print(sec)
        print(config.items(sec))
    '''
    import os
    devName = 'SM-BT730NC-1'
    flashID = list(os.urandom(16))
    print(flashID)

    ret, devData = server.getDeviceInfo(devName)
    if not ret:
        logging.error("Get device info error: %s" % returnData)
        server.userLogout()
        return

    leftNum = devData["left_num"]
    if leftNum <= 0:    
        logging.error("授权数量不足！")
        server.userLogout()
        return
    action = {"type": "gen_auth_key", "num": 1, "device": devName, "rawId": flashID}
    ret, jsonData = server.addRequestRecord(action)
    if not ret:
        logging.error("请求授权失败！")
        server.userLogout()
        return

    print(eval(jsonData["action"])["cryptData"])
    # print(jsonData["action"]["cryptData"])

    ret, msg = server.userLogout()
    logging.info('%s, %s', ret, msg)

    '''
    dev = 'SM733'
    ret, returnData = server.getDeviceInfo(dev)
    if not ret:
        logging.error('Get device info error: %s' % returnData)
    else:
        logging.info('Get Device: \n %s' % returnData)

    devId = '3283402084003'
    gains = '183,33,4,2,5,2,56,66'
    ret, returnData = server.addTuningRecord(devId, gains)
    if not ret:
        logging.error('Add tuning record error: %s' % returnData)
    else:
        logging.info('Add tuning Record, left Authorization: \n %s' % returnData)
    '''


if __name__ == "__main__":
    server_test()



