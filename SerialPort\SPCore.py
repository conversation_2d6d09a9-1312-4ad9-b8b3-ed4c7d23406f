# -*- coding: utf-8 -*-

import serial
from SerialPort import SPOptions, SPSendThread, SPReceiveThread


class SerialPort(object):
    __sendString = True
    __receiveString = True

    def __init__(self, port='com1', baudrate=9600, parity='N', stop=1, bits=8, timeout=0.1):
        self.__serialPort = serial.Serial(port=port, baudrate=baudrate, parity=parity, stopbits=stop, bytesize=bits,
                                          timeout=timeout)
        self.sendThread = SPSendThread.SendThread(self, self.__serialPort)
        self.receiveThread = SPReceiveThread.ReceiveThread(self, self.__serialPort)
        self.sendThread.setSendString(self.__sendString)
        self.receiveThread.setReceiveString(self.__receiveString)
        self.sendThread.start()
        self.receiveThread.start()

    def open(self):
        if self.isOpen():
            self.close()
        try:
            # print(self.__serialPort.port)
            # print(self.__serialPort.baudrate)
            # print(self.__serialPort.bytesize)
            # print(self.__serialPort.parity)
            # print(self.__serialPort.stopbits)
            self.__serialPort.open()
            return True, "串口打开成功！"
        except Exception as e:
            msg = "无法打开串口！"
            if str(e).find("PermissionError") >= 0:
                msg = "串口 {!r} 已被占用！".format(str(self.__serialPort.port).upper())
            return False, msg

    def close(self):
        if self.isOpen():
            try:
                self.__serialPort.close()
                return True
            except:
                pass
        return False

    def isOpen(self):
        return self.__serialPort.isOpen()

    def setPort(self, port):
        try:
            self.__serialPort.port = port
            return True, "串口打开成功！"
        except Exception as e:
            msg = "串口打开失败！"
            if str(e).find("PermissionError") >= 0:
                msg = "串口 {!r} 已被占用！".format(str(self.__serialPort.port).upper())
            return False, msg

    def getPort(self):
        return self.__serialPort.port

    def setBaudrate(self, b):
        self.__serialPort.baudrate = b

    def setParity(self, p):
        self.__serialPort.parity = p

    def setByteSize(self, b):
        self.__serialPort.bytesize = b

    def setStopBits(self, s):
        self.__serialPort.stopbits = s

    def setSendString(self, e):
        self.__sendString = e
        self.sendThread.setSendString(e)

    def setReceiveString(self, e):
        self.__receiveString = e
        self.receiveThread.setReceiveString(e)

    @staticmethod
    def getAvailablePortNames():
        result = []
        coms = ['COM%s' % i for i in range(1, 257)]
        for port in coms:
            try:
                s = serial.Serial(port)
                s.close()
                result.append(port)
            except serial.SerialException as e:
                if str(e).find("PermissionError") >= 0:
                    result.append(port)
        if len(result) == 0:
            result.append("(无可用)")
        return result

    def getBaudrateNames(self):
        return SPOptions.Baudrate.BAUDRATES

    def getParityNames(self):
        return SPOptions.Parity.PARITY_NAMES

    def getByteSizeNames(self):
        return SPOptions.ByteSize.BYTESIZE_NAMES

    def getStopBitsNames(self):
        return SPOptions.StopBits.STOPBITS_NAMES

    def dataArrayToString(self, l):
        s = ""
        for i in l:
            s += (str(i) + " ")
        return s

    def byteToHexString(self, b):
        h = hex(b)
        h = h.replace("0x", "")
        if len(h) == 1:
            h = "0" + h
        return h.upper()

    def hexStringToByteArray(self, s):
        try:
            data = s.split()
            hexData = []
            for b in data:
                hexData.append(int(b, 16))
            dataBytes = bytes(hexData)
            return dataBytes
        except:
            return None
