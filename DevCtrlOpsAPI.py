import os
import ctypes as ct
import logging
import platform
import time
from functools import wraps
import pyDes
import binascii
from Crypto.Cipher import AES

import win32com.client

from BaseSubMainWindow import BaseResource


def isWin32():
    val = platform.architecture()
    if val[0] == '32bit':
        return True
    else:
        return False


def dllfuncwrapper(func):
    """Decorator to monitor DLL function calls."""
    @wraps(func)
    def new_fun(*args, **kwargs):
        """Create a function wrapper."""
        def to_bytes(args, kwargs):
            """Convert str arguments to UTF-8 bytes."""
            args = list(args)
            for idx, val in enumerate(args):
                if isinstance(val, str):
                    args[idx] = bytes(val, encoding="UTF-8")
            for key in kwargs:
                if isinstance(kwargs[key], str):
                    kwargs[key] = bytes(kwargs[key], encoding="UTF-8")
            return tuple(args), kwargs

        def to_str(retval):
            """Convert byte return values to str"""
            if isinstance(retval, tuple):
                vals = []
                for item in retval:
                    if isinstance(item, bytes):
                        vals.append(item.decode())
                    else:
                        vals.append(item)
                return tuple(vals)
            if isinstance(retval, bytes):
                return retval.decode()
            return retval

        args, kwargs = to_bytes(args, kwargs)
        result = to_str(func(*args, **kwargs))
        return result
    return new_fun


def retry(max_retries=3, delay=0):
    def decorator(func):
        def wrapper(*args, **kwargs):
            result = 0
            for _ in range(max_retries):
                result = func(*args, **kwargs)
                if isinstance(result,tuple):
                    if result[0] >= 0:
                        return result
                else:
                    if result >= 0:
                        return result
                time.sleep(delay)
            return result
        return wrapper
    return decorator


class DevCtrlOps:
    dllFile = 'Ls900SDK.dll'
    """
    A python wrapper class for CSK uart burn DLL.
    """
    def __init__(self, path_to_dll='Ls900SDK\\', logInfo=None):
        """Load the TestFlash DLL"""
        try:
            if isWin32():
                path_to_dll = path_to_dll + 'x86\\'
            else:
                path_to_dll = path_to_dll + 'x64\\'
            path_to_dll = BaseResource.resource_path(path_to_dll)
            logging.info('SDK Path: %s', path_to_dll)
            self.devCtrlDLL = ct.CDLL(path_to_dll + self.dllFile)
            if logInfo:
                self.logInfo = logInfo
            else:
                self.logInfo = logging.info
        except Exception as e:
            logging.error("Cannot load Ls900SDK.dll from " + path_to_dll)
            if ct.sizeof(ct.c_void_p) == 8:
                logging.error("Check that the DLL is present and 64 bit."
                      "64 bit Python can only be used with 64 bit DLL")
            else:
                logging.error("Check that the DLL is present and 32 bit"
                      "32 bit Python can only be used with 32 bit DLL")
            raise e

    RET_OK = 0
    RET_ERROR = -1
    REPEAT_TIMES = 3

    @dllfuncwrapper
    def lsHpSDKVersion(self):
        self.devCtrlDLL.lsHpSDKVersion.restype = ct.c_int32
        self.devCtrlDLL.lsHpSDKVersion.argtypes = []
        
        retVal = self.devCtrlDLL.lsHpSDKVersion()
        return retVal

    @dllfuncwrapper
    def lsHpMgrInit(self):
        self.devCtrlDLL.lsHpMgrInit.restype = None
        self.devCtrlDLL.lsHpMgrInit.argtypes = []

        self.devCtrlDLL.lsHpMgrInit()
        return

    @dllfuncwrapper
    def lsHpMgrDeinit(self):
        self.devCtrlDLL.lsHpMgrDeinit.restype = None
        self.devCtrlDLL.lsHpMgrDeinit.argtypes = []

        self.devCtrlDLL.lsHpMgrDeinit()
        return

    @dllfuncwrapper
    def lsHpMgrConfig(self, vendorId: int, productId: int):
        self.devCtrlDLL.lsHpMgrConfig.restype = None
        self.devCtrlDLL.lsHpMgrConfig.argtypes = [ct.c_uint16, ct.c_uint16]

        self.devCtrlDLL.lsHpMgrConfig(vendorId, productId)
        return

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetHeadsets(self):
        self.devCtrlDLL.lsHpGetHeadsets.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetHeadsets.argtypes = [ct.c_void_p]

        hpIds = (ct.c_int * 32)(0)

        retVal = self.devCtrlDLL.lsHpGetHeadsets(hpIds)
        return retVal, hpIds[:]

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetFwVersion(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetFwVersion.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetFwVersion.argtypes = [ct.c_int, ct.c_void_p, ct.c_void_p]

        verInfo = ct.c_uint16(0)
        buildInfo = (ct.c_uint8 * 64)(0)

        retVal = self.devCtrlDLL.lsHpGetFwVersion(HeadsetId, ct.byref(verInfo), buildInfo)
        return retVal, verInfo.value, buildInfo[:]

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetDeviceID(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetDeviceID.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetDeviceID.argtypes = [ct.c_int, ct.c_void_p]

        idBuf = (ct.c_uint8 * 64)(0)

        retVal = self.devCtrlDLL.lsHpGetDeviceID(HeadsetId, idBuf)
        return retVal, idBuf[:]

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetDeviceID(self, HeadsetId: int, idData: list):
        self.devCtrlDLL.lsHpSetDeviceID.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetDeviceID.argtypes = [ct.c_int, ct.c_void_p]

        idBuf = (ct.c_uint8 * len(idData))(*idData)

        return self.devCtrlDLL.lsHpSetDeviceID(HeadsetId, idBuf)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetDenoiseMode(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetDenoiseMode.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetDenoiseMode.argtypes = [ct.c_int, ct.c_void_p]

        mode = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGetDenoiseMode(HeadsetId, ct.byref(mode))
        return retVal, mode.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetDenoiseMode(self, HeadsetId: int, mode: int):
        self.devCtrlDLL.lsHpSetDenoiseMode.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetDenoiseMode.argtypes = [ct.c_int, ct.c_uint8]

        return self.devCtrlDLL.lsHpSetDenoiseMode(HeadsetId, mode)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetDenoiseAlgCh(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetDenoiseAlgCh.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetDenoiseAlgCh.argtypes = [ct.c_int, ct.c_void_p]

        algCh = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGetDenoiseAlgCh(HeadsetId, ct.byref(algCh))
        return retVal, algCh.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetDenoiseAlgCh(self, HeadsetId: int, algCh: int):
        self.devCtrlDLL.lsHpSetDenoiseAlgCh.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetDenoiseAlgCh.argtypes = [ct.c_int, ct.c_uint8]

        return self.devCtrlDLL.lsHpSetDenoiseAlgCh(HeadsetId, algCh)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetMicDataFmt(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetMicDataFmt.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetMicDataFmt.argtypes = [ct.c_int, ct.c_void_p]

        fmt = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGetMicDataFmt(HeadsetId, ct.byref(fmt))
        return retVal, fmt.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetMicDataFmt(self, HeadsetId: int, fmt: int):
        self.devCtrlDLL.lsHpSetMicDataFmt.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetMicDataFmt.argtypes = [ct.c_int, ct.c_uint8]

        return self.devCtrlDLL.lsHpSetMicDataFmt(HeadsetId, fmt)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetAlgMethod(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetAlgMethod.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetAlgMethod.argtypes = [ct.c_int, ct.c_void_p]

        method = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGetAlgMethod(HeadsetId, ct.byref(method))
        return retVal, method.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetAlgMethod(self, HeadsetId: int, method: int):
        self.devCtrlDLL.lsHpSetAlgMethod.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetAlgMethod.argtypes = [ct.c_int, ct.c_uint8]

        return self.devCtrlDLL.lsHpSetAlgMethod(HeadsetId, method)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetMicGain(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetMicGain.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetMicGain.argtypes = [ct.c_int, ct.c_void_p]

        gain = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGetMicGain(HeadsetId, ct.byref(gain))
        return retVal, gain.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetMicGain(self, HeadsetId: int, gain: int):
        self.devCtrlDLL.lsHpSetMicGain.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetMicGain.argtypes = [ct.c_int, ct.c_uint8]

        return self.devCtrlDLL.lsHpSetMicGain(HeadsetId, gain)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetLedState(self, HeadsetId: int, led):
        self.devCtrlDLL.lsHpGetLedState.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetLedState.argtypes = [ct.c_int, ct.c_uint8, ct.c_void_p]

        state = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGetLedState(HeadsetId, led, ct.byref(state))
        return retVal, state.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetLedState(self, HeadsetId: int, led: int, state: int, period: int):
        self.devCtrlDLL.lsHpSetLedState.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetLedState.argtypes = [ct.c_int, ct.c_uint8, ct.c_uint8, ct.c_uint8]

        return self.devCtrlDLL.lsHpSetLedState(HeadsetId, led, state, period)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetWaterMarkState(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetWaterMarkState.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetWaterMarkState.argtypes = [ct.c_int, ct.c_void_p]

        state = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGetWaterMarkState(HeadsetId, ct.byref(state))
        return retVal, state.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetWaterMarkState(self, HeadsetId: int, state: int):
        self.devCtrlDLL.lsHpSetWaterMarkState.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetWaterMarkState.argtypes = [ct.c_int, ct.c_uint8]

        return self.devCtrlDLL.lsHpSetWaterMarkState(HeadsetId, state)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetWaterMarkMsg(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetWaterMarkMsg.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetWaterMarkMsg.argtypes = [ct.c_int, ct.c_void_p]

        wmMsg = (ct.c_uint8 * 64)(0)

        retVal = self.devCtrlDLL.lsHpGetWaterMarkMsg(HeadsetId, wmMsg)
        return retVal, wmMsg[:]

    @retry(max_retries=REPEAT_TIMES)
    def lsHpSetWaterMarkMsg(self, HeadsetId: int, wmMsg: list):
        self.devCtrlDLL.lsHpSetWaterMarkMsg.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetWaterMarkMsg.argtypes = [ct.c_int, ct.c_void_p]

        wmMsg = (ct.c_uint8 * len(wmMsg))(*wmMsg)

        return self.devCtrlDLL.lsHpSetWaterMarkMsg(HeadsetId, wmMsg)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetRmsTh(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetRmsTh.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetRmsTh.argtypes = [ct.c_int, ct.c_void_p]

        rmsTh = ct.c_int8(0)

        retVal = self.devCtrlDLL.lsHpGetRmsTh(HeadsetId, ct.byref(rmsTh))
        return retVal, rmsTh.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetRmsTh(self, HeadsetId: int, rmsTh: int):
        self.devCtrlDLL.lsHpSetRmsTh.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetRmsTh.argtypes = [ct.c_int, ct.c_int8]

        return self.devCtrlDLL.lsHpSetRmsTh(HeadsetId, rmsTh)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetUdiskState(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetUdiskState.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetUdiskState.argtypes = [ct.c_int, ct.c_void_p]

        state = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGetUdiskState(HeadsetId, ct.byref(state))
        return retVal, state.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetUdiskState(self, HeadsetId: int, state: int):
        self.devCtrlDLL.lsHpSetUdiskState.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetUdiskState.argtypes = [ct.c_int, ct.c_uint8]

        return self.devCtrlDLL.lsHpSetUdiskState(HeadsetId, state)

    '''
    typedef struct
    {
        uint32_t state;
        uint32_t attack_time;
        uint32_t release_time;
        int32_t min_level;
        int32_t target_level;
        int32_t auto_gain;
    } agc_params_t;
    '''
    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetAgcParams(self, HeadsetId: int, agcDict: dict):
        self.devCtrlDLL.lsHpSetAgcParams.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetAgcParams.argtypes = [ct.c_int, ct.c_void_p]

        agcParams = (ct.c_uint32 * 6)(0)
        agcParams[0] = agcDict['state']
        agcParams[1] = agcDict['attack_time']
        agcParams[2] = agcDict['release_time']
        agcParams[3] = agcDict['min_level']
        agcParams[4] = agcDict['target_level']
        agcParams[5] = agcDict['auto_gain']

        return self.devCtrlDLL.lsHpSetAgcParams(HeadsetId, agcParams)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetAgcParams(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetAgcParams.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetAgcParams.argtypes = [ct.c_int, ct.c_void_p]

        agcParams = (ct.c_uint32 * 6)(0)

        retVal = self.devCtrlDLL.lsHpGetAgcParams(HeadsetId, agcParams)
        return retVal, {'state': agcParams[0], 'attack_time': agcParams[1], 'release_time': agcParams[2],
                        'min_level': agcParams[3], 'target_level': agcParams[4], 'auto_gain': agcParams[5]}

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetProductData(self, HeadsetId: int, index: int):
        self.devCtrlDLL.lsHpGetProductData.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetProductData.argtypes = [ct.c_int, ct.c_int, ct.c_void_p]

        productData = (ct.c_uint8 * 64)(0)

        retVal = self.devCtrlDLL.lsHpGetProductData(HeadsetId, index, productData)
        return retVal, productData[:]

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetProductData(self, HeadsetId: int, index: int, data: list):
        self.devCtrlDLL.lsHpSetProductData.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetProductData.argtypes = [ct.c_int, ct.c_int, ct.c_int, ct.c_void_p]

        productData = (ct.c_uint8 * len(data))(*data)

        return self.devCtrlDLL.lsHpSetProductData(HeadsetId, index, len(data), productData)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGeActivateState(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGeActivateState.restype = ct.c_int32
        self.devCtrlDLL.lsHpGeActivateState.argtypes = [ct.c_int, ct.c_void_p]

        state = ct.c_uint8(0)

        retVal = self.devCtrlDLL.lsHpGeActivateState(HeadsetId, ct.byref(state))
        return retVal, state.value

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpSetCryptoData(self, HeadsetId: int, data: list):
        self.devCtrlDLL.lsHpSetCryptoData.restype = ct.c_int32
        self.devCtrlDLL.lsHpSetCryptoData.argtypes = [ct.c_int, ct.c_int, ct.c_void_p]

        cryptoData = (ct.c_uint8 * len(data))(*data)

        return self.devCtrlDLL.lsHpSetCryptoData(HeadsetId, len(data), cryptoData)

    @retry(max_retries=REPEAT_TIMES)
    @dllfuncwrapper
    def lsHpGetFlashUID(self, HeadsetId: int):
        self.devCtrlDLL.lsHpGetFlashUID.restype = ct.c_int32
        self.devCtrlDLL.lsHpGetFlashUID.argtypes = [ct.c_int, ct.c_void_p]

        flashUIDData = (ct.c_uint8 * 16)(0)

        retVal = self.devCtrlDLL.lsHpGetFlashUID(HeadsetId, flashUIDData)
        return retVal, flashUIDData[:]

    @dllfuncwrapper
    def lsHpFactoryReset(self, HeadsetId: int):
        self.devCtrlDLL.lsHpFactoryReset.restype = ct.c_int32
        self.devCtrlDLL.lsHpFactoryReset.argtypes = [ct.c_int]

        retVal = self.devCtrlDLL.lsHpFactoryReset(HeadsetId)
        return retVal

    @dllfuncwrapper
    def lsHpEnterOtaMode(self, HeadsetId: int):
        self.devCtrlDLL.lsHpEnterOtaMode.restype = ct.c_int32
        self.devCtrlDLL.lsHpEnterOtaMode.argtypes = [ct.c_int]

        retVal = self.devCtrlDLL.lsHpEnterOtaMode(HeadsetId)
        return retVal

    @staticmethod
    def checkUDiskInfo():
        wmi = win32com.client.GetObject("winmgmts:")
        devList = wmi.InstancesOf("Win32_DiskDrive")
        resultList = []
        for dev in devList:
            if dev.InterfaceType == 'USB':
                # print(dev.PNPDeviceID, dev.DeviceID, dev.Size, dev.CapabilityDescriptions)
                resultList.append({'drive': dev.DeviceID.replace('\\', '').strip('.')})

        devList = wmi.InstancesOf("Win32_DiskDriveToDiskPartition")
        for dev in devList:
            # print(dev.Path_)
            strList = str(dev.Path_).split('"')
            diskDrive = strList[2].replace('\\', '').strip('.')
            diskPartition = strList[6].replace('\\', '')
            for result in resultList:
                if result['drive'] == diskDrive:
                    result['partition'] = diskPartition

        devList = wmi.InstancesOf("Win32_LogicalDiskToPartition")
        for dev in devList:
            # print(dev.Path_)
            strList = str(dev.Path_).split('\\"')
            for result in resultList:
                if result['partition'] == strList[1]:
                    result['volume'] = strList[3]

        return resultList

    def deviceActivate(self, HeadsetId: int):
        ret, flashUIDBytes = self.lsHpGetFlashUID(HeadsetId)
        if ret <= 0:
            logging.error('Get flash UID error.')
            return False

        flashUIDBytes = bytes(flashUIDBytes)
        aesKey = os.urandom(16)

        # logging.info('Flash UID: %s, AES Key: %s', binascii.b2a_hex(flashUIDBytes), binascii.b2a_hex(aesKey))

        desKey = aesKey[:8][::-1]
        des = pyDes.des(desKey, pyDes.ECB, desKey, pad=None, padmode=pyDes.PAD_NORMAL)
        desData0 = des.encrypt(flashUIDBytes[:8][::-1])
        desKey = aesKey[8:][::-1]
        des = pyDes.des(desKey, pyDes.ECB, desKey, pad=None, padmode=pyDes.PAD_NORMAL)
        desData1 = des.encrypt(flashUIDBytes[8:][::-1])
        desData = desData0[::-1] + desData1[::-1]

        aes = AES.new(aesKey, AES.MODE_ECB)
        aesData = aes.encrypt(flashUIDBytes)

        '''
        logging.info('FlashID[0]: %s, [1]: %s', binascii.b2a_hex(flashUIDBytes[:8][::-1]),
                     binascii.b2a_hex(flashUIDBytes[8:][::-1]))
        logging.info('DES: key[0] %s, data[0]: %s, key[1] %s, data[1]: %s',
                     binascii.b2a_hex(aesKey[:8][::-1]), binascii.b2a_hex(desData0),
                     binascii.b2a_hex(aesKey[8:][::-1]), binascii.b2a_hex(desData1))
        logging.info('DES Data: %s, AES Data: %s', binascii.b2a_hex(desData), binascii.b2a_hex(aesData))
        '''

        cryptData = aesData[8:] + aesKey[:8] + aesData[:8] + desData[8:] + aesKey[8:] + desData[:8]

        ret = self.lsHpSetCryptoData(HeadsetId, list(cryptData))
        if ret != 0:
            logging.error('Set crypto data error!')
            return False

        return True


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    #LibPath = r'./Ls900SDK/'
    devCtrl = DevCtrlOps()

    sdkVer = devCtrl.lsHpSDKVersion()
    logging.info('SDK Version: %d.%02d', (sdkVer >> 8) & 0xff, sdkVer & 0xff)

    devCtrl.lsHpMgrInit()
    devCtrl.lsHpMgrConfig(0x291A, 0x3301)
    # devCtrl.lsHpMgrConfig(0x17EF, 0x6173)

    ret, hpIds = devCtrl.lsHpGetHeadsets()
    if ret <= 0:
        logging.info('No valid HP found!')
        exit(0)
    else:
        logging.info('%d HP Found, %s', ret, hpIds[:ret])

    ret, ver, build = devCtrl.lsHpGetFwVersion(hpIds[0])
    if ret != 0:
        logging.error('Ls900SDK: get fw version error!')
    else:
        while build and build[-1] == 0:
            build.pop()
        logging.info('Fw version: %d.%02d, build: %s', (ver >> 8) & 0xff, ver & 0xff, str(bytes(build), encoding="utf-8"))

    '''
    ret, devID = devCtrl.lsHpGetDeviceID(hpIds[0])
    if ret < 0:
        logging.error('Ls900SDK: get device ID error!')
    else:
        logging.info('Device ID: %s', str(bytes(devID[:ret]), encoding="utf-8"))

    ret, mode = devCtrl.lsHpGetDenoiseMode(hpIds[0])
    if ret < 0:
        logging.error('Ls900SDK: get denoise Level error!')
    else:
        modeStr = ['level 0', 'level 1', 'level 2', 'level 3', 'level 4', 'Bypass']
        logging.info('Denoise Level: %s', modeStr[mode])

    ret, fmt = devCtrl.lsHpGetMicDataFmt(hpIds[0])
    if ret < 0:
        logging.error('Ls900SDK: get MIC Output format error!')
    else:
        fmtStr = ['ENC', 'Original L', 'Original R']
        logging.info('MIC Output format: L = %s, R = %s', fmtStr[fmt & 0x0f], fmtStr[(fmt >> 4) & 0x0f])

    ledStr = ['BLUE', 'RED', 'GREEN']
    ledStateStr = ["OFF", "ON", "FLASH"]
    for led in range(3):
        ret, state = devCtrl.lsHpGetLedState(hpIds[0], 1 << led)
        if ret < 0:
            logging.error('Ls900SDK: get LED %s state error!', ledStr[led])
        else:
            logging.info('LED %s state: %s', ledStr[led], ledStateStr[state])

    ret, state = devCtrl.lsHpGetWaterMarkState(hpIds[0])
    if ret < 0:
        logging.error('Ls900SDK: get WaterMark state error!')
    else:
        stateStr = ['OFF', 'ON']
        logging.info('WaterMark state: %s', stateStr[state])

    ret, wmMsg = devCtrl.lsHpGetWaterMarkMsg(hpIds[0])
    if ret < 0:
        logging.error('Ls900SDK: get WaterMark message error!')
    else:
        logging.info('WaterMark message: %s', str(bytes(wmMsg[:ret]), encoding="utf-8"))

    ret, rmsTh = devCtrl.lsHpGetRmsTh(hpIds[0])
    if ret < 0:
        logging.error('Ls900SDK: get MIC Signal RMS threshold error!')
    else:
        logging.info('MIC Signal RMS threshold: %ddB', rmsTh)

    ret, state = devCtrl.lsHpGetUdiskState(hpIds[0])
    if ret < 0:
        logging.error('Ls900SDK: get U-Disk state error!')
    else:
        stateStr = ['OFF', 'ON']
        logging.info('U-Disk state: %s', stateStr[state])
    '''

    '''
    idx = 0
    for idx in range(0):
        data = [0]*60
        ret = devCtrl.lsHpSetProductData(hpIds[0], idx, data)
        if ret < 0:
            logging.error('Set Production[%d] error!', idx)

    idx = 0
    for idx in range(2):
        ret, productData = devCtrl.lsHpGetProductData(hpIds[0], idx)
        if ret > 0:
            logging.info('Get Production[%d]: %s', idx, productData)
    '''

    '''
    if not devCtrl.deviceActivate(hpIds[0]):
        logging.error('Device Activate error!')
    else:
        logging.info('Device Activate success!')
    '''

    '''
    ret, state = devCtrl.lsHpGeActivateState(hpIds[0])
    if ret < 0:
        logging.error('Ls900SDK: get Activate state error!')
    else:
        logging.info('Activate state: %s', state)

    ret, flashIDData = devCtrl.lsHpGetFlashUID(hpIds[0])
    if ret > 0:
        logging.info('Get Flash UID: %s', flashIDData[:ret])
    else:
        logging.error('Get flash UID error.')
    '''


    cryptData = [0]*48
    ret = devCtrl.lsHpSetCryptoData(hpIds[0], list(cryptData))
    if ret != 0:
        logging.error('Set crypto data error!')
    else:
        logging.info('Clear crypto data.')


    '''
    ret = devCtrl.lsHpFactoryReset(hpIds[0])
    logging.info('ret = %d', ret)
    '''

    '''
    ret = devCtrl.lsHpEnterOtaMode(hpIds[0])
    logging.info('ret = %d', ret)
    '''

    devCtrl.lsHpMgrDeinit()

 