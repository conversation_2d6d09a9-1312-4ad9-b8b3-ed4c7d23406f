# -*- coding: utf-8 -*-
import configparser
import json
import logging
import os

from BaseSubMainWindow import BaseResource


class ConfigParser(object):

    @staticmethod
    def saveJsonFile(saveData, json_file='params.json'):
        logging.info('Save Data to %s', json_file)
        with open(json_file, 'w') as outfile:
            json.dump(saveData, outfile, sort_keys=False, indent=4)

    @staticmethod
    def loadJsonFile(filename='params.json'):
        logging.info('Load Json Data from %s', filename)
        with open(filename) as json_file:
            data = json.load(json_file)
            return data

    @staticmethod
    def saveConfigFile(config, filename):
        configFile = BaseResource.getConfigFileFullName(filename)
        with open(configFile, 'w') as fp:
            # logging.info('Write config to: %s', configFile)
            config.write(fp)

    @staticmethod
    def getAppConfig(filename='Config.ini'):
        config = configparser.ConfigParser()
        config.optionxform = str
        configFile = BaseResource.getConfigFileFullName(filename)
        if os.path.isfile(configFile):
            config.read(configFile)
            ret = config.has_section('Log')
            ret &= config.has_option('Log', 'level')
            ret &= config.has_section('Setting')
            if ret:
                logging.info('Read App config from %s', configFile)
                return config

        # Default Config Item value
        config.clear()
        logging.info('Using Default App Configuration')
        config.add_section('Log')
        config.set('Log', 'level', 'info')

        config.add_section('Setting')
        config.add_section('Config')

        ConfigParser.saveConfigFile(config, filename)

        return config


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')




