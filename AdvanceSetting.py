import sys
import logging
from PySide6 import QtCore, QtGui, QtWidgets
from BaseSubMainWindow import BaseSubDialog
from ui_SettingDialog import Ui_DialogSetting
from CsvFileOps import RecordCsvFileOps
import serial
import serial.tools.list_ports


class AdvancedSetting(BaseSubDialog):

    def __init__(self, config=None, limitConfig = None, parent=None):
        super(AdvancedSetting, self).__init__()
        self.ui = Ui_DialogSetting()
        self.ui.setupUi(self)

        self.srvHandle = None
        self.config = config
        self.comPortList = []

        self.ui.tableWidget.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)
        self.ui.tableWidgetMicGrade.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)
        self.ui.tableWidgetRingConfig.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)

        self.updateComDevList()
        self.updateUIFromConfig(self.config)
        self.onCheckPlayDevice()
        self.onCheckRecordDevice()

        self.comDevMenu = QtWidgets.QMenu(self)
        self.rescanDev = QtGui.QAction("重新扫描", self)
        self.rescanDev.triggered.connect(self.updateComDevList)
        self.comDevMenu.addAction(self.rescanDev)
        self.ui.gpTestBoxConfig.setContextMenuPolicy(QtCore.Qt.ContextMenuPolicy.CustomContextMenu)
        self.ui.gpTestBoxConfig.customContextMenuRequested.connect(self.createComDevRightMenu)

        self.ui.btnFreqLimitParams.clicked.connect(self.onOpenFreqLimitParams)
        self.ui.btnAdd.clicked.connect(self.addTableRow)
        self.ui.btnDelete.clicked.connect(self.deleteTableRow)
        self.ui.btnClear.clicked.connect(self.clearTHDTableData)

        self.ui.btnMicGradeAdd.clicked.connect(self.addMicGradeTableRow)
        self.ui.btnMicGradeDelete.clicked.connect(self.deleteMicGradeTableRow)
        self.ui.btnMicGradeClear.clicked.connect(self.clearMicGradeTableData)

        self.ui.checkBoxPlayDevice.stateChanged.connect(self.onCheckPlayDevice)
        self.ui.checkBoxRecordDevice.stateChanged.connect(self.onCheckRecordDevice)

    def updateUIFromConfig(self, config):
        if config is not None:
            if config.has_option('Setting', 'sample_rate'):
                self.ui.comboBoxSamplerate.setCurrentText(config['Setting']['sample_rate'])
            if config.has_option('Setting', 'sample_len'):
                self.ui.spinBoxTimeLen.setValue(float(config['Setting']['sample_len']))
            if config.has_option('Setting', 'fft_size'):
                self.ui.comboBoxFFTSize.setCurrentText(config['Setting']['fft_size'])
            if config.has_option('Setting', 'signal_type'):
                sigTypeList = ['WhiteNoise', 'SweepSin(20-20KHz)']
                sigType = sigTypeList.index(config['Setting']['signal_type'])
                self.ui.comboBoxAudioType.setCurrentIndex(sigType)
            if config.has_option('Setting', 'save_audio'):
                self.ui.checkBoxSaveAudio.setChecked(eval(config['Setting']['save_audio']))
            if config.has_option('Setting', 'save_fft'):
                self.ui.checkBoxSaveFFT.setChecked(eval(config['Setting']['save_fft']))
            if config.has_option('Setting', 'show_limit_config'):
                self.ui.checkBoxShowLimitConfig.setChecked(eval(config['Setting']['show_limit_config']))
            if config.has_option('Setting', 'freq_test_enable'):
                self.ui.gpCodecFreqParams.setChecked(eval(config['Setting']['freq_test_enable']))
            if config.has_option('Setting', 'update_snd_first'):
                self.ui.checkBoxUpdateSndFirst.setChecked(eval(config['Setting']['update_snd_first']))
            if config.has_option('Setting', 'erb_band_enable'):
                self.ui.checkBoxERBBand.setChecked(eval(config['Setting']['erb_band_enable']))

            if config.has_option('Setting', 'specify_snd_play_enable'):
                self.ui.checkBoxPlayDevice.setChecked(eval(config['Setting']['specify_snd_play_enable']))
            if config.has_option('Setting', 'specify_snd_record_enable'):
                self.ui.checkBoxRecordDevice.setChecked(eval(config['Setting']['specify_snd_record_enable']))
            if config.has_option('Setting', 'specify_snd_play_name'):
                self.ui.leditPlayDeviceName.setText(config['Setting']['specify_snd_play_name'])
            if config.has_option('Setting', 'specify_snd_record_name'):
                self.ui.leditRecordDeviceName.setText(config['Setting']['specify_snd_record_name'])

            if config.has_option('Setting', 'thd_test_enable'):
                self.ui.gpTHDTestConfig.setChecked(eval(config['Setting']['thd_test_enable']))
            if config.has_option('Config', 'thd_config_data'):
                thdCfgData = eval(config.get('Config', 'thd_config_data'))
                self.updateTHDConfigData(thdCfgData)

            if config.has_option('Setting', 'mic_grade_enable'):
                self.ui.gpMicGradeConfig.setChecked(eval(config['Setting']['mic_grade_enable']))
            if config.has_option('Config', 'mic_grade_data'):
                micGradeCfgData = eval(config.get('Config', 'mic_grade_data'))
                self.updateMicGradeData(micGradeCfgData)

            if config.has_option('Setting', 'dev_ring_test_enable'):
                self.ui.gpDevRingConfig.setChecked(eval(config['Setting']['dev_ring_test_enable']))
            if config.has_option('DeviceConfig', 'fft_test_enable'):
                self.ui.checkBoxRingFreqTestEnable.setChecked(eval(config['DeviceConfig']['fft_test_enable']))
            if config.has_option('DeviceConfig', 'thd_test_enable'):
                self.ui.checkBoxRingThdTestEnable.setChecked(eval(config['DeviceConfig']['thd_test_enable']))
            if config.has_option('DeviceConfig', 'stop_if_fail'):
                self.ui.checkBoxRingFailStop.setChecked(eval(config['DeviceConfig']['stop_if_fail']))
            if config.has_option('DeviceConfig', 'limit_cfg'):
                limitConfig = eval(config['DeviceConfig']['limit_cfg'])
                self.updateTableRingData(limitConfig)

            if config.has_option('BoxConfig', 'com_port'):
                comPort = config.get('BoxConfig', 'com_port')
                if comPort and comPort in self.comPortList:
                    self.ui.comboComportBox.setCurrentIndex(self.comPortList.index(comPort))
            if config.has_option('BoxConfig', 'baudrate'):
                baudrate = config.get('BoxConfig', 'baudrate')
                if baudrate and baudrate != self.ui.comboBaudrateBox.currentText():
                    self.ui.comboBaudrateBox.clear()
                    self.ui.comboBaudrateBox.addItem(baudrate)
                    self.ui.comboBaudrateBox.setCurrentIndex(0)
            if config.has_option('BoxConfig', 'auto_test_enable'):
                self.ui.checkBoxAutoTest.setChecked(eval(config['BoxConfig']['auto_test_enable']))

    def closeEvent(self, event):
        logging.info('Setting window Close Event.')

    def fftSizeChange(self, index):
        logging.info('fft size change: %s', self.ui.comboBoxFFTSize.currentIndex())

    def onOpenFreqLimitParams(self):
        logging.info('Open Ambient Limit params window.')
        if self.freqLimitCfg:
            sampleRate = int(self.ui.comboBoxSamplerate.currentText())
            fftSize = int(self.ui.comboBoxFFTSize.currentText())
            recLen = self.ui.spinBoxTimeLen.value()
            saveAudio = self.ui.checkBoxSaveAudio.isChecked()
            saveFFT = self.ui.checkBoxSaveFFT.isChecked()
            self.freqLimitCfg.setFreqParams(sampleRate, fftSize, recLen, saveAudio, saveFFT)
            self.freqLimitCfg.exec()

            if self.config is not None:
                self.config.set('Setting', 'limit_balance_max', str(self.freqLimitCfg.ui.doubleSpinBoxBalanceMax.value()))
                self.config.set('Setting', 'limit_freq_start', str(self.freqLimitCfg.ui.lineEditLRFreqStart.text()))
                self.config.set('Setting', 'limit_freq_end', str(self.freqLimitCfg.ui.lineEditLRFreqEnd.text()))

    def clearTHDTableData(self):
        logging.info('Clear all THDN Table data.')
        rowCnt = self.ui.tableWidget.rowCount()
        while rowCnt > 0:
            self.ui.tableWidget.removeRow(0)
            rowCnt = self.ui.tableWidget.rowCount()

    def getTHDConfigData(self):
        logging.info('Get THDN Config data from table.')
        thdCfgData = []
        rowCnt = self.ui.tableWidget.rowCount()
        for idx in range(rowCnt):
            freq = None
            amp = None
            limit = None
            thdIdx = None
            item = self.ui.tableWidget.item(idx, 0)
            if item and item.text():
                freq = self.ui.tableWidget.item(idx, 0).text()
            item = self.ui.tableWidget.item(idx, 1)
            if item and item.text():
                amp = self.ui.tableWidget.item(idx, 1).text()
            item = self.ui.tableWidget.item(idx, 2)
            if item and item.text():
                limit = self.ui.tableWidget.item(idx, 2).text()
            item = self.ui.tableWidget.item(idx, 3)
            if item and item.text():
                thdIdx = self.ui.tableWidget.item(idx, 3).text()
            if freq and amp and limit and thdIdx:
                # rowData = {'Freqs': freq, '幅值(dB)': amp, '限值': limit}
                rowData = [round(float(freq), 2), round(float(amp), 2), round(float(limit), 2), int(thdIdx)]
                thdCfgData.append(rowData)
        return thdCfgData

    def updateTHDConfigData(self, dataList):
        self.clearTHDTableData()
        self.ui.tableWidget.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)

        for rowIdx in range(len(dataList)):
            self.ui.tableWidget.insertRow(rowIdx)
            item0 = QtWidgets.QTableWidgetItem()
            item1 = QtWidgets.QTableWidgetItem()
            item2 = QtWidgets.QTableWidgetItem()
            item3 = QtWidgets.QTableWidgetItem()
            item0.setData(QtCore.Qt.ItemDataRole.DisplayRole, round(dataList[rowIdx][0], 2))
            self.ui.tableWidget.setItem(rowIdx, 0, item0)
            item1.setData(QtCore.Qt.ItemDataRole.DisplayRole, round(dataList[rowIdx][1], 2))
            self.ui.tableWidget.setItem(rowIdx, 1, item1)
            item2.setData(QtCore.Qt.ItemDataRole.DisplayRole, round(dataList[rowIdx][2], 2))
            self.ui.tableWidget.setItem(rowIdx, 2, item2)
            item3.setData(QtCore.Qt.ItemDataRole.DisplayRole, int(dataList[rowIdx][3]))
            self.ui.tableWidget.setItem(rowIdx, 3, item3)

    def addTableRow(self):
        logging.info('Add new data row.')
        curRow = self.ui.tableWidget.currentRow()
        self.ui.tableWidget.insertRow(curRow + 1)

    def deleteTableRow(self):
        logging.info('Delete one data row.')
        curRow = self.ui.tableWidget.currentRow()
        if curRow >= 0:
            self.ui.tableWidget.removeRow(curRow)

    def clearMicGradeTableData(self):
        logging.info('Clear all Mice Grade Table data.')
        rowCnt = self.ui.tableWidgetMicGrade.rowCount()
        while rowCnt > 0:
            self.ui.tableWidgetMicGrade.removeRow(0)
            rowCnt = self.ui.tableWidgetMicGrade.rowCount()

    def getMicGradeData(self):
        logging.info('Get Mic Grading Config data from table.')
        micGradeData = []
        rowCnt = self.ui.tableWidgetMicGrade.rowCount()
        for idx in range(rowCnt):
            freq = None
            ampL = None
            limitL = None
            ampR = None
            limitR = None
            grade = None
            item = self.ui.tableWidgetMicGrade.item(idx, 0)
            if item and item.text():
                freq = self.ui.tableWidgetMicGrade.item(idx, 0).text()
            item = self.ui.tableWidgetMicGrade.item(idx, 1)
            if item and item.text():
                ampL = self.ui.tableWidgetMicGrade.item(idx, 1).text()
            item = self.ui.tableWidgetMicGrade.item(idx, 2)
            if item and item.text():
                limitL = self.ui.tableWidgetMicGrade.item(idx, 2).text()
            item = self.ui.tableWidgetMicGrade.item(idx, 3)
            if item and item.text():
                ampR = self.ui.tableWidgetMicGrade.item(idx, 3).text()
            item = self.ui.tableWidgetMicGrade.item(idx, 4)
            if item and item.text():
                limitR = self.ui.tableWidgetMicGrade.item(idx, 4).text()
            item = self.ui.tableWidgetMicGrade.item(idx, 5)
            if item and item.text():
                grade = self.ui.tableWidgetMicGrade.item(idx, 5).text()

            if freq and ampL and limitL and ampR and limitR and grade:
                rowData = [round(float(freq), 2), round(float(ampL), 2), round(float(limitL), 2), round(float(ampR), 2), round(float(limitR), 2), grade]
                micGradeData.append(rowData)
        return micGradeData

    def updateMicGradeData(self, dataList):
        self.clearMicGradeTableData()
        self.ui.tableWidgetMicGrade.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)

        for rowIdx in range(len(dataList)):
            self.ui.tableWidgetMicGrade.insertRow(rowIdx)
            item0 = QtWidgets.QTableWidgetItem()
            item1 = QtWidgets.QTableWidgetItem()
            item2 = QtWidgets.QTableWidgetItem()
            item3 = QtWidgets.QTableWidgetItem()
            item4 = QtWidgets.QTableWidgetItem()
            item5 = QtWidgets.QTableWidgetItem()
            item0.setData(QtCore.Qt.ItemDataRole.DisplayRole, round(dataList[rowIdx][0], 2))
            self.ui.tableWidgetMicGrade.setItem(rowIdx, 0, item0)
            item1.setData(QtCore.Qt.ItemDataRole.DisplayRole, round(dataList[rowIdx][1], 2))
            self.ui.tableWidgetMicGrade.setItem(rowIdx, 1, item1)
            item2.setData(QtCore.Qt.ItemDataRole.DisplayRole, round(dataList[rowIdx][2], 2))
            self.ui.tableWidgetMicGrade.setItem(rowIdx, 2, item2)
            item3.setData(QtCore.Qt.ItemDataRole.DisplayRole, round(dataList[rowIdx][3], 2))
            self.ui.tableWidgetMicGrade.setItem(rowIdx, 3, item3)
            item4.setData(QtCore.Qt.ItemDataRole.DisplayRole, round(dataList[rowIdx][4], 2))
            self.ui.tableWidgetMicGrade.setItem(rowIdx, 4, item4)
            item5.setData(QtCore.Qt.ItemDataRole.DisplayRole, dataList[rowIdx][5])
            self.ui.tableWidgetMicGrade.setItem(rowIdx, 5, item5)

    def addMicGradeTableRow(self):
        logging.info('Add new Mic Grade data row.')
        curRow = self.ui.tableWidgetMicGrade.currentRow()
        self.ui.tableWidgetMicGrade.insertRow(curRow + 1)

    def deleteMicGradeTableRow(self):
        logging.info('Delete one Mic Grade data row.')
        curRow = self.ui.tableWidgetMicGrade.currentRow()
        if curRow >= 0:
            self.ui.tableWidgetMicGrade.removeRow(curRow)

    def clearTableRingData(self):
        logging.info('Clear all Ring Table data.')
        rowCnt = self.ui.tableWidgetRingConfig.rowCount()
        while rowCnt > 0:
            self.ui.tableWidgetRingConfig.removeRow(0)
            rowCnt = self.ui.tableWidgetRingConfig.rowCount()

    def updateTableRingData(self, dataList):
        self.clearTableRingData()
        self.ui.tableWidgetRingConfig.horizontalHeader().setSectionResizeMode(QtWidgets.QHeaderView.ResizeMode.Stretch)

        for rowIdx in range(len(dataList)):
            self.ui.tableWidgetRingConfig.insertRow(rowIdx)
            item0 = QtWidgets.QTableWidgetItem()
            item1 = QtWidgets.QTableWidgetItem()
            item2 = QtWidgets.QTableWidgetItem()
            item0.setData(QtCore.Qt.ItemDataRole.DisplayRole, dataList[rowIdx][0])
            self.ui.tableWidgetRingConfig.setItem(rowIdx, 0, item0)
            item1.setData(QtCore.Qt.ItemDataRole.DisplayRole, dataList[rowIdx][1])
            self.ui.tableWidgetRingConfig.setItem(rowIdx, 1, item1)
            item2.setData(QtCore.Qt.ItemDataRole.DisplayRole, dataList[rowIdx][2])
            self.ui.tableWidgetRingConfig.setItem(rowIdx, 2, item2)

    def getTableRingData(self):
        logging.info('Get Ring device Config data from table.')
        ringConfigData = []
        rowCnt = self.ui.tableWidgetRingConfig.rowCount()
        for idx in range(rowCnt):
            mic = None
            limitIdx = None
            thdIdx = None
            item = self.ui.tableWidgetRingConfig.item(idx, 0)
            if item and item.text():
                mic = self.ui.tableWidgetRingConfig.item(idx, 0).text()
            item = self.ui.tableWidgetRingConfig.item(idx, 1)
            if item and item.text():
                limitIdx = self.ui.tableWidgetRingConfig.item(idx, 1).text()
            item = self.ui.tableWidgetRingConfig.item(idx, 2)
            if item and item.text():
                thdIdx = self.ui.tableWidgetRingConfig.item(idx, 2).text()
            if mic and limitIdx and thdIdx:
                rowData = [mic, limitIdx, thdIdx]
                ringConfigData.append(rowData)
        return ringConfigData

    def scanComPort(self):
        # logging.info('Scan all COM port.')
        ports_list = list(serial.tools.list_ports.comports())
        if len(ports_list) <= 0:
            logging.error('COM Port not found!')
            return []
        else:
            portNameList = []
            for comport in ports_list:
                portNameList.append(list(comport)[0])
                # print(str(list(comport)))
        return portNameList

    def updateComDevList(self):
        self.comPortList = self.scanComPort()
        self.ui.comboComportBox.clear()
        for port in self.comPortList:
            self.ui.comboComportBox.addItem(port)
        self.ui.comboComportBox.setCurrentIndex(0)

    def createComDevRightMenu(self):
        self.comDevMenu.popup(QtGui.QCursor.pos(), at=None)

    def onCheckPlayDevice(self):
        # logging.info('Check Play Device.')
        if self.ui.checkBoxPlayDevice.isChecked():
            self.ui.leditPlayDeviceName.setEnabled(True)
        else:
            self.ui.leditPlayDeviceName.setEnabled(False)

    def onCheckRecordDevice(self):
        # logging.info('Check Record Device.')
        if self.ui.checkBoxRecordDevice.isChecked():
            self.ui.leditRecordDeviceName.setEnabled(True)
        else:
            self.ui.leditRecordDeviceName.setEnabled(False)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    app = QtWidgets.QApplication(sys.argv)
    advanceWin = AdvancedSetting()
    advanceWin.show()
    sys.exit(app.exec())

