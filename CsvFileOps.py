import os
import logging
import csv
import base64
from BaseSubMainWindow import BaseResource


class CsvFileOps:

    @staticmethod
    def saveDataToCsvFile(recordList, header, csvFile):
        logging.debug('Save Data to csv file: %s', csvFile)
        try:
            with open(csvFile, 'w+', newline='') as csvf:
                writer = csv.writer(csvf)
                if csvf.tell() == 0:
                    writer.writerow(header)
                writer.writerows(recordList)
                return True
        except Exception as e:
            logging.exception(e)
            return False

    @staticmethod
    def getDataFromCsvFile(csvFile):
        logging.debug('Get Records from csv file: %s', csvFile)
        try:
            with open(csvFile, 'r') as csvf:
                reader = csv.reader(csvf)
                records = list()
                for row in reader:
                    records.append(row)
                return records
        except Exception as e:
            logging.exception(e)


class RecordCsvFileOps:

    @staticmethod
    def byte_xor(byte):
        return bytes([_a ^ 0xff for _a in byte])

    @staticmethod
    def appendEncryptoRecord(record, csvFile, header=None):
        logging.debug('Append Record to csv file: %s', csvFile)
        if not header:
            header = ['pcba_sn', 'usb_connect', 'fw_check', 'led_test', 'udisk_test', 'codec_test', 'total_result', 'date']
        try:
            with open(csvFile, 'a+', newline='') as csvf:
                writer = csv.DictWriter(csvf, header)
                if csvf.tell() == 0:
                    writer.writeheader()
                for key, value in record.items():
                    encVal = bytes(record[key], encoding="utf8")
                    encVal = RecordCsvFileOps.byte_xor(encVal)
                    record[key] = str(base64.urlsafe_b64encode(encVal), encoding="utf8")
                writer.writerow(record)
                return True
        except Exception as e:
            logging.exception(e)
            return False

    @staticmethod
    def getDecryptoRecords(csvFile):
        logging.debug('Get Records from csv file: %s', csvFile)
        try:
            with (open(csvFile, 'r') as csvf):
                reader = csv.DictReader(csvf)
                records = list()
                for record in reader:
                    for key, value in record.items():
                        val = base64.urlsafe_b64decode(record[key])
                        val = RecordCsvFileOps.byte_xor(val)
                        record[key] = str(val, encoding="utf-8")
                    records.append(record)
                return records
        except Exception as e:
            logging.exception(e)

    @staticmethod
    def appendRecords(recordList, csvFile, header=None):
        logging.debug('Append Record to csv file: %s', csvFile)
        if not header:
            header = ['pcba_sn', 'usb_connect', 'fw_check', 'led_test', 'udisk_test', 'codec_test', 'total_result',
                      'date']
        try:
            with open(csvFile, 'a+', newline='') as csvf:
                writer = csv.DictWriter(csvf, header)
                if csvf.tell() == 0:
                    writer.writeheader()
                for record in recordList:
                    writer.writerow(record)
                return True
        except Exception as e:
            logging.exception(e)
            return False

    @staticmethod
    def clearRecords(csvFile, header=None):
        logging.debug('Clear Records of csv file: %s', csvFile)
        if not header:
            header = ['pcba_sn', 'usb_connect', 'fw_check', 'led_test', 'udisk_test', 'codec_test', 'total_result', 'date']
        try:
            with open(csvFile, 'w', newline='') as csvf:
                writer = csv.DictWriter(csvf, header)
                writer.writeheader()
                return True
        except Exception as e:
            logging.exception(e)
            return False

    @staticmethod
    def getRecords(csvFile):
        logging.debug('Get Records from csv file: %s', csvFile)
        try:
            with open(csvFile, 'r') as csvf:
                reader = csv.DictReader(csvf)
                records = list()
                for row in reader:
                    records.append(row)
                return records
        except Exception as e:
            logging.exception(e)

    @staticmethod
    def getRecordFileList(suffix='.csv'):
        logging.debug('Get Records file list')
        fullFile = BaseResource.getConfigFileFullName('Record_', folder='Records')
        path = os.path.dirname(fullFile)
        fileList = []
        for file in os.listdir(path):
            if file.endswith(suffix):
                fileList.append(os.path.join(path, file))
        return fileList


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    file = r'C:\Users\<USER>\AppData\Local\Ls900TestTool\Records\Records_20240319.csv'
    records = RecordCsvFileOps.getDecryptoRecords(file)
    print(records)