# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'LoginDialog.ui'
##
## Created by: Qt User Interface Compiler version 6.6.2
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON>rush, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QDialog, QHBoxLayout, QLabel,
    QLayout, QLineEdit, QPushButton, QSizePolicy,
    QSpacerItem, QTabWidget, QVBoxLayout, QWidget)

class Ui_UserLoginDialog(object):
    def setupUi(self, UserLoginDialog):
        if not UserLoginDialog.objectName():
            UserLoginDialog.setObjectName(u"UserLoginDialog")
        UserLoginDialog.resize(365, 197)
        font = QFont()
        font.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        UserLoginDialog.setFont(font)
        UserLoginDialog.setModal(True)
        self.horizontalLayout_6 = QHBoxLayout(UserLoginDialog)
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.tabWidget = QTabWidget(UserLoginDialog)
        self.tabWidget.setObjectName(u"tabWidget")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.tabWidget.sizePolicy().hasHeightForWidth())
        self.tabWidget.setSizePolicy(sizePolicy)
        font1 = QFont()
        font1.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        font1.setPointSize(11)
        self.tabWidget.setFont(font1)
        self.tabWidget.setStyleSheet(u"")
        self.tabLogin = QWidget()
        self.tabLogin.setObjectName(u"tabLogin")
        self.verticalLayout = QVBoxLayout(self.tabLogin)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.label = QLabel(self.tabLogin)
        self.label.setObjectName(u"label")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.label.sizePolicy().hasHeightForWidth())
        self.label.setSizePolicy(sizePolicy1)
        self.label.setMinimumSize(QSize(80, 0))
        font2 = QFont()
        font2.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        font2.setPointSize(10)
        font2.setBold(False)
        font2.setUnderline(False)
        self.label.setFont(font2)

        self.horizontalLayout.addWidget(self.label)

        self.lineEditUserName = QLineEdit(self.tabLogin)
        self.lineEditUserName.setObjectName(u"lineEditUserName")
        self.lineEditUserName.setFont(font)
        self.lineEditUserName.setStyleSheet(u"QLineEdit {\n"
"padding: 1px;\n"
"border-style: solid;\n"
"border: 1px solid gray;\n"
"border-radius: 4px;\n"
"}")

        self.horizontalLayout.addWidget(self.lineEditUserName)


        self.verticalLayout.addLayout(self.horizontalLayout)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label_2 = QLabel(self.tabLogin)
        self.label_2.setObjectName(u"label_2")
        sizePolicy1.setHeightForWidth(self.label_2.sizePolicy().hasHeightForWidth())
        self.label_2.setSizePolicy(sizePolicy1)
        self.label_2.setMinimumSize(QSize(80, 0))
        self.label_2.setFont(font2)

        self.horizontalLayout_2.addWidget(self.label_2)

        self.lineEditPassword = QLineEdit(self.tabLogin)
        self.lineEditPassword.setObjectName(u"lineEditPassword")
        self.lineEditPassword.setFont(font)
        self.lineEditPassword.setStyleSheet(u"QLineEdit {\n"
"padding: 1px;\n"
"border-style: solid;\n"
"border: 1px solid gray;\n"
"border-radius: 4px;\n"
"}")
        self.lineEditPassword.setEchoMode(QLineEdit.PasswordEchoOnEdit)

        self.horizontalLayout_2.addWidget(self.lineEditPassword)


        self.verticalLayout.addLayout(self.horizontalLayout_2)

        self.verticalSpacer = QSpacerItem(20, 30, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)

        self.verticalLayout.addItem(self.verticalSpacer)

        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.btnLogin = QPushButton(self.tabLogin)
        self.btnLogin.setObjectName(u"btnLogin")
        font3 = QFont()
        font3.setFamilies([u"\u5fae\u8f6f\u96c5\u9ed1"])
        font3.setPointSize(10)
        self.btnLogin.setFont(font3)
        self.btnLogin.setStyleSheet(u"")

        self.horizontalLayout_5.addWidget(self.btnLogin)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer)

        self.btnClose = QPushButton(self.tabLogin)
        self.btnClose.setObjectName(u"btnClose")
        self.btnClose.setFont(font3)

        self.horizontalLayout_5.addWidget(self.btnClose)


        self.verticalLayout.addLayout(self.horizontalLayout_5)

        self.tabWidget.addTab(self.tabLogin, "")
        self.tabServerConfig = QWidget()
        self.tabServerConfig.setObjectName(u"tabServerConfig")
        self.verticalLayout_2 = QVBoxLayout(self.tabServerConfig)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setSizeConstraint(QLayout.SetDefaultConstraint)
        self.label_3 = QLabel(self.tabServerConfig)
        self.label_3.setObjectName(u"label_3")
        sizePolicy1.setHeightForWidth(self.label_3.sizePolicy().hasHeightForWidth())
        self.label_3.setSizePolicy(sizePolicy1)
        self.label_3.setMinimumSize(QSize(80, 0))
        self.label_3.setFont(font3)

        self.horizontalLayout_3.addWidget(self.label_3)

        self.lineEditServer = QLineEdit(self.tabServerConfig)
        self.lineEditServer.setObjectName(u"lineEditServer")
        self.lineEditServer.setMinimumSize(QSize(0, 23))
        self.lineEditServer.setStyleSheet(u"QLineEdit {\n"
"padding: 1px;\n"
"border-style: solid;\n"
"border: 1px solid gray;\n"
"border-radius: 4px;\n"
"}")

        self.horizontalLayout_3.addWidget(self.lineEditServer)


        self.verticalLayout_2.addLayout(self.horizontalLayout_3)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.label_4 = QLabel(self.tabServerConfig)
        self.label_4.setObjectName(u"label_4")
        sizePolicy1.setHeightForWidth(self.label_4.sizePolicy().hasHeightForWidth())
        self.label_4.setSizePolicy(sizePolicy1)
        self.label_4.setMinimumSize(QSize(80, 0))
        self.label_4.setFont(font3)

        self.horizontalLayout_4.addWidget(self.label_4)

        self.lineEditPort = QLineEdit(self.tabServerConfig)
        self.lineEditPort.setObjectName(u"lineEditPort")
        self.lineEditPort.setMinimumSize(QSize(0, 23))
        self.lineEditPort.setStyleSheet(u"QLineEdit {\n"
"padding: 1px;\n"
"border-style: solid;\n"
"border: 1px solid gray;\n"
"border-radius: 4px;\n"
"}")

        self.horizontalLayout_4.addWidget(self.lineEditPort)


        self.verticalLayout_2.addLayout(self.horizontalLayout_4)

        self.verticalSpacer_2 = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_2.addItem(self.verticalSpacer_2)

        self.tabWidget.addTab(self.tabServerConfig, "")

        self.horizontalLayout_6.addWidget(self.tabWidget)


        self.retranslateUi(UserLoginDialog)

        self.tabWidget.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(UserLoginDialog)
    # setupUi

    def retranslateUi(self, UserLoginDialog):
        UserLoginDialog.setWindowTitle(QCoreApplication.translate("UserLoginDialog", u"\u7528\u6237\u767b\u5f55", None))
        self.label.setText(QCoreApplication.translate("UserLoginDialog", u"User Name:", None))
        self.label_2.setText(QCoreApplication.translate("UserLoginDialog", u"Password:", None))
        self.btnLogin.setText(QCoreApplication.translate("UserLoginDialog", u"Login", None))
        self.btnClose.setText(QCoreApplication.translate("UserLoginDialog", u"Close", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabLogin), QCoreApplication.translate("UserLoginDialog", u"Login", None))
        self.label_3.setText(QCoreApplication.translate("UserLoginDialog", u"Server:", None))
        self.label_4.setText(QCoreApplication.translate("UserLoginDialog", u"Port:", None))
        self.lineEditPort.setText(QCoreApplication.translate("UserLoginDialog", u"80", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tabServerConfig), QCoreApplication.translate("UserLoginDialog", u"Server", None))
    # retranslateUi

