<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>DialogSetting</class>
 <widget class="QDialog" name="DialogSetting">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>578</width>
    <height>795</height>
   </rect>
  </property>
  <property name="font">
   <font>
    <family>微软雅黑</family>
   </font>
  </property>
  <property name="windowTitle">
   <string>高级设置</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QGroupBox" name="gpTestBoxConfig">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>400</width>
       <height>0</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="title">
      <string>测试箱参数</string>
     </property>
     <property name="checkable">
      <bool>false</bool>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_10">
      <item>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_11">
          <item>
           <widget class="QLabel" name="label_7">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string>控制串口：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboComportBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="currentIndex">
             <number>-1</number>
            </property>
            <property name="maxVisibleItems">
             <number>16</number>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="0" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_13">
          <item>
           <widget class="QLabel" name="label_9">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string>波特率：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBaudrateBox">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="currentIndex">
             <number>0</number>
            </property>
            <property name="maxVisibleItems">
             <number>16</number>
            </property>
            <item>
             <property name="text">
              <string>9600</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <widget class="QCheckBox" name="checkBoxAutoTest">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>关箱自动测试</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="gpCodecFreqParams">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>400</width>
       <height>0</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="title">
      <string>频响测试参数</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <item>
       <layout class="QGridLayout" name="gridLayout">
        <item row="3" column="0">
         <widget class="QPushButton" name="btnFreqLimitParams">
          <property name="text">
           <string>频响测试阈值配置</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout">
          <item>
           <widget class="QLabel" name="label">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string>FFT Size：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBoxFFTSize">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="currentIndex">
             <number>7</number>
            </property>
            <property name="maxVisibleItems">
             <number>16</number>
            </property>
            <item>
             <property name="text">
              <string>128</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>256</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>512</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>1024</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>2048</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>4096</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>8192</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>16384</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>32768</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>65536</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
        <item row="0" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_8">
          <item>
           <widget class="QLabel" name="label_5">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string>采样频率：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBoxSamplerate">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="currentIndex">
             <number>0</number>
            </property>
            <property name="maxVisibleItems">
             <number>16</number>
            </property>
            <item>
             <property name="text">
              <string>48000</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
        <item row="2" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_9">
          <item>
           <widget class="QCheckBox" name="checkBoxPlayDevice">
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="text">
             <string>播放设备</string>
            </property>
            <property name="checked">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="leditPlayDeviceName">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="readOnly">
             <bool>false</bool>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="2" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_12">
          <item>
           <widget class="QCheckBox" name="checkBoxRecordDevice">
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LeftToRight</enum>
            </property>
            <property name="text">
             <string>录音设备</string>
            </property>
            <property name="checked">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="leditRecordDeviceName">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="readOnly">
             <bool>false</bool>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="5" column="1">
         <widget class="QCheckBox" name="checkBoxSaveAudio">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>保存录音文件</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QCheckBox" name="checkBoxUpdateSndFirst">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>测试前更新声卡设备</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <item>
           <widget class="QLabel" name="label_3">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string>噪声类型：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="comboBoxAudioType">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="currentIndex">
             <number>1</number>
            </property>
            <property name="maxVisibleItems">
             <number>16</number>
            </property>
            <item>
             <property name="text">
              <string>白噪声</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>20~20K扫频</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
        <item row="1" column="0">
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QLabel" name="label_2">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>80</width>
              <height>0</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="text">
             <string>采样时长(s)：</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QDoubleSpinBox" name="spinBoxTimeLen">
            <property name="font">
             <font>
              <family>微软雅黑</family>
              <pointsize>10</pointsize>
             </font>
            </property>
            <property name="decimals">
             <number>1</number>
            </property>
            <property name="minimum">
             <double>1.000000000000000</double>
            </property>
            <property name="maximum">
             <double>20.000000000000000</double>
            </property>
            <property name="singleStep">
             <double>0.100000000000000</double>
            </property>
            <property name="value">
             <double>1.000000000000000</double>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item row="4" column="0">
         <widget class="QCheckBox" name="checkBoxShowLimitConfig">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>显示阈值标定</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QCheckBox" name="checkBoxERBBand">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>Enable ERBBand</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item row="5" column="0">
         <widget class="QCheckBox" name="checkBoxSaveFFT">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>保存频响文件</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="gpTHDTestConfig">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="title">
      <string>THD+N测试参数</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_6">
        <item>
         <widget class="QPushButton" name="btnAdd">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="text">
           <string>+</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnDelete">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="text">
           <string>-</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="btnClear">
          <property name="maximumSize">
           <size>
            <width>50</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="text">
           <string>清空</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="tableWidget">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="sortingEnabled">
         <bool>true</bool>
        </property>
        <property name="rowCount">
         <number>0</number>
        </property>
        <attribute name="horizontalHeaderVisible">
         <bool>true</bool>
        </attribute>
        <attribute name="horizontalHeaderCascadingSectionResizes">
         <bool>true</bool>
        </attribute>
        <attribute name="horizontalHeaderMinimumSectionSize">
         <number>25</number>
        </attribute>
        <attribute name="horizontalHeaderDefaultSectionSize">
         <number>130</number>
        </attribute>
        <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
         <bool>true</bool>
        </attribute>
        <attribute name="verticalHeaderVisible">
         <bool>false</bool>
        </attribute>
        <attribute name="verticalHeaderCascadingSectionResizes">
         <bool>false</bool>
        </attribute>
        <attribute name="verticalHeaderShowSortIndicator" stdset="0">
         <bool>false</bool>
        </attribute>
        <column>
         <property name="text">
          <string>频点(Hz)</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>采样时长(s)</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>限值(dB)</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>参数序号</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="gpMicGradeConfig">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="title">
      <string>MIC分档参数</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_7">
        <item>
         <widget class="QPushButton" name="btnMicGradeAdd">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="text">
           <string>+</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnMicGradeDelete">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="maximumSize">
           <size>
            <width>40</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="text">
           <string>-</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="btnMicGradeClear">
          <property name="maximumSize">
           <size>
            <width>50</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="text">
           <string>清空</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="tableWidgetMicGrade">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="sortingEnabled">
         <bool>true</bool>
        </property>
        <property name="rowCount">
         <number>0</number>
        </property>
        <attribute name="horizontalHeaderVisible">
         <bool>true</bool>
        </attribute>
        <attribute name="horizontalHeaderCascadingSectionResizes">
         <bool>true</bool>
        </attribute>
        <attribute name="horizontalHeaderMinimumSectionSize">
         <number>25</number>
        </attribute>
        <attribute name="horizontalHeaderDefaultSectionSize">
         <number>130</number>
        </attribute>
        <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
         <bool>true</bool>
        </attribute>
        <attribute name="verticalHeaderVisible">
         <bool>false</bool>
        </attribute>
        <attribute name="verticalHeaderCascadingSectionResizes">
         <bool>false</bool>
        </attribute>
        <attribute name="verticalHeaderShowSortIndicator" stdset="0">
         <bool>false</bool>
        </attribute>
        <column>
         <property name="text">
          <string>频点(Hz)</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>幅值L(dB)</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>限值L</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>幅值R(dB)</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>限值R</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>等级</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="gpDevRingConfig">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>400</width>
       <height>0</height>
      </size>
     </property>
     <property name="font">
      <font>
       <family>微软雅黑</family>
       <pointsize>10</pointsize>
      </font>
     </property>
     <property name="title">
      <string>圆盘麦测试</string>
     </property>
     <property name="checkable">
      <bool>true</bool>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="QCheckBox" name="checkBoxRingFreqTestEnable">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>频响测试</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxRingThdTestEnable">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>THDN测试</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QCheckBox" name="checkBoxRingFailStop">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>10</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>失败立即停止</string>
          </property>
          <property name="checked">
           <bool>false</bool>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QTableWidget" name="tableWidgetRingConfig">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>10</pointsize>
         </font>
        </property>
        <property name="sortingEnabled">
         <bool>true</bool>
        </property>
        <property name="rowCount">
         <number>0</number>
        </property>
        <attribute name="horizontalHeaderVisible">
         <bool>true</bool>
        </attribute>
        <attribute name="horizontalHeaderCascadingSectionResizes">
         <bool>true</bool>
        </attribute>
        <attribute name="horizontalHeaderMinimumSectionSize">
         <number>25</number>
        </attribute>
        <attribute name="horizontalHeaderDefaultSectionSize">
         <number>130</number>
        </attribute>
        <attribute name="horizontalHeaderShowSortIndicator" stdset="0">
         <bool>true</bool>
        </attribute>
        <attribute name="verticalHeaderVisible">
         <bool>false</bool>
        </attribute>
        <attribute name="verticalHeaderCascadingSectionResizes">
         <bool>false</bool>
        </attribute>
        <attribute name="verticalHeaderShowSortIndicator" stdset="0">
         <bool>false</bool>
        </attribute>
        <column>
         <property name="text">
          <string>MIC序号</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>频响阈值序号</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
        <column>
         <property name="text">
          <string>THD参数序号</string>
         </property>
         <property name="font">
          <font>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </column>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
